import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

// Width percentage to DP
export const widthPercentageToDP = (widthPercent) => {
  const elemWidth = typeof widthPercent === 'number' ? widthPercent : parseFloat(widthPercent);
  return (width * elemWidth) / 100;
};

// Height percentage to DP
export const heightPercentageToDP = (heightPercent) => {
  const elemHeight = typeof heightPercent === 'number' ? heightPercent : parseFloat(heightPercent);
  return (height * elemHeight) / 100;
};

// Aliases for shorter usage
export const wp = widthPercentageToDP;
export const hp = heightPercentageToDP;
