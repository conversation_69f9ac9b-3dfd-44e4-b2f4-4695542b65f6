import React, { useRef, useEffect, useState } from 'react';
import { View, StyleSheet, SafeAreaView, StatusBar, Dimensions, Animated, TouchableOpacity, Text, Platform } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AIWorkoutGenerator from '../components/AIWorkoutGenerator.redesigned';
import { useUser } from '../context/UserContext';

const { width, height } = Dimensions.get('window');

const AdvancedParticle = ({ style }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const speed = 4000 + Math.random() * 2000;
    const delay = Math.random() * 3000;

    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, { toValue: 1, duration: speed, useNativeDriver: true }),
        Animated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = Animated.sequence([
      Animated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 1, duration: speed - 1000, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);

    setTimeout(() => {
      animation.start();
      Animated.loop(opacitySequence).start();
    }, delay);

  }, []);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <Animated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

export default function AIWorkoutGeneratorScreen() {
  const router = useRouter();
  const { currentUser } = useUser();
  const [workoutGenerated, setWorkoutGenerated] = useState(false);
  
  const handleWorkoutGenerated = async (workout) => {
    setWorkoutGenerated(true);
    // Save the generated workout to AsyncStorage
    if (currentUser) {
      try {
        const workoutData = {
          ...workout,
          generatedAt: new Date().toISOString(),
          userId: currentUser.uid || currentUser.email
        };
        await AsyncStorage.setItem('@last_generated_workout', JSON.stringify(workoutData));
      } catch (error) {
        console.error('Error saving workout:', error);
      }
    }
  };
  
  return (
    <View style={styles.container}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      {[...Array(20)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      <View style={[styles.glowCircle, { top: '10%', right: -50 }]} />
      <View style={[styles.glowCircle, { bottom: '15%', left: -50 }]} />
      
      <StatusBar barStyle="light-content" />
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>AI Workout Generator</Text>
          <View style={styles.headerRight} />
        </View>
        
        <AIWorkoutGenerator 
          onClose={() => router.back()}
          onWorkoutGenerated={handleWorkoutGenerated}
          userProfile={currentUser}
        />
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight + 10 : 10,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerRight: {
    width: 40,
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 5,
    zIndex: 0,
  },
  glowCircle: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    zIndex: 0,
  },
});