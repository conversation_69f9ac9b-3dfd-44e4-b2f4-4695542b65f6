import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  FlatList,
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';

import MediaRenderer from './MediaRenderer';
import { useThemeColor } from '../hooks/useThemeColor';

const { width: screenWidth } = Dimensions.get('window');

interface SliderItem {
  id: string;
  type: 'image' | 'video' | 'youtube';
  source: string;
  title?: string;
  description?: string;
  fallbackSource?: string;
}

interface ImageSliderProps {
  data: SliderItem[];
  height?: number;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showPagination?: boolean;
  showTitles?: boolean;
  onItemPress?: (item: SliderItem, index: number) => void;
}

const ImageSlider: React.FC<ImageSliderProps> = ({
  data,
  height = 200,
  autoPlay = false,
  autoPlayInterval = 3000,
  showPagination = true,
  showTitles = false,
  onItemPress,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const tintColor = useThemeColor({}, 'tint');

  useEffect(() => {
    if (autoPlay && data.length > 1) {
      intervalRef.current = setInterval(() => {
        setCurrentIndex((prevIndex) => {
          const nextIndex = (prevIndex + 1) % data.length;
          flatListRef.current?.scrollToIndex({
            index: nextIndex,
            animated: true,
          });
          return nextIndex;
        });
      }, autoPlayInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoPlay, autoPlayInterval, data.length]);

  const handleScroll = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset;
    const viewSize = event.nativeEvent.layoutMeasurement;
    const pageNum = Math.floor(contentOffset.x / viewSize.width);
    setCurrentIndex(pageNum);
  };

  const renderItem = ({ item, index }: { item: SliderItem; index: number }) => (
    <TouchableOpacity
      style={[styles.slide, { width: screenWidth - 32 }]}
      onPress={() => onItemPress?.(item, index)}
      activeOpacity={0.8}
    >
      <MediaRenderer
        type={item.type}
        source={item.source}
        fallbackSource={item.fallbackSource}
        style={{...styles.media, height}}
        resizeMode="cover"
      />
      {showTitles && (item.title || item.description) && (
        <View style={[styles.textOverlay, { backgroundColor: `${backgroundColor}CC` }]}>
          {item.title && (
            <Text style={[styles.title, { color: textColor }]} numberOfLines={1}>
              {item.title}
            </Text>
          )}
          {item.description && (
            <Text style={[styles.description, { color: textColor }]} numberOfLines={2}>
              {item.description}
            </Text>
          )}
        </View>
      )}
    </TouchableOpacity>
  );

  const renderPagination = () => (
    <View style={styles.paginationContainer}>
      {data.map((_, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.paginationDot,
            {
              backgroundColor: index === currentIndex ? tintColor : `${textColor}30`,
            },
          ]}
          onPress={() => {
            flatListRef.current?.scrollToIndex({ index, animated: true });
            setCurrentIndex(index);
          }}
        />
      ))}
    </View>
  );

  if (!data || data.length === 0) {
    return (
      <View style={[styles.emptyContainer, { height, backgroundColor }]}>
        <Text style={[styles.emptyText, { color: textColor }]}>
          No media to display
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={data}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.flatListContent}
        onScrollToIndexFailed={(info) => {
          console.warn('ScrollToIndex failed:', info);
        }}
      />
      {showPagination && data.length > 1 && renderPagination()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
  },
  flatListContent: {
    paddingHorizontal: 16,
  },
  slide: {
    marginRight: 16,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  media: {
    width: '100%',
    borderRadius: 12,
  },
  textOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 12,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    opacity: 0.8,
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    marginHorizontal: 16,
  },
  emptyText: {
    fontSize: 16,
    opacity: 0.6,
  },
});

export default ImageSlider;