<<<<<<<
<<<<<<<



import React, { useState, useCallback } from 'react';















import { View, StyleSheet, ActivityIndicator, Text, Dimensions } from 'react-native';















import FastImage from 'react-native-fast-image';















import { Video, ResizeMode } from 'expo-av';















import YoutubePlayer from 'react-native-youtube-iframe';















import { useThemeColor } from '@/hooks/useThemeColor';































const { width: screenWidth } = Dimensions.get('window');































interface MediaRendererProps {















  type: 'image' | 'video' | 'youtube';















  source: string;















  style?: any;















  fallbackSource?: string;















  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';















  autoplay?: boolean;















  controls?: boolean;















  onLoad?: () => void;















  onError?: (error: any) => void;















}































const MediaRenderer: React.FC<MediaRendererProps> = ({















  type,















  source,















  style,















  fallbackSource,















  resizeMode = 'cover',















  autoplay = false,















  controls = true,















  onLoad,















  onError,















  ...props















}) => {















  const [loading, setLoading] = useState(true);















  const [error, setError] = useState(false);















  const [playing, setPlaying] = useState(autoplay);































  const backgroundColor = useThemeColor({}, 'background');















  const textColor = useThemeColor({}, 'text');































  const handleLoad = useCallback(() => {















    setLoading(false);















    onLoad?.();















  }, [onLoad]);































  const handleError = useCallback((err: any) => {















    console.log(`MediaRenderer Error (${type}):`, err);















    setError(true);















    setLoading(false);















    onError?.(err);















  }, [type, onError]);































  const extractYoutubeId = (url: string): string | null => {















    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;















    const match = url.match(regExp);















    return match && match[2].length === 11 ? match[2] : null;















  };































  const renderFallback = () => (















    <View style={[style, styles.fallbackContainer, { backgroundColor }]}>















      {fallbackSource ? (















        <FastImage















          style={[style, styles.fallbackImage]}















          source={{ uri: fallbackSource }}















          resizeMode={FastImage.resizeMode[resizeMode]}















          onLoad={handleLoad}















          onError={() => setError(true)}















        />















      ) : (















        <View style={styles.errorContainer}>















          <Text style={[styles.errorText, { color: textColor }]}>















            Failed to load {type}















          </Text>















        </View>















      )}















    </View>















  );































  const renderContent = () => {















    if (error) {















      return renderFallback();















    }































    switch (type) {















      case 'youtube':















        const videoId = extractYoutubeId(source);















        if (!videoId) {















          console.warn('Invalid YouTube URL:', source);















          return renderFallback();















        }















        















        return (















          <View style={[style, styles.youtubeContainer]}>















            <YoutubePlayer















              height={style?.height || 200}















              width={style?.width || screenWidth - 32}















              videoId={videoId}















              play={playing}















              onReady={handleLoad}















              onError={handleError}















              initialPlayerParams={{















                controls: controls,















                rel: false,















                showinfo: false,















                modestbranding: true,















              }}















            />















            {loading && (















              <View style={styles.loadingOverlay}>















                <ActivityIndicator size="large" color="#FF0000" />















              </View>















            )}















          </View>















        );































      case 'video':















        return (















          <View style={[style, styles.videoContainer]}>















            <Video















              style={[style, styles.video]}















              source={{ uri: source }}















              useNativeControls={controls}















              resizeMode={ResizeMode.CONTAIN}















              isLooping={false}















              shouldPlay={autoplay}















              onLoad={handleLoad}















              onError={handleError}















              onPlaybackStatusUpdate={(status) => {















                if (status.isLoaded) {















                  setPlaying(status.shouldPlay || false);















                }















              }}















            />















            {loading && (















              <View style={styles.loadingOverlay}>















                <ActivityIndicator size="large" color="#007AFF" />















              </View>















            )}















          </View>















        );































      case 'image':















      default:















        return (















          <View style={[style, styles.imageContainer]}>















            <FastImage















              style={[style, styles.image]}















              source={typeof source === 'string' ? { uri: source } : source}















              resizeMode={FastImage.resizeMode[resizeMode]}















              onLoad={handleLoad}















              onError={handleError}















              {...props}















            />















            {loading && (















              <View style={styles.loadingOverlay}>















                <ActivityIndicator size="large" color="#007AFF" />















              </View>















            )}















          </View>















        );















    }















  };































  return (















    <View style={[style, styles.container, { backgroundColor }]}>















      {renderContent()}















    </View>















  );















};































const styles = StyleSheet.create({















  container: {















    overflow: 'hidden',















    justifyContent: 'center',















    alignItems: 'center',















  },















  imageContainer: {















    position: 'relative',















  },















  image: {















    width: '100%',















    height: '100%',















  },















  videoContainer: {















    position: 'relative',















  },















  video: {















    width: '100%',















    height: '100%',















  },















  youtubeContainer: {















    position: 'relative',















  },















  fallbackContainer: {















    justifyContent: 'center',















    alignItems: 'center',















  },















  fallbackImage: {















    width: '100%',















    height: '100%',















  },















  errorContainer: {















    justifyContent: 'center',















    alignItems: 'center',















    padding: 20,















  },















  errorText: {















    fontSize: 14,















    textAlign: 'center',















  },















  loadingOverlay: {















    position: 'absolute',















    top: 0,















    left: 0,















    right: 0,















    bottom: 0,















    justifyContent: 'center',















    alignItems: 'center',















    backgroundColor: 'rgba(0, 0, 0, 0.1)',















  },















});































export default MediaRenderer;















=======



import React, { useState, useCallback } from 'react';







import { View, StyleSheet, ActivityIndicator, Text, Dimensions } from 'react-native';







import FastImage from 'react-native-fast-image';







import { Video, ResizeMode } from 'expo-av';







import YoutubePlayer from 'react-native-youtube-iframe';







import { useThemeColor } from '@/hooks/useThemeColor';















const { width: screenWidth } = Dimensions.get('window');















interface MediaRendererProps {







  type: 'image' | 'video' | 'youtube';







  source: string;







  style?: any;







  fallbackSource?: string;







  resizeMode?: 'cover' | 'contain' | 'stretch' | 'center';







  autoplay?: boolean;







  controls?: boolean;







  onLoad?: () => void;







  onError?: (error: any) => void;







}















const MediaRenderer: React.FC<MediaRendererProps> = ({







  type,







  source,







  style,







  fallbackSource,







  resizeMode = 'cover',







  autoplay = false,







  controls = true,







  onLoad,







  onError,







  ...props







}) => {







  const [loading, setLoading] = useState(true);







  const [error, setError] = useState(false);







  const [playing, setPlaying] = useState(autoplay);















  const backgroundColor = useThemeColor({}, 'background');







  const textColor = useThemeColor({}, 'text');















  const handleLoad = useCallback(() => {







    setLoading(false);







    onLoad?.();







  }, [onLoad]);















  const handleError = useCallback((err: any) => {







    console.log(`MediaRenderer Error (${type}):`, err);







    setError(true);







    setLoading(false);







    onError?.(err);







  }, [type, onError]);















  const extractYoutubeId = (url: string): string | null => {







    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;







    const match = url.match(regExp);







    return match && match[2].length === 11 ? match[2] : null;







  };















  const renderFallback = () => (







    <View style={[style, styles.fallbackContainer, { backgroundColor }]}>







      {fallbackSource ? (







        <FastImage







          style={[style, styles.fallbackImage]}







          source={{ uri: fallbackSource }}







          resizeMode={FastImage.resizeMode[resizeMode]}







          onLoad={handleLoad}







          onError={() => setError(true)}







        />







      ) : (







        <View style={styles.errorContainer}>







          <Text style={[styles.errorText, { color: textColor }]}>







            Failed to load {type}







          </Text>







        </View>







      )}







    </View>







  );















  const renderContent = () => {







    if (error) {







      return renderFallback();







    }















    switch (type) {







      case 'youtube':







        const videoId = extractYoutubeId(source);







        if (!videoId) {







          console.warn('Invalid YouTube URL:', source);







          return renderFallback();







        }







        







        return (







          <View style={[style, styles.youtubeContainer]}>







            <YoutubePlayer







              height={style?.height || 200}







              width={style?.width || screenWidth - 32}







              videoId={videoId}







              play={playing}







              onReady={handleLoad}







              onError={handleError}







              initialPlayerParams={{







                controls: controls,







                rel: false,







              }}







            />







            {loading && (







              <View style={styles.loadingOverlay}>







                <ActivityIndicator size="large" color="#FF0000" />







              </View>







            )}







          </View>







        );















      case 'video':







        return (







          <View style={[style, styles.videoContainer]}>







            <Video







              style={[style, styles.video]}







              source={{ uri: source }}







              useNativeControls={controls}







              resizeMode={ResizeMode.CONTAIN}







              isLooping={false}







              shouldPlay={autoplay}







              onLoad={handleLoad}







              onError={handleError}







              onPlaybackStatusUpdate={(status) => {







                if (status.isLoaded) {







                  setPlaying(status.shouldPlay || false);







                }







              }}







            />







            {loading && (







              <View style={styles.loadingOverlay}>







                <ActivityIndicator size="large" color="#007AFF" />







              </View>







            )}







          </View>







        );















      case 'image':







      default:







        return (







          <View style={[style, styles.imageContainer]}>







            <FastImage







              style={[style, styles.image]}







              source={typeof source === 'string' ? { uri: source } : source}







              resizeMode={FastImage.resizeMode[resizeMode]}







              onLoad={handleLoad}







              onError={() => handleError('FastImage load error')}







              {...props}







            />







            {loading && (







              <View style={styles.loadingOverlay}>







                <ActivityIndicator size="large" color="#007AFF" />







              </View>







            )}







          </View>







        );







    }







  };















  return (







    <View style={[style, styles.container, { backgroundColor }]}>







      {renderContent()}







    </View>







  );







};















const styles = StyleSheet.create({







  container: {







    overflow: 'hidden',







    justifyContent: 'center',







    alignItems: 'center',







  },







  imageContainer: {







    position: 'relative',







  },







  image: {







    width: '100%',







    height: '100%',







  },







  videoContainer: {







    position: 'relative',







  },







  video: {







    width: '100%',







    height: '100%',







  },







  youtubeContainer: {







    position: 'relative',







  },







  fallbackContainer: {







    justifyContent: 'center',







    alignItems: 'center',







  },







  fallbackImage: {







    width: '100%',







    height: '100%',







  },







  errorContainer: {







    justifyContent: 'center',







    alignItems: 'center',







    padding: 20,







  },







  errorText: {







    fontSize: 14,







    textAlign: 'center',







  },







  loadingOverlay: {







    position: 'absolute',







    top: 0,







    left: 0,







    right: 0,







    bottom: 0,







    justifyContent: 'center',







    alignItems: 'center',







    backgroundColor: 'rgba(0, 0, 0, 0.1)',







  },







});















export default MediaRenderer;







>>>>>>>



=======
import React, { useState, useCallback } from 'react';

import { View, StyleSheet, ActivityIndicator, Text, Dimensions } from 'react-native';

import FastImage from 'react-native-fast-image';

import { Video, ResizeMode } from 'expo-av';

import YoutubePlayer from 'react-native-youtube-iframe';

import { useThemeColor } from '@/hooks/useThemeColor';



const { width: screenWidth } = Dimensions.get('window');



interface MediaRendererProps {

  type: 'image' | 'video' | 'youtube';

  source: string;

  style?: any;

  fallbackSource?: string;

  resizeMode?: 'cover' | 'contain' | 'stretch' | 'center';

  autoplay?: boolean;

  controls?: boolean;

  onLoad?: () => void;

  onError?: (error: any) => void;

}



const MediaRenderer: React.FC<MediaRendererProps> = ({

  type,

  source,

  style,

  fallbackSource,

  resizeMode = 'cover',

  autoplay = false,

  controls = true,

  onLoad,

  onError,

  ...props

}) => {

  const [loading, setLoading] = useState(true);

  const [error, setError] = useState(false);

  const [playing, setPlaying] = useState(autoplay);



  const backgroundColor = useThemeColor({}, 'background');

  const textColor = useThemeColor({}, 'text');



  const handleLoad = useCallback(() => {

    setLoading(false);

    onLoad?.();

  }, [onLoad]);



  const handleError = useCallback((err: any) => {

    console.log(`MediaRenderer Error (${type}):`, err);

    setError(true);

    setLoading(false);

    onError?.(err);

  }, [type, onError]);



  const extractYoutubeId = (url: string): string | null => {

    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;

    const match = url.match(regExp);

    return match && match[2].length === 11 ? match[2] : null;

  };



  const renderFallback = () => (

    <View style={[style, styles.fallbackContainer, { backgroundColor }]}>

      {fallbackSource ? (

        <FastImage

          style={[style, styles.fallbackImage]}

          source={{ uri: fallbackSource }}

          resizeMode={FastImage.resizeMode[resizeMode]}

          onLoad={handleLoad}

          onError={() => setError(true)}

        />

      ) : (

        <View style={styles.errorContainer}>

          <Text style={[styles.errorText, { color: textColor }]}>

            Failed to load {type}

          </Text>

        </View>

      )}

    </View>

  );



  const renderContent = () => {

    if (error) {

      return renderFallback();

    }



    switch (type) {

      case 'youtube':

        const videoId = extractYoutubeId(source);

        if (!videoId) {

          console.warn('Invalid YouTube URL:', source);

          return renderFallback();

        }

        

        return (

          <View style={[style, styles.youtubeContainer]}>

            <YoutubePlayer

              height={style?.height || 200}

              width={style?.width || screenWidth - 32}

              videoId={videoId}

              play={playing}

              onReady={handleLoad}

              onError={handleError}

              initialPlayerParams={{

                controls: controls,

                rel: false,

              }}

            />

            {loading && (

              <View style={styles.loadingOverlay}>

                <ActivityIndicator size="large" color="#FF0000" />

              </View>

            )}

          </View>

        );



      case 'video':

        return (

          <View style={[style, styles.videoContainer]}>

            <Video

              style={[style, styles.video]}

              source={{ uri: source }}

              useNativeControls={controls}

              resizeMode={ResizeMode.CONTAIN}

              isLooping={false}

              shouldPlay={autoplay}

              onLoad={handleLoad}

              onError={handleError}

              onPlaybackStatusUpdate={(status) => {

                if (status.isLoaded) {

                  setPlaying(status.shouldPlay || false);

                }

              }}

            />

            {loading && (

              <View style={styles.loadingOverlay}>

                <ActivityIndicator size="large" color="#007AFF" />

              </View>

            )}

          </View>

        );



      case 'image':

      default:

        return (

          <View style={[style, styles.imageContainer]}>

            <FastImage

              style={[style, styles.image]}

              source={typeof source === 'string' ? { uri: source } : source}

              resizeMode={FastImage.resizeMode[resizeMode]}

              onLoad={handleLoad}

              onError={() => handleError('FastImage load error')}

              {...props}

            />

            {loading && (

              <View style={styles.loadingOverlay}>

                <ActivityIndicator size="large" color="#007AFF" />

              </View>

            )}

          </View>

        );

    }

  };



  return (

    <View style={[style, styles.container, { backgroundColor }]}>

      {renderContent()}

    </View>

  );

};



const styles = StyleSheet.create({

  container: {

    overflow: 'hidden',

    justifyContent: 'center',

    alignItems: 'center',

  },

  imageContainer: {

    position: 'relative',

  },

  image: {

    width: '100%',

    height: '100%',

  },

  videoContainer: {

    position: 'relative',

  },

  video: {

    width: '100%',

    height: '100%',

  },

  youtubeContainer: {

    position: 'relative',

  },

  fallbackContainer: {

    justifyContent: 'center',

    alignItems: 'center',

  },

  fallbackImage: {

    width: '100%',

    height: '100%',

  },

  errorContainer: {

    justifyContent: 'center',

    alignItems: 'center',

    padding: 20,

  },

  errorText: {

    fontSize: 14,

    textAlign: 'center',

  },

  loadingOverlay: {

    position: 'absolute',

    top: 0,

    left: 0,

    right: 0,

    bottom: 0,

    justifyContent: 'center',

    alignItems: 'center',

    backgroundColor: 'rgba(0, 0, 0, 0.1)',

  },

});



export default MediaRenderer;

>>>>>>>
