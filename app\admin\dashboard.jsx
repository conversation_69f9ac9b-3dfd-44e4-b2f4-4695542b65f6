import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  Alert,
  Dimensions,
  Platform,
  SafeAreaView,
  ActivityIndicator,
  Animated as RNAnimated,
  FlatList,
} from 'react-native';
// import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from '../../utils/responsive';
import { StatusBar } from 'expo-status-bar';
import { Line<PERSON>hart, PieChart, BarChart } from 'react-native-chart-kit';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { useColorScheme } from 'react-native';

const { width, height } = Dimensions.get('window');

const safeIconSize = (size, fallback = 24) => (typeof size === 'number' && isFinite(size) && !isNaN(size) ? size : fallback);

// Advanced Particle component
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const animSpeed = 4000 * speed + Math.random() * 2000;
    const animDelay = Math.random() * 3000 + delay;
    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(particleAnim, { toValue: 1, duration: animSpeed, useNativeDriver: true }),
        RNAnimated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = RNAnimated.sequence([
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: animSpeed - 1000, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);
    
    const timeoutId = setTimeout(() => {
      animation.start();
      RNAnimated.loop(opacitySequence).start();
    }, animDelay);

    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, [delay, speed]);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <RNAnimated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

const FloatingShape = ({ style, delay = 0, rotationSpeed = 1 }) => {
  const floatAnim = useRef(new RNAnimated.Value(0)).current;
  const rotationAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const floatAnimation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(floatAnim, {
            toValue: 1,
            duration: 3000 + Math.random() * 2000,
            useNativeDriver: true,
          }),
        RNAnimated.timing(floatAnim, {
            toValue: 0,
            duration: 3000 + Math.random() * 2000,
            useNativeDriver: true,
          }),
        ])
    );
    
    const rotateAnimation = RNAnimated.loop(
      RNAnimated.timing(rotationAnim, {
          toValue: 1,
          duration: 15000 * rotationSpeed,
          useNativeDriver: true,
        })
    );
    
    const timeoutId = setTimeout(() => {
      floatAnimation.start();
      rotateAnimation.start();
    }, delay);

    return () => {
      clearTimeout(timeoutId);
      floatAnimation.stop();
      rotateAnimation.stop();
    };
  }, [delay, rotationSpeed]);

  const translateY = floatAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });

  const rotate = rotationAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <RNAnimated.View
      style={[
        style,
        {
          transform: [{ translateY }, { rotate }],
        },
      ]}
    />
  );
};

function ErrorBoundary({ children }) {
    return (
    <View style={{ flex: 1 }}>
      {children}
      </View>
    );
}

function sanitizeChartData(data) {
  return Array.isArray(data) ? data.map(d => (typeof d === 'number' && isFinite(d) && !isNaN(d) ? d : 0)) : [];
}

export default function AdminDashboard() {
  const router = useRouter();
  
  // All hooks must be called at the top level in the same order every time
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalPlans: 0,
    totalExercises: 0,
    totalWorkouts: 0,
    totalCalories: 0,
  });

  // Animation values with error handling
  const fadeAnim = useRef(new RNAnimated.Value(0)).current;
  const slideAnim = useRef(new RNAnimated.Value(50)).current;
  const scaleAnim = useRef(new RNAnimated.Value(0.9)).current;
  const cardAnims = useRef(Array(6).fill().map(() => new RNAnimated.Value(0))).current;
  const menuItemAnims = useRef(Array(6).fill().map(() => new RNAnimated.Value(0))).current;

  const [weeklyData, setWeeklyData] = useState({ labels: [], data: [] });
  const [activeUsers, setActiveUsers] = useState(0);

  // let [fontsLoaded] = useFonts({
  //   Poppins_400Regular,
  //   Poppins_600SemiBold,
  //   Poppins_700Bold,
  // });
  let fontsLoaded = true; // Temporary fix

  const [logoutError, setLogoutError] = useState(null);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [logoutVisible, setLogoutVisible] = useState(false);

  const [showStats, setShowStats] = useState({
    totalUsers: true,
    activeUsers: true,
    totalPlans: true,
    totalExercises: true,
    totalWorkouts: true,
    totalCalories: true,
  });
  const [statOrder, setStatOrder] = useState([
    'totalUsers',
    'activeUsers',
    'totalPlans',
    'totalExercises',
    'totalWorkouts',
    'totalCalories',
  ]);
  const [darkMode, setDarkMode] = useState(false);
  const [planPopularityData, setPlanPopularityData] = useState([]);
  const [topExercisesData, setTopExercisesData] = useState([]);

  // Global error handler
  const handleError = (error) => {
    console.error('AdminDashboard error:', error);
    setHasError(true);
    setErrorMessage(error.message || 'An unknown error occurred');
  };

  // Function to fetch active users from workout history
  const fetchActiveUsers = async () => {
    try {
      const workoutHistoryData = await AsyncStorage.getItem('@workout_history');
      const workoutHistory = workoutHistoryData ? JSON.parse(workoutHistoryData) : [];
      
      const activeUsersSet = new Set();
      if (workoutHistory && Array.isArray(workoutHistory)) {
        workoutHistory.forEach(workout => {
          if (workout.userId) {
            activeUsersSet.add(workout.userId);
          }
        });
      }
      
      return activeUsersSet.size;
    } catch (error) {
      console.error('Error fetching active users:', error);
      return 0;
    }
  };

  // Fetch analytics data
  const fetchAnalyticsData = async () => {
    try {
      const workoutHistoryData = await AsyncStorage.getItem('@workout_history');
      const workoutHistory = workoutHistoryData ? JSON.parse(workoutHistoryData) : [];
      
      const planCounts = {};
      workoutHistory.forEach(w => {
        if (w.planName) planCounts[w.planName] = (planCounts[w.planName] || 0) + 1;
      });
      const planPopularity = Object.entries(planCounts).map(([name, count]) => ({ name, count }));
      setPlanPopularityData(planPopularity);
      
      const exerciseCounts = {};
      workoutHistory.forEach(w => {
        if (Array.isArray(w.exercises)) {
          w.exercises.forEach(ex => {
            if (ex.name) exerciseCounts[ex.name] = (exerciseCounts[ex.name] || 0) + 1;
          });
        }
      });
      const topExercises = Object.entries(exerciseCounts).map(([name, count]) => ({ name, count }));
      setTopExercisesData(topExercises);
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    }
  };

  useEffect(() => {
    const checkAdminLogin = async () => {
      try {
        const adminLoggedIn = await AsyncStorage.getItem('@FitnessApp:adminLoggedIn');
        if (adminLoggedIn !== 'true') {
          Alert.alert(
            'Authentication Required',
            'Please log in as admin to access this page.',
            [{ text: 'OK', onPress: () => router.replace('/admin/login') }]
          );
          return;
        }
        
        fetchData();
      } catch (error) {
        console.error('Error checking admin login status:', error);
        setHasError(true);
        setErrorMessage('Failed to check admin login status: ' + error.message);
      }
    };
    
    try {
      checkAdminLogin();
    } catch (error) {
      console.error('Error in useEffect:', error);
      handleError(error);
    }

    // Start animations with error handling
    try {
      if (fadeAnim && slideAnim && scaleAnim && cardAnims && menuItemAnims) {
        const animations = [];
        
        animations.push(
          RNAnimated.parallel([
            RNAnimated.timing(fadeAnim, {
              toValue: 1,
              duration: 800,
              useNativeDriver: true,
            }),
            RNAnimated.timing(slideAnim, {
              toValue: 0,
              duration: 800,
              useNativeDriver: true,
            }),
            RNAnimated.spring(scaleAnim, {
              toValue: 1,
              friction: 8,
              tension: 40,
              useNativeDriver: true,
            }),
          ])
        );

        cardAnims.forEach((anim, index) => {
          if (anim) {
            animations.push(
              RNAnimated.timing(anim, {
                toValue: 1,
                duration: 500,
                delay: 300 + (index * 100),
                useNativeDriver: true,
              })
            );
          }
        });

        menuItemAnims.forEach((anim, index) => {
          if (anim) {
            animations.push(
              RNAnimated.timing(anim, {
                toValue: 1,
                duration: 500,
                delay: 800 + (index * 100),
                useNativeDriver: true,
              })
            );
          }
        });

        animations.forEach((anim, index) => {
          try {
            if (anim && typeof anim.start === 'function') {
              anim.start();
            }
          } catch (error) {
            console.error(`Animation ${index} start error:`, error);
          }
        });
      }
    } catch (error) {
      console.error('Animation error:', error);
    }

    return () => {
      try {
        if (fadeAnim && typeof fadeAnim.stopAnimation === 'function') {
          try {
            fadeAnim.stopAnimation();
          } catch (e) {
            console.log('Fade animation cleanup error:', e);
          }
        }
        if (slideAnim && typeof slideAnim.stopAnimation === 'function') {
          try {
            slideAnim.stopAnimation();
          } catch (e) {
            console.log('Slide animation cleanup error:', e);
          }
        }
        if (scaleAnim && typeof scaleAnim.stopAnimation === 'function') {
          try {
            scaleAnim.stopAnimation();
          } catch (e) {
            console.log('Scale animation cleanup error:', e);
          }
        }
        cardAnims.forEach((anim, index) => {
          if (anim && typeof anim.stopAnimation === 'function') {
            try {
              anim.stopAnimation();
            } catch (e) {
              console.log(`Card animation ${index} cleanup error:`, e);
            }
          }
        });
        menuItemAnims.forEach((anim, index) => {
          if (anim && typeof anim.stopAnimation === 'function') {
            try {
              anim.stopAnimation();
            } catch (e) {
              console.log(`Menu animation ${index} cleanup error:`, e);
            }
          }
        });
      } catch (error) {
        console.error('Animation cleanup error:', error);
      }
    };
  }, []);

  // Fetch analytics data on mount
  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const fetchData = async () => {
    try {
      setRefreshing(true);
      
      // Fetch all user data from AsyncStorage
      const [
        usersData,
        plansData,
        exercisesData,
        workoutHistory,
        userProgress,
        planPerformance
      ] = await Promise.all([
        AsyncStorage.getItem('@FitnessApp:users'),
        AsyncStorage.getItem('@diet_plans'),
        AsyncStorage.getItem('@exercises'),
        AsyncStorage.getItem('@workout_history'),
        AsyncStorage.getItem('@user_progress'),
        AsyncStorage.getItem('@plan_performance')
      ]);

      let users = [];
      let plans = [];
      let exercises = [];
      let workouts = [];
      let progress = [];
      let performance = [];

      try { users = usersData ? JSON.parse(usersData) : []; } catch (e) { console.error('Error parsing users:', e); }
      try { plans = plansData ? JSON.parse(plansData) : []; } catch (e) { console.error('Error parsing plans:', e); }
      try { exercises = exercisesData ? JSON.parse(exercisesData) : []; } catch (e) { console.error('Error parsing exercises:', e); }
      try { workouts = workoutHistory ? JSON.parse(workoutHistory) : []; } catch (e) { console.error('Error parsing workouts:', e); }
      try { progress = userProgress ? JSON.parse(userProgress) : []; } catch (e) { console.error('Error parsing progress:', e); }
      try { performance = planPerformance ? JSON.parse(planPerformance) : []; } catch (e) { console.error('Error parsing performance:', e); }

      // Calculate real statistics
      const totalUsers = users.length || 0;
      const activeUsers = users.filter(user => {
        const lastActivity = user.lastLogin || user.createdAt;
        if (!lastActivity) return false;
        const daysSinceActivity = (Date.now() - new Date(lastActivity).getTime()) / (1000 * 60 * 60 * 24);
        return daysSinceActivity <= 7; // Active within last 7 days
      }).length;

      const totalPlans = plans.length || 0;
      const totalExercises = exercises.length || 0;
      const totalWorkouts = workouts.length || 0;
      
      // Calculate total calories burned
      const totalCalories = workouts.reduce((sum, workout) => {
        return sum + (workout.caloriesBurned || 0);
      }, 0);

      // Update stats
      setStats({
        totalUsers,
        activeUsers,
        totalPlans,
        totalExercises,
        totalWorkouts,
        totalCalories
      });

      // Fetch active users data
      await fetchActiveUsers();
      
      // Fetch analytics data
      await fetchAnalyticsData();

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      handleError(error);
    } finally {
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    fetchData();
  };

  const handleAdminLogout = async () => {
    setLogoutError(null);
    setLogoutLoading(true);
    try {
      await AsyncStorage.removeItem('@FitnessApp:adminLoggedIn');
      setLogoutLoading(false);
      router.replace('/admin/login');
    } catch (error) {
      setLogoutLoading(false);
      setLogoutError('Logout failed. Please try again.');
      console.error('Error during admin logout:', error);
    }
  };

  const renderStatCard = (title, value, icon, color, index) => {
    const animValue = cardAnims[index];
    
    let translateYValue = 0;
    let scaleValue = 1;
    
    try {
      if (animValue && animValue.interpolate && typeof animValue.interpolate === 'function') {
        const interpolatedTranslateY = animValue.interpolate({
          inputRange: [0, 1],
          outputRange: [50, 0]
        });
        const interpolatedScale = animValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 1]
        });
        
        translateYValue = Number(interpolatedTranslateY) || 0;
        scaleValue = Number(interpolatedScale) || 1;
      }
    } catch (error) {
      console.error('Animation interpolation error:', error);
      translateYValue = 0;
      scaleValue = 1;
    }

    return (
      <Animated.View
        style={[
          styles.statCardContainer,
          {
            opacity: Number(animValue) || 1,
            transform: [
              { translateY: translateYValue }, 
              { scale: scaleValue }
            ]
          }
        ]}
      >
        <View style={[styles.statCard, { backgroundColor: `${color}20` }]}>
          <View style={[styles.iconContainer, { backgroundColor: `${color}30` }]}>
            <Ionicons name={icon} size={safeIconSize(24)} color={color} />
          </View>
          <View style={styles.statContent}>
            <Text style={styles.statValue}>{value}</Text>
            <Text style={styles.statTitle}>{title}</Text>
          </View>
        </View>
      </Animated.View>
    );
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <View style={[StyleSheet.absoluteFill, { backgroundColor: '#1a1a2e' }]} />
        <ActivityIndicator size="large" color="#667eea" />
      </View>
    );
  }

  if (hasError) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#0c0c0c' }}>
        <Text style={{ color: 'red', fontSize: 18, margin: 20 }}>An error occurred in the dashboard.</Text>
        <Text style={{ color: 'white', fontSize: 14 }}>{errorMessage}</Text>
        <TouchableOpacity 
          style={{ backgroundColor: '#667eea', padding: 12, borderRadius: 8, marginTop: 16 }}
          onPress={() => setHasError(false)}
        >
          <Text style={{ color: 'white' }}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const menuItems = [
    {
      id: 'users',
      title: 'User Management',
      icon: 'people-outline',
      action: () => {
        try {
          router.push('/admin/recent-users');
        } catch (error) {
          console.error('Navigation error:', error);
          Alert.alert('Error', 'Unable to open User Management. Please try again.');
        }
      },
      color: '#667eea',
      description: 'View and manage user accounts',
    },
    {
      id: 'plans',
      title: 'Workout Plans',
      icon: 'calendar-outline',
      action: () => {
        try {
          router.push('/admin/manage-plans');
        } catch (error) {
          console.error('Navigation error:', error);
          Alert.alert('Error', 'Unable to open Workout Plans. Please try again.');
        }
      },
      color: '#764ba2',
      description: 'Create and edit workout plans',
    },
    {
      id: 'exercises',
      title: 'Exercise Library',
      icon: 'fitness-outline',
      action: () => {
        try {
          router.push('/admin/exercise-library');
        } catch (error) {
          console.error('Navigation error:', error);
          Alert.alert('Error', 'Unable to open Exercise Library. Please try again.');
        }
      },
      color: '#f093fb',
      description: 'Manage exercise database',
    },
    {
      id: 'analytics',
      title: 'User Analytics',
      icon: 'bar-chart-outline',
      action: () => {
        try {
          router.push('/admin/user-analytics');
        } catch (error) {
          console.error('Navigation error:', error);
          Alert.alert('Error', 'Unable to open User Analytics. Please try again.');
        }
      },
      color: '#4CAF50',
      description: 'View user engagement metrics',
    },
    {
      id: 'performance',
      title: 'Plan Performance',
      icon: 'trending-up-outline',
      action: () => {
        try {
          router.push('/admin/plan-performance');
        } catch (error) {
          console.error('Navigation error:', error);
          Alert.alert('Error', 'Unable to open Plan Performance. Please try again.');
        }
      },
      color: '#FF9800',
      description: 'Analyze workout plan effectiveness',
    },
    {
      id: 'settings',
      title: 'Admin Settings',
      icon: 'settings-outline',
      action: () => {
        try {
          router.push('/settings');
        } catch (error) {
          console.error('Navigation error:', error);
          Alert.alert('Error', 'Unable to open Admin Settings. Please try again.');
        }
      },
      color: '#F44336',
      description: 'Configure admin preferences',
    },
  ];

  const motivationalQuotes = [
    "Lead by example!",
    "Empower your users today!",
    "Every admin action counts!",
    "Stay organized, stay ahead!",
    "Great dashboards drive great results!",
    "Your vision shapes the community!",
    "Data is your superpower!",
    "Inspire, manage, succeed!"
  ];
  const todayQuote = motivationalQuotes[new Date().getDate() % motivationalQuotes.length];

    return (
      <ErrorBoundary>
        <SafeAreaView style={styles.container}>
        <StatusBar style="light" />
        <View style={[styles.background, { backgroundColor: '#1a1a2e' }]}>
          <AdvancedParticle
            style={{ position: 'absolute', width: wp(5), height: wp(5), left: wp(10), top: hp(20) }}
            delay={0}
            speed={1.2}
          />
          <AdvancedParticle
            style={{ position: 'absolute', width: wp(5), height: wp(5), left: wp(80), top: hp(30) }}
            delay={500}
            speed={0.8}
          />
          <AdvancedParticle
            style={{ position: 'absolute', width: wp(5), height: wp(5), left: wp(20), top: hp(70) }}
            delay={1000}
            speed={1.5}
          />
          <FloatingShape
            style={{ 
              position: 'absolute', 
              width: wp(15), 
              height: wp(15), 
              borderRadius: wp(7.5),
              backgroundColor: 'rgba(102, 126, 234, 0.05)',
              right: wp(15), 
              top: hp(15) 
            }}
            delay={0}
            rotationSpeed={0.5}
          />
          <FloatingShape
            style={{ 
              position: 'absolute', 
              width: wp(10), 
              height: wp(10), 
              borderRadius: wp(5),
              backgroundColor: 'rgba(118, 75, 162, 0.05)',
              left: wp(20), 
              bottom: hp(20) 
            }}
            delay={500}
            rotationSpeed={1.2}
          />

          <RNAnimated.View 
            style={[
              styles.header,
              {
                opacity: Number(fadeAnim) || 1,
                transform: [{ 
                  translateY: (slideAnim && slideAnim.interpolate) ? 
                    Number(slideAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [50, 0]
                    })) : 0 
                }]
              }
            ]}
          >
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
              activeOpacity={0.8}
            >
              <View style={[styles.backButtonGradient, { backgroundColor: 'rgba(102, 126, 234, 0.8)' }]}>
                <Ionicons name="arrow-back" size={24} color="#ffffff" />
              </View>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Admin Dashboard</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <TouchableOpacity
                style={styles.refreshButton}
                onPress={onRefresh}
                activeOpacity={0.8}
              >
                <View style={[styles.refreshButtonGradient, { backgroundColor: 'rgba(102, 126, 234, 0.8)' }]}>
                  <Ionicons name="refresh" size={24} color="#ffffff" />
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={{ marginLeft: 12, backgroundColor: '#F44336', borderRadius: 8, padding: 10, alignItems: 'center', flexDirection: 'row' }}
                onPress={() => setLogoutVisible(true)}
                activeOpacity={0.8}
              >
                <Ionicons name="log-out-outline" size={20} color="#fff" style={{ marginRight: 6 }} />
                <Text style={{ color: '#fff', fontFamily: 'Poppins_600SemiBold', fontSize: 15 }}>Logout</Text>
              </TouchableOpacity>
            </View>
          </RNAnimated.View>

          {logoutVisible && (
            <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, backgroundColor: 'rgba(0,0,0,0.5)', zIndex: 100, justifyContent: 'center', alignItems: 'center' }}>
              <View style={{ backgroundColor: '#1a1a2e', borderRadius: 16, padding: 24, alignItems: 'center', width: 280 }}>
                <Ionicons name="log-out-outline" size={36} color="#F44336" style={{ marginBottom: 12 }} />
                <Text style={{ color: '#fff', fontFamily: 'Poppins_700Bold', fontSize: 18, marginBottom: 8 }}>Confirm Logout</Text>
                <Text style={{ color: '#fff', fontFamily: 'Poppins_400Regular', fontSize: 14, marginBottom: 18, textAlign: 'center' }}>Are you sure you want to log out of the admin dashboard?</Text>
                {logoutError && <Text style={{ color: 'red', marginBottom: 8 }}>{logoutError}</Text>}
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' }}>
                  <TouchableOpacity
                    style={{ backgroundColor: '#F44336', borderRadius: 8, paddingVertical: 10, paddingHorizontal: 18, marginRight: 10 }}
                    onPress={handleAdminLogout}
                    disabled={logoutLoading}
                  >
                    <Text style={{ color: '#fff', fontFamily: 'Poppins_600SemiBold', fontSize: 15 }}>{logoutLoading ? 'Logging out...' : 'Logout'}</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={{ backgroundColor: '#667eea', borderRadius: 8, paddingVertical: 10, paddingHorizontal: 18 }}
                    onPress={() => setLogoutVisible(false)}
                    disabled={logoutLoading}
                  >
                    <Text style={{ color: '#fff', fontFamily: 'Poppins_600SemiBold', fontSize: 15 }}>Cancel</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          )}

          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor="#667eea"
                colors={['#667eea', '#764ba2']}
              />
            }
          >
            <RNAnimated.View 
              style={[
                styles.welcomeContainer,
                { 
                  transform: [{ 
                    scale: Number(scaleAnim) || 1 
                  }],
                  opacity: Number(fadeAnim) || 1
                }
              ]}
            >
              <Text style={styles.welcomeTitle}>Welcome, Admin!</Text>
              <Text style={styles.welcomeSubtitle}>{todayQuote}</Text>
            </RNAnimated.View>

            <View style={styles.statsContainer}>
              {renderStatCard('Total Users', stats.totalUsers, 'people', '#667eea', 0)}
              {renderStatCard('Active Users', stats.activeUsers, 'person', '#4CAF50', 1)}
              {renderStatCard('Workout Plans', stats.totalPlans, 'calendar', '#FF9800', 2)}
              {renderStatCard('Exercises', stats.totalExercises, 'fitness', '#9C27B0', 3)}
              {renderStatCard('Workouts', stats.totalWorkouts, 'barbell', '#F44336', 4)}
              {renderStatCard('Calories Burned', stats.totalCalories.toLocaleString(), 'flame', '#FF5722', 5)}
            </View>

            <View style={styles.menuContainer}>
              <Text style={styles.menuTitle}>Quick Actions</Text>
              <View style={styles.menuGrid}>
                {menuItems.map((item, index) => (
                  <Animated.View
                      key={item.id}
                      style={[
                      styles.menuItem,
                      {
                        opacity: Number(menuItemAnims[index]) || 1,
                        transform: [{ 
                          scale: Number(menuItemAnims[index]) || 1 
                        }]
                        }
                      ]}
                    >
                      <TouchableOpacity
                      style={[styles.menuButton, { borderLeftColor: item.color }]}
                        onPress={item.action}
                        activeOpacity={0.8}
                      >
                      <View style={[styles.menuIcon, { backgroundColor: `${item.color}20` }]}>
                        <Ionicons name={item.icon} size={24} color={item.color} />
                          </View>
                      <View style={styles.menuContent}>
                        <Text style={styles.menuItemTitle}>{item.title}</Text>
                        <Text style={styles.menuItemDescription}>{item.description}</Text>
                          </View>
                      </TouchableOpacity>
                  </Animated.View>
                ))}
              </View>
            </View>
          </ScrollView>
        </View>
      </SafeAreaView>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  background: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0c0c0c',
  },
  particleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'rgba(255,255,255,0.05)',
    marginBottom: 10,
  },
  backButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  backButtonGradient: {
    padding: 10,
    borderRadius: 12,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: 'Poppins_700Bold',
  },
  refreshButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  refreshButtonGradient: {
    padding: 10,
    borderRadius: 12,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  welcomeContainer: {
    alignItems: 'center',
    paddingVertical: 20,
    marginBottom: 20,
  },
  welcomeTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    fontFamily: 'Poppins_700Bold',
  },
  welcomeSubtitle: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  statCardContainer: {
    width: '48%',
    marginBottom: 12,
  },
  statCard: {
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statContent: {
    alignItems: 'center',
  },
  statValue: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
    fontFamily: 'Poppins_700Bold',
  },
  statTitle: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
  },
  menuContainer: {
    paddingHorizontal: 16,
  },
  menuTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    fontFamily: 'Poppins_700Bold',
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  menuItem: {
    width: '48%',
    marginBottom: 12,
  },
  menuButton: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  menuContent: {
    flex: 1,
  },
  menuItemTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
    fontFamily: 'Poppins_600SemiBold',
  },
  menuItemDescription: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
  },
});