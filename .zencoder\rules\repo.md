---
description: Repository Information Overview
alwaysApply: true
---

# My-fitness Information

## Summary
A React Native fitness application built with Expo, featuring workout tracking, AI coaching, nutrition guidance, and social features. The app provides a comprehensive fitness experience with various workout types, tracking capabilities, and personalized plans.

## Structure
- **app**: Main application screens and navigation using Expo Router
- **components**: Reusable UI components like AICoach, ImageSlider, StepCounter
- **assets**: Static resources including images, fonts, icons, and Lottie animations
- **context**: State management with UserContext for authentication and user data
- **constants**: Application constants and data files
- **scripts**: Utility scripts for dependency and image issue fixes
- **android**: Android-specific configuration files

## Language & Runtime
**Language**: JavaScript/TypeScript (React Native)
**Version**: React Native 0.76.9, React 18.3.1
**Build System**: Expo (v52.0.47)
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- expo-router: File-based routing for navigation
- expo-image: Image handling and optimization
- expo-location: GPS and location tracking
- expo-sensors: Device sensor access for fitness tracking
- firebase: Backend services (currently mocked)
- nativewind: TailwindCSS for React Native
- react-native-reanimated: Advanced animations
- react-native-gesture-handler: Touch handling
- lottie-react-native: Lottie animation support

**Development Dependencies**:
- typescript: Type checking
- jest-expo: Testing framework
- @babel/core: JavaScript compiler

## Build & Installation
```bash
# Install dependencies
npm install

# Start the development server
npx expo start

# Run on Android
npx expo run:android

# Run on iOS
npx expo run:ios

# Run on web
npx expo start --web
```

## Testing
**Framework**: Jest with jest-expo preset
**Test Location**: Not specified in repository
**Run Command**:
```bash
npm test
```

## Mobile Configuration
**iOS Configuration**:
- Bundle Identifier: com.anonymous.Myfitness
- Supports iPad: Yes

**Android Configuration**:
- Package: com.anonymous.Myfitness
- Permissions: Activity recognition, location, camera, storage
- Adaptive Icon: Configured in app.config.js

## Authentication
The app uses a local AsyncStorage-based authentication system with Firebase integration (currently mocked). User data is stored locally with separate storage keys for different user aspects.

## Features
- User profile management with fitness metrics
- Workout tracking and planning
- AI coaching and workout generation
- GPS tracking for outdoor activities
- Social features with metaverse integration
- Nutrition tracking and guidance
- Sleep tracking
- Step counting