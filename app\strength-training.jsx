import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  Animated,
  Platform,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import SafeImage from '../components/SafeImage';
import { getImageByCategory } from '../constants/remoteImages';

const { width, height } = Dimensions.get('window');

// Advanced Particle component for background effects
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, {
          toValue: 1,
          duration: (4000 + Math.random() * 3000) * speed,
          useNativeDriver: true,
        }),
        Animated.timing(particleAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    );
    
    const timeoutId = setTimeout(() => {
      animation.start();
    }, delay);
    
    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, []);

  const top = particleAnim.interpolate({ 
    inputRange: [0, 1], 
    outputRange: [height, -20] 
  });
  
  return (
    <Animated.View 
      style={[
        style, 
        { 
          transform: [{ translateY: top }], 
          opacity: opacityAnim 
        }
      ]} 
    />
  );
};

// Video Player Component with sophisticated design
const VideoPlayer = ({ videoUrl, title, duration, style }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  const handlePlayPress = () => {
    setIsLoading(true);
    // Simulate video loading
    setTimeout(() => {
      setIsLoading(false);
      setIsPlaying(true);
    }, 1000);
  };

  const handlePressIn = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.8,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  return (
    <Animated.View 
      style={[
        styles.videoCard,
        style,
        {
          transform: [{ scale: scaleAnim }],
          opacity: opacityAnim,
        }
      ]}
    >
      <TouchableOpacity
        style={styles.videoTouchable}
        onPress={handlePlayPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        <LinearGradient
          colors={['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']}
          style={styles.videoGradient}
        >
          <SafeImage 
            source={{ uri: getImageByCategory('strength') }}
            style={styles.videoThumbnail}
            resizeMode="cover"
          />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.8)']}
            style={styles.videoOverlay}
          >
            <View style={styles.videoInfo}>
              <View style={styles.videoHeader}>
                <View style={styles.videoIconContainer}>
                  <Ionicons name="play-circle" size={24} color="#fff" />
                </View>
                <View style={styles.videoMeta}>
                  <Text style={styles.videoTitle}>{title}</Text>
                  <Text style={styles.videoDuration}>{duration}</Text>
                </View>
              </View>
              
              <View style={styles.videoControls}>
                <TouchableOpacity 
                  style={styles.playButton}
                  onPress={handlePlayPress}
                >
                  <LinearGradient
                    colors={['#FF6B6B', '#FF8E53']}
                    style={styles.playButtonGradient}
                  >
                    <Ionicons 
                      name={isPlaying ? "pause" : "play"} 
                      size={20} 
                      color="#fff" 
                    />
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
          
          {isLoading && (
            <View style={styles.loadingOverlay}>
              <LinearGradient
                colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)']}
                style={styles.loadingGradient}
              >
                <Ionicons name="refresh" size={24} color="#fff" />
                <Text style={styles.loadingText}>Loading...</Text>
              </LinearGradient>
            </View>
          )}
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Image Slider with improved design
const ImageSlider = ({ images }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef(null);

  const handleScroll = (event) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / width);
    setCurrentIndex(index);
  };

  return (
    <View style={styles.sliderContainer}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        {images.map((image, index) => (
          <View key={index} style={styles.slide}>
            <SafeImage 
              source={image.uri} 
              style={styles.slideImage} 
              resizeMode="cover"
            />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.8)']}
              style={styles.slideOverlay}
            >
              <Text style={styles.slideTitle}>{image.title}</Text>
              <Text style={styles.slideSubtitle}>{image.subtitle}</Text>
            </LinearGradient>
          </View>
        ))}
      </ScrollView>
      <View style={styles.pagination}>
        {images.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              { backgroundColor: index === currentIndex ? '#fff' : 'rgba(255,255,255,0.5)' }
            ]}
          />
        ))}
      </View>
    </View>
  );
};

export default function StrengthTraining() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  const strengthImages = [
    { 
      uri: getImageByCategory('strength'), 
      title: 'Chest Workouts',
      subtitle: 'Build a powerful upper body'
    },
    { 
      uri: getImageByCategory('strength'), 
      title: 'Shoulder Training',
      subtitle: 'Develop strong, stable shoulders'
    },
    { 
      uri: getImageByCategory('strength'), 
      title: 'Arm Strength',
      subtitle: 'Sculpt defined arms'
    },
    { 
      uri: getImageByCategory('strength'), 
      title: 'Leg Power',
      subtitle: 'Build a solid foundation'
    },
  ];

  const strengthVideos = [
    {
      title: 'Bench Press Mastery',
      duration: '8:45',
      thumbnail: getImageByCategory('strength'),
      videoUrl: 'https://example.com/bench-press.mp4'
    },
    {
      title: 'Deadlift Technique',
      duration: '12:30',
      thumbnail: getImageByCategory('strength'),
      videoUrl: 'https://example.com/deadlift.mp4'
    },
    {
      title: 'Squat Fundamentals',
      duration: '10:15',
      thumbnail: getImageByCategory('strength'),
      videoUrl: 'https://example.com/squat.mp4'
    }
  ];

  const trainingTips = [
    {
      title: 'Progressive Overload',
      description: 'Gradually increase weight or reps to build strength over time.',
      icon: 'trending-up',
      color: '#FF6B6B'
    },
    {
      title: 'Proper Form',
      description: 'Focus on correct technique before increasing weight.',
      icon: 'checkmark-circle',
      color: '#4ECDC4'
    },
    {
      title: 'Rest & Recovery',
      description: 'Allow muscles 48-72 hours to recover between sessions.',
      icon: 'bed',
      color: '#667eea'
    },
    {
      title: 'Compound Movements',
      description: 'Prioritize multi-joint exercises for maximum efficiency.',
      icon: 'fitness',
      color: '#FFD166'
    }
  ];

  const workoutPrograms = [
    {
      title: 'Beginner Strength',
      duration: '45 min',
      difficulty: 'Beginner',
      focus: 'Full Body',
      exercises: ['Squats', 'Push-ups', 'Rows', 'Planks'],
      image: { uri: getImageByCategory('strength') }
    },
    {
      title: 'Intermediate Power',
      duration: '60 min',
      difficulty: 'Intermediate',
      focus: 'Upper Body',
      exercises: ['Bench Press', 'Overhead Press', 'Pull-ups', 'Dips'],
      image: { uri: getImageByCategory('strength') }
    },
    {
      title: 'Advanced Strength',
      duration: '75 min',
      difficulty: 'Advanced',
      focus: 'Lower Body',
      exercises: ['Deadlifts', 'Squats', 'Lunges', 'Calf Raises'],
      image: { uri: getImageByCategory('strength') }
    }
  ];

  if (!fontsLoaded) return null;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />
      <LinearGradient
        colors={['#FF6B6B', '#FF8E53', '#FFD166']}
        style={StyleSheet.absoluteFill}
      />
      
      {/* Background Particles */}
      {[...Array(15)].map((_, i) => (
        <AdvancedParticle 
          key={i} 
          style={[
            styles.particle, 
            { 
              left: `${Math.random() * 100}%`, 
              width: Math.random() * 4 + 2, 
              height: Math.random() * 4 + 2 
            }
          ]} 
          delay={Math.random() * 3000}
          speed={Math.random() * 2 + 0.5}
        />
      ))}
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Strength Training</Text>
        <TouchableOpacity style={styles.shareButton}>
          <Ionicons name="share-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Image Slider */}
      <ImageSlider images={strengthImages} />

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {['overview', 'programs', 'tips', 'videos'].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[styles.tab, activeTab === tab && styles.activeTab]}
            onPress={() => setActiveTab(tab)}
          >
            <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'overview' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Build Real Strength</Text>
            <Text style={styles.description}>
              Strength training is essential for building muscle, improving bone density, 
              and boosting metabolism. It helps you perform daily activities with ease and 
              prevents age-related muscle loss.
            </Text>
            
            <View style={styles.benefitsContainer}>
              <View style={styles.benefitCard}>
                                    <Ionicons name="fitness-outline" size={24} color="#FF6B6B" />
                <Text style={styles.benefitTitle}>Muscle Growth</Text>
                <Text style={styles.benefitText}>Build lean muscle mass</Text>
              </View>
              <View style={styles.benefitCard}>
                <Ionicons name="trending-up" size={24} color="#4ECDC4" />
                <Text style={styles.benefitTitle}>Metabolism</Text>
                <Text style={styles.benefitText}>Increase resting metabolic rate</Text>
              </View>
              <View style={styles.benefitCard}>
                <Ionicons name="shield-checkmark" size={24} color="#96CEB4" />
                <Text style={styles.benefitTitle}>Bone Health</Text>
                <Text style={styles.benefitText}>Strengthen bones and joints</Text>
              </View>
            </View>
          </View>
        )}

        {activeTab === 'programs' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Workout Programs</Text>
            <Text style={styles.description}>
              Choose a strength training program that matches your experience level and goals.
            </Text>
            
            {workoutPrograms.map((program, index) => (
              <TouchableOpacity key={index} style={styles.programCard}>
                <LinearGradient
                  colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
                  style={styles.programCardGradient}
                >
                  <SafeImage 
                    source={program.image} 
                    style={styles.programImage}
                    resizeMode="cover"
                  />
                <View style={styles.programInfo}>
                  <Text style={styles.programTitle}>{program.title}</Text>
                  <View style={styles.programMeta}>
                    <View style={styles.metaItem}>
                        <Ionicons name="time-outline" size={16} color="#FFD166" />
                      <Text style={styles.metaText}>{program.duration}</Text>
                    </View>
                    <View style={styles.metaItem}>
                        <Ionicons name="fitness-outline" size={16} color="#FF6B6B" />
                      <Text style={styles.metaText}>{program.difficulty}</Text>
                    </View>
                    <View style={styles.metaItem}>
                        <Ionicons name="target-outline" size={16} color="#4ECDC4" />
                      <Text style={styles.metaText}>{program.focus}</Text>
                    </View>
                  </View>
                  <View style={styles.exercisesList}>
                    {program.exercises.map((exercise, idx) => (
                        <Text key={idx} style={styles.exerciseText}>• {exercise}</Text>
                    ))}
                  </View>
                </View>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {activeTab === 'tips' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Training Tips</Text>
            <Text style={styles.description}>
              Follow these essential tips for effective strength training.
            </Text>
            
            {trainingTips.map((tip, index) => (
              <View key={index} style={styles.tipCard}>
                <View style={[styles.tipIcon, { backgroundColor: tip.color }]}>
                  <Ionicons name={tip.icon} size={24} color="#fff" />
                </View>
                <View style={styles.tipContent}>
                  <Text style={styles.tipTitle}>{tip.title}</Text>
                  <Text style={styles.tipDescription}>{tip.description}</Text>
                </View>
              </View>
            ))}
          </View>
        )}

        {activeTab === 'videos' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Training Videos</Text>
            <Text style={styles.description}>
              Watch expert strength training videos to perfect your form and technique.
            </Text>
            
            {strengthVideos.map((video, index) => (
              <VideoPlayer
                key={index}
                videoUrl={video.videoUrl}
                title={video.title}
                duration={video.duration}
                style={styles.videoItem}
              />
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(4),
    paddingTop: hp(2),
    paddingBottom: hp(1),
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: wp(2),
    borderRadius: wp(2),
  },
  headerTitle: {
    fontSize: wp(7),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  shareButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: wp(2),
    borderRadius: wp(2),
  },
  sliderContainer: {
    height: hp(30),
    position: 'relative',
  },
  slide: {
    width: width,
    height: hp(30),
    position: 'relative',
  },
  slideImage: {
    width: '100%',
    height: '100%',
  },
  slideOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: hp(10),
    justifyContent: 'flex-end',
    paddingBottom: hp(2),
    paddingHorizontal: wp(4),
  },
  slideTitle: {
    fontSize: wp(6),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  slideSubtitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
    marginTop: hp(0.5),
  },
  pagination: {
    position: 'absolute',
    bottom: hp(1),
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    width: wp(2),
    height: wp(2),
    borderRadius: wp(1),
    marginHorizontal: wp(1),
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,0.1)',
    marginHorizontal: wp(4),
    borderRadius: wp(3),
    padding: wp(1),
  },
  tab: {
    flex: 1,
    paddingVertical: hp(1.5),
    alignItems: 'center',
    borderRadius: wp(2),
  },
  activeTab: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  tabText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255,255,255,0.7)',
  },
  activeTabText: {
    color: '#fff',
  },
  content: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: wp(10),
    borderTopRightRadius: wp(10),
    marginTop: hp(2),
  },
  section: {
    padding: wp(5),
  },
  sectionTitle: {
    fontSize: wp(7),
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: hp(1),
  },
  description: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    lineHeight: hp(2.5),
    marginBottom: hp(2),
  },
  benefitsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  benefitCard: {
    width: wp(40),
    backgroundColor: '#fff',
    borderRadius: wp(5),
    padding: wp(4),
    marginVertical: hp(0.5),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  benefitTitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginTop: hp(1),
    textAlign: 'center',
  },
  benefitText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: hp(0.5),
  },
  programCard: {
    marginBottom: hp(2),
  },
  programCardGradient: {
    borderRadius: wp(8),
    overflow: 'hidden',
  },
  programImage: {
    width: '100%',
    height: hp(15),
  },
  programInfo: {
    padding: wp(4),
  },
  programTitle: {
    fontSize: wp(5.5),
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: hp(0.8),
  },
  programMeta: {
    flexDirection: 'row',
    marginBottom: hp(1),
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: wp(4),
  },
  metaText: {
    fontSize: wp(3.8),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    marginLeft: wp(1),
  },
  exercisesList: {
    marginTop: hp(0.8),
  },
  exerciseText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
    marginLeft: wp(2),
  },
  tipCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: wp(5),
    padding: wp(4),
    marginBottom: hp(1),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  tipIcon: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(4),
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginBottom: hp(0.5),
  },
  tipDescription: {
    fontSize: wp(3.8),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    lineHeight: hp(2.2),
  },
  videoCard: {
    width: '100%',
    height: hp(25),
    borderRadius: wp(5),
    overflow: 'hidden',
    marginBottom: hp(2),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  videoTouchable: {
    flex: 1,
  },
  videoGradient: {
    flex: 1,
    borderRadius: wp(5),
    overflow: 'hidden',
  },
  videoThumbnail: {
    width: '100%',
    height: '100%',
  },
  videoOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: hp(10),
    justifyContent: 'space-between',
    padding: wp(4),
    paddingBottom: hp(2),
  },
  videoInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  videoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  videoIconContainer: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: wp(1.5),
    borderRadius: wp(3),
    marginRight: wp(2),
  },
  videoMeta: {
    flexDirection: 'column',
  },
  videoTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  videoDuration: {
    fontSize: wp(3.8),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
  },
  videoControls: {
    alignItems: 'center',
  },
  playButton: {
    width: wp(15),
    height: wp(15),
    borderRadius: wp(7.5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButtonGradient: {
    flex: 1,
    borderRadius: wp(7.5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: wp(5),
  },
  loadingGradient: {
    flexDirection: 'column',
    alignItems: 'center',
    padding: wp(5),
  },
  loadingText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    marginTop: hp(1),
  },
  videoItem: {
    marginBottom: hp(2),
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 5,
  },
}); 