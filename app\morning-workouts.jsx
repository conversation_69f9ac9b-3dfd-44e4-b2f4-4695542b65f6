import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  Animated,
  Platform,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import SafeImage from '../components/SafeImage';
import { getImageByCategory } from '../constants/remoteImages';

const { width, height } = Dimensions.get('window');

// Advanced Particle component for background effects
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, {
          toValue: 1,
          duration: (4000 + Math.random() * 3000) * speed,
          useNativeDriver: true,
        }),
        Animated.timing(particleAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    );
    
    const timeoutId = setTimeout(() => {
      animation.start();
    }, delay);
    
    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, []);

  const top = particleAnim.interpolate({ 
    inputRange: [0, 1], 
    outputRange: [height, -20] 
  });
  
  return (
    <Animated.View 
      style={[
        style, 
        { 
          transform: [{ translateY: top }], 
          opacity: opacityAnim 
        }
      ]} 
    />
  );
};

// Video Player Component with sophisticated design
const VideoPlayer = ({ videoUrl, title, duration, style }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  const handlePlayPress = () => {
    setIsLoading(true);
    // Simulate video loading
    setTimeout(() => {
      setIsLoading(false);
      setIsPlaying(true);
    }, 1000);
  };

  const handlePressIn = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.8,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  return (
    <Animated.View 
      style={[
        styles.videoCard,
        style,
        {
          transform: [{ scale: scaleAnim }],
          opacity: opacityAnim,
        }
      ]}
    >
      <TouchableOpacity
        style={styles.videoTouchable}
        onPress={handlePlayPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        <LinearGradient
          colors={['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']}
          style={styles.videoGradient}
        >
          <SafeImage 
            source={{ uri: getImageByCategory('cardio') }}
            style={styles.videoThumbnail}
            resizeMode="cover"
          />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.8)']}
            style={styles.videoOverlay}
          >
            <View style={styles.videoInfo}>
              <View style={styles.videoHeader}>
                <View style={styles.videoIconContainer}>
                  <Ionicons name="play-circle" size={24} color="#fff" />
                </View>
                <View style={styles.videoMeta}>
                  <Text style={styles.videoTitle}>{title}</Text>
                  <Text style={styles.videoDuration}>{duration}</Text>
                </View>
              </View>
              
              <View style={styles.videoControls}>
                <TouchableOpacity 
                  style={styles.playButton}
                  onPress={handlePlayPress}
                >
                  <LinearGradient
                    colors={['#4CAF50', '#45a049']}
                    style={styles.playButtonGradient}
                  >
                    <Ionicons 
                      name={isPlaying ? "pause" : "play"} 
                      size={20} 
                      color="#fff" 
                    />
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
          
          {isLoading && (
            <View style={styles.loadingOverlay}>
              <LinearGradient
                colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)']}
                style={styles.loadingGradient}
              >
                <Ionicons name="refresh" size={24} color="#fff" />
                <Text style={styles.loadingText}>Loading...</Text>
              </LinearGradient>
            </View>
          )}
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Image Slider with improved design
const ImageSlider = ({ images }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef(null);

  const handleScroll = (event) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / width);
    setCurrentIndex(index);
  };

  return (
    <View style={styles.sliderContainer}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        {images.map((image, index) => (
          <View key={index} style={styles.slide}>
            <SafeImage 
              source={image.uri} 
              style={styles.slideImage} 
              resizeMode="cover"
            />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.8)']}
              style={styles.slideOverlay}
            >
              <Text style={styles.slideTitle}>{image.title}</Text>
              <Text style={styles.slideSubtitle}>{image.subtitle}</Text>
            </LinearGradient>
          </View>
        ))}
      </ScrollView>
      <View style={styles.pagination}>
        {images.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              { backgroundColor: index === currentIndex ? '#fff' : 'rgba(255,255,255,0.5)' }
            ]}
          />
        ))}
      </View>
    </View>
  );
};

export default function MorningWorkouts() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  const morningImages = [
    { 
      uri: getImageByCategory('exercise'), 
      title: 'Sunrise Stretches',
      subtitle: 'Wake up your body gently'
    },
    { 
      uri: getImageByCategory('exercise'), 
      title: 'Morning Cardio',
      subtitle: 'Boost your energy for the day'
    },
    { 
      uri: getImageByCategory('cardio'), 
      title: 'Energy Boost',
      subtitle: 'Get your metabolism going'
    },
    { 
      uri: getImageByCategory('strength'), 
      title: 'Wake Up Flow',
      subtitle: 'Start your day with strength'
    },
  ];

  const morningVideos = [
    {
      title: '5-Minute Morning Stretch',
      duration: '5:00',
      thumbnail: getImageByCategory('exercise'),
      videoUrl: 'https://example.com/morning-stretch.mp4'
    },
    {
      title: 'Quick Cardio Wake Up',
      duration: '10:30',
      thumbnail: getImageByCategory('exercise'),
      videoUrl: 'https://example.com/cardio-wakeup.mp4'
    },
    {
      title: 'Morning Yoga Flow',
      duration: '15:45',
      thumbnail: getImageByCategory('exercise'),
      videoUrl: 'https://example.com/morning-yoga.mp4'
    }
  ];

  const workoutTips = [
    {
      title: 'Start Slow',
      description: 'Begin with gentle stretches to wake up your muscles gradually.',
      icon: 'sunny',
      color: '#FFD166'
    },
    {
      title: 'Stay Hydrated',
      description: 'Drink water before, during, and after your morning workout.',
      icon: 'water',
      color: '#4ECDC4'
    },
    {
      title: 'Listen to Your Body',
      description: 'Don\'t push too hard - morning workouts should energize, not exhaust.',
      icon: 'heart',
      color: '#FF6B6B'
    },
    {
      title: 'Be Consistent',
      description: 'Try to workout at the same time each morning for best results.',
      icon: 'calendar',
      color: '#667eea'
    }
  ];

  const workoutRoutines = [
    {
      title: 'Quick 10-Minute Wake Up',
      duration: '10 min',
      difficulty: 'Beginner',
      exercises: ['Sun Salutations', 'Light Jogging', 'Arm Circles', 'Deep Breathing'],
      image: { uri: getImageByCategory('exercise') }
    },
    {
      title: 'Morning Energy Boost',
      duration: '20 min',
      difficulty: 'Intermediate',
      exercises: ['Jumping Jacks', 'Push-ups', 'Squats', 'Plank Hold'],
      image: { uri: getImageByCategory('exercise') }
    },
    {
      title: 'Sunrise Yoga Flow',
      duration: '30 min',
      difficulty: 'All Levels',
      exercises: ['Child\'s Pose', 'Cat-Cow Stretch', 'Downward Dog', 'Warrior Poses'],
      image: { uri: getImageByCategory('cardio') }
    }
  ];

  if (!fontsLoaded) return null;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={StyleSheet.absoluteFill}
      />
      
      {/* Background Particles */}
      {[...Array(15)].map((_, i) => (
        <AdvancedParticle 
          key={i} 
          style={[
            styles.particle, 
            { 
              left: `${Math.random() * 100}%`, 
              width: Math.random() * 4 + 2, 
              height: Math.random() * 4 + 2 
            }
          ]} 
          delay={Math.random() * 3000}
          speed={Math.random() * 2 + 0.5}
        />
      ))}
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Morning Workouts</Text>
        <TouchableOpacity style={styles.shareButton}>
          <Ionicons name="share-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Image Slider */}
      <ImageSlider images={morningImages} />

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {['overview', 'routines', 'tips', 'videos'].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[styles.tab, activeTab === tab && styles.activeTab]}
            onPress={() => setActiveTab(tab)}
          >
            <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'overview' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Why Morning Workouts?</Text>
            <Text style={styles.description}>
              Morning workouts boost your metabolism, improve mood, and set a positive tone for the entire day. 
              They help you establish a consistent routine and ensure you get your exercise in before life gets busy.
            </Text>
            
            <View style={styles.benefitsContainer}>
              <View style={styles.benefitCard}>
                <Ionicons name="trending-up" size={24} color="#4ECDC4" />
                <Text style={styles.benefitTitle}>Metabolism Boost</Text>
                <Text style={styles.benefitText}>Kickstart your metabolism for the day</Text>
              </View>
              <View style={styles.benefitCard}>
                <Ionicons name="happy" size={24} color="#FFD166" />
                <Text style={styles.benefitTitle}>Better Mood</Text>
                <Text style={styles.benefitText}>Release endorphins early in the day</Text>
              </View>
              <View style={styles.benefitCard}>
                <Ionicons name="time" size={24} color="#FF6B6B" />
                <Text style={styles.benefitTitle}>Consistency</Text>
                <Text style={styles.benefitText}>Build a reliable fitness routine</Text>
              </View>
            </View>
          </View>
        )}

        {activeTab === 'routines' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Morning Routines</Text>
            <Text style={styles.description}>
              Choose a morning workout routine that fits your schedule and energy level.
            </Text>
            
            {workoutRoutines.map((routine, index) => (
              <TouchableOpacity key={index} style={styles.routineCard}>
                <LinearGradient
                  colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
                  style={styles.routineCardGradient}
                >
                  <SafeImage 
                    source={routine.image} 
                    style={styles.routineImage}
                    resizeMode="cover"
                  />
                <View style={styles.routineInfo}>
                  <Text style={styles.routineTitle}>{routine.title}</Text>
                  <View style={styles.routineMeta}>
                    <View style={styles.metaItem}>
                        <Ionicons name="time-outline" size={16} color="#FFD166" />
                      <Text style={styles.metaText}>{routine.duration}</Text>
                    </View>
                    <View style={styles.metaItem}>
                        <Ionicons name="fitness-outline" size={16} color="#FF6B6B" />
                      <Text style={styles.metaText}>{routine.difficulty}</Text>
                    </View>
                  </View>
                  <View style={styles.exercisesList}>
                    {routine.exercises.map((exercise, idx) => (
                        <Text key={idx} style={styles.exerciseText}>• {exercise}</Text>
                    ))}
                  </View>
                </View>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {activeTab === 'tips' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Morning Workout Tips</Text>
            <Text style={styles.description}>
              Follow these tips to make the most of your morning workouts.
            </Text>
            
            {workoutTips.map((tip, index) => (
              <View key={index} style={styles.tipCard}>
                <View style={[styles.tipIcon, { backgroundColor: tip.color }]}>
                  <Ionicons name={tip.icon} size={24} color="#fff" />
                </View>
                <View style={styles.tipContent}>
                  <Text style={styles.tipTitle}>{tip.title}</Text>
                  <Text style={styles.tipDescription}>{tip.description}</Text>
                </View>
              </View>
            ))}
          </View>
        )}

        {activeTab === 'videos' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Morning Workout Videos</Text>
            <Text style={styles.description}>
              Follow along with these guided morning workout videos.
            </Text>
            
            {morningVideos.map((video, index) => (
              <VideoPlayer
                key={index}
                videoUrl={video.videoUrl}
                title={video.title}
                duration={video.duration}
                style={styles.videoItem}
              />
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa', // Added background color for content area
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(5),
    paddingTop: hp(2),
    paddingBottom: hp(1),
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: wp(2),
    borderRadius: wp(2),
  },
  headerTitle: {
    fontSize: wp(7),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  shareButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: wp(2),
    borderRadius: wp(2),
  },
  sliderContainer: {
    height: hp(30),
    position: 'relative',
  },
  slide: {
    width: width,
    height: hp(30),
    position: 'relative',
  },
  slideImage: {
    width: '100%',
    height: '100%',
  },
  slideOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: hp(10),
    justifyContent: 'flex-end',
    paddingBottom: hp(2),
    paddingHorizontal: wp(5),
  },
  slideTitle: {
    fontSize: wp(6),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  slideSubtitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
    marginTop: hp(0.5),
  },
  pagination: {
    position: 'absolute',
    bottom: hp(1),
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    width: wp(2),
    height: wp(2),
    borderRadius: wp(1),
    marginHorizontal: wp(1),
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,0.1)',
    marginHorizontal: wp(5),
    borderRadius: wp(3),
    padding: hp(0.5),
  },
  tab: {
    flex: 1,
    paddingVertical: hp(1.5),
    alignItems: 'center',
    borderRadius: wp(2),
  },
  activeTab: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  tabText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255,255,255,0.7)',
  },
  activeTabText: {
    color: '#fff',
  },
  content: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: wp(10),
    borderTopRightRadius: wp(10),
    marginTop: hp(2),
  },
  section: {
    padding: wp(5),
  },
  sectionTitle: {
    fontSize: wp(7),
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: hp(1),
  },
  description: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    lineHeight: hp(2.5),
    marginBottom: hp(2),
  },
  benefitsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(1),
  },
  benefitCard: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: wp(5),
    padding: hp(1.5),
    marginHorizontal: wp(1),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  benefitTitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginTop: hp(1),
    textAlign: 'center',
  },
  benefitText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: hp(0.5),
  },
  routineCard: {
    marginBottom: hp(2),
    borderRadius: wp(8),
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  routineCardGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(4),
  },
  routineImage: {
    width: wp(25),
    height: hp(15),
    borderRadius: wp(4),
    marginRight: wp(4),
  },
  routineInfo: {
    flex: 1,
  },
  routineTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: hp(0.8),
  },
  routineMeta: {
    flexDirection: 'row',
    marginBottom: hp(1),
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: wp(5),
  },
  metaText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    marginLeft: wp(1),
  },
  exercisesList: {
    marginTop: hp(0.8),
  },
  exerciseText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
    marginLeft: wp(2),
  },
  tipCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: wp(5),
    padding: hp(1.5),
    marginBottom: hp(1.5),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  tipIcon: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(4),
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginBottom: hp(0.5),
  },
  tipDescription: {
    fontSize: wp(3.8),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    lineHeight: hp(2.2),
  },
  videoCard: {
    width: '100%',
    height: hp(25),
    borderRadius: wp(5),
    overflow: 'hidden',
    marginBottom: hp(2),
    position: 'relative',
  },
  videoTouchable: {
    flex: 1,
  },
  videoGradient: {
    flex: 1,
    borderRadius: wp(5),
    overflow: 'hidden',
  },
  videoThumbnail: {
    width: '100%',
    height: '100%',
  },
  videoOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: hp(10),
    justifyContent: 'space-between',
    padding: wp(4),
    paddingBottom: hp(2),
  },
  videoInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  videoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  videoIconContainer: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: wp(1.5),
    borderRadius: wp(3),
    marginRight: wp(3),
  },
  videoMeta: {
    flex: 1,
  },
  videoTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(0.5),
  },
  videoDuration: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
  },
  videoControls: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  playButton: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButtonGradient: {
    flex: 1,
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: wp(5),
  },
  loadingGradient: {
    flexDirection: 'column',
    alignItems: 'center',
    padding: wp(5),
  },
  loadingText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    marginTop: hp(1),
  },
  videoItem: {
    marginBottom: hp(2),
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 5,
    opacity: 0.8,
  },
}); 