// This file is used in the project. Do NOT delete.
import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'expo-router';
import {
  View, Text, TouchableOpacity, ScrollView, StyleSheet,
  ActivityIndicator, Dimensions, Pressable, Alert, Platform, SafeAreaView, Animated,
  Modal, TextInput, Image, FlatList,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFonts, Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins';
import * as SplashScreen from 'expo-splash-screen';
import { LinearGradient } from 'expo-linear-gradient';
import { useUser } from '../context/UserContext';
import { Pedometer, Accelerometer, Gyroscope, Magnetometer, DeviceMotion } from 'expo-sensors';
import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import { Camera } from 'expo-camera';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Ionicons, MaterialIcons, FontAwesome5, MaterialCommunityIcons } from '@expo/vector-icons';
import { TabView, SceneMap, TabBar } from 'react-native-tab-view';
import { useWindowDimensions } from 'react-native';
import { Animated as RNAnimated } from 'react-native';
import LottieView from 'lottie-react-native';

// Import new components
import AICoach from '../components/AICoach';
import SocialMetaverse from '../components/SocialMetaverse';
import StepCounter from '../components/StepCounter';
import GPSTracker from '../components/GPSTracker';
import RealTimeTracker from '../components/RealTimeTracker';
import SleepTracker from '../components/SleepTracker';

// ImageSlider Component
function ImageSlider({ images }) {
  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef(null);
  
  useEffect(() => {
    let interval;
    // Auto-scroll every 5 seconds
    if (images.length > 1) {
      interval = setInterval(() => {
        if (flatListRef.current) {
          const nextIndex = (activeIndex + 1) % images.length;
          flatListRef.current.scrollToIndex({
            index: nextIndex,
            animated: true,
          });
          setActiveIndex(nextIndex);
        }
      }, 5000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [activeIndex, images.length]);
  
  const handleScroll = (event) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    const index = Math.round(scrollPosition / (Dimensions.get('window').width - 32));
    setActiveIndex(index);
  };
  
  const renderItem = ({ item }) => {
    return (
      <TouchableOpacity 
        activeOpacity={0.9}
        onPress={() => item.onPress && item.onPress()}
        style={{
          width: Dimensions.get('window').width - 32,
          height: 180,
          borderRadius: 16,
          marginHorizontal: 16,
          overflow: 'hidden',
        }}
      >
        <>
          <ActivityIndicator 
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              marginLeft: -15,
              marginTop: -15,
              zIndex: 1,
            }} 
            size="large" 
            color="#667eea" 
          />
          <Image
            source={item.image}
            style={{
              width: '100%',
              height: '100%',
              borderRadius: 16,
            }}
            resizeMode="cover"
            onError={(e) => console.error('Image loading error:', e.nativeEvent.error)}
          />
        </>
        {item.title && (
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              padding: 16,
            }}
          >
            <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold', fontFamily: 'Poppins_600SemiBold' }}>
              {item.title}
            </Text>
            {item.subtitle && (
              <Text style={{ color: '#fff', fontSize: 14, fontFamily: 'Poppins_400Regular' }}>
                {item.subtitle}
              </Text>
            )}
          </LinearGradient>
        )}
      </TouchableOpacity>
    );
  };
  
  return (
    <View style={{ marginVertical: 16, marginHorizontal: 0, width: '100%' }}>
      <Text style={{ 
        color: '#fff', 
        fontSize: 18, 
        fontWeight: 'bold', 
        marginBottom: 10, 
        marginLeft: 16, 
        fontFamily: 'Poppins_600SemiBold' 
      }}>
        Featured Workouts
      </Text>
      <FlatList
        ref={flatListRef}
        data={images}
        renderItem={renderItem}
        keyExtractor={(item, index) => `slide-${index}`}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        contentContainerStyle={{ paddingRight: 16 }}
        getItemLayout={(data, index) => ({
          length: Dimensions.get('window').width - 32,
          offset: (Dimensions.get('window').width - 32) * index,
          index,
        })}
      />
      <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: 10 }}>
        {images.map((_, index) => (
          <View
            key={`dot-${index}`}
            style={{
              width: 8,
              height: 8,
              borderRadius: 4,
              backgroundColor: index === activeIndex ? '#667eea' : 'rgba(255,255,255,0.5)',
              marginHorizontal: 4,
            }}
          />
        ))}
      </View>
    </View>
  );
}

// WorkoutCalendar Component
function WorkoutCalendar({ userEmail }) {
  const router = useRouter();
  const [workoutDays, setWorkoutDays] = useState([]);
  const [streaks, setStreaks] = useState([]);
  const [selectedDay, setSelectedDay] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  
  useEffect(() => {
    async function fetchWorkoutDays() {
      try {
        // Try to get user-specific workout history
        let historyStr = null;
        if (userEmail) {
          historyStr = await AsyncStorage.getItem(`@workout_history:${userEmail}`);
        }
        
        // If no data, create sample data for demo
        if (!historyStr) {
          const sampleWorkouts = [];
          const today = new Date();
          
          // Create some sample workouts for the past 30 days
          for (let i = 0; i < 30; i++) {
            // Add a workout for approximately 40% of days
            if (Math.random() < 0.4) {
              const workoutDate = new Date();
              workoutDate.setDate(today.getDate() - i);
              
              // Random workout type
              const workoutTypes = ['Strength', 'Cardio', 'Yoga', 'HIIT', 'Flexibility'];
              const randomType = workoutTypes[Math.floor(Math.random() * workoutTypes.length)];
              
              // Random duration between 15-60 minutes
              const duration = Math.floor(Math.random() * 46) + 15;
              
              sampleWorkouts.push({
                date: workoutDate.toISOString(),
                type: randomType,
                duration: duration,
                completed: true,
                calories: Math.floor(Math.random() * 300) + 100,
              });
            }
          }
          
          // Save sample workouts to AsyncStorage
          await AsyncStorage.setItem(
            userEmail ? `@workout_history:${userEmail}` : '@workout_history',
            JSON.stringify(sampleWorkouts)
          );
          
          historyStr = JSON.stringify(sampleWorkouts);
        }
        // If no user-specific data, try general workout history
        if (!historyStr) {
          historyStr = await AsyncStorage.getItem('@workout_history');
        }
        
        let history = [];
        try { 
          history = historyStr ? JSON.parse(historyStr) : []; 
        } catch (error) {
          console.error('Error parsing workout history:', error);
          history = [];
        }
        
        // Map: {day, type, details}
        const days = history.map(w => {
          const date = w.date ? new Date(w.date) : null;
          return date ? { 
            day: date.getDate(), 
            month: date.getMonth(),
            year: date.getFullYear(),
            type: w.type || 'Other', 
            details: w 
          } : null;
        }).filter(Boolean);
        
        setWorkoutDays(days);
        
        // Calculate streaks
        const dayNums = days.map(d => d.day).sort((a, b) => a - b);
        let streakArr = [];
        let currentStreak = [];
        for (let i = 0; i < dayNums.length; i++) {
          if (i === 0 || dayNums[i] === dayNums[i - 1] + 1) {
            currentStreak.push(dayNums[i]);
          } else {
            if (currentStreak.length > 1) streakArr.push([...currentStreak]);
            currentStreak = [dayNums[i]];
          }
        }
        if (currentStreak.length > 1) streakArr.push([...currentStreak]);
        setStreaks(streakArr.flat());
      } catch (error) {
        console.error('Error fetching workout days:', error);
        setWorkoutDays([]);
        setStreaks([]);
      }
    }
    fetchWorkoutDays();
  }, [userEmail]);

  // Function to get days in month
  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };
  
  // Function to get day of week the month starts on (0 = Sunday, 1 = Monday, etc.)
  const getFirstDayOfMonth = (year, month) => {
    return new Date(year, month, 1).getDay();
  };
  
  // Function to navigate to previous month
  const goToPreviousMonth = () => {
    const previousMonth = new Date(currentMonth);
    previousMonth.setMonth(previousMonth.getMonth() - 1);
    setCurrentMonth(previousMonth);
  };
  
  // Function to navigate to next month
  const goToNextMonth = () => {
    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    setCurrentMonth(nextMonth);
  };
  
  // Function to add a new workout
  const addWorkout = async (workout) => {
    try {
      // Get existing workouts
      const historyKey = userEmail ? `@workout_history:${userEmail}` : '@workout_history';
      const historyStr = await AsyncStorage.getItem(historyKey);
      const history = historyStr ? JSON.parse(historyStr) : [];
      
      // Add new workout
      history.push(workout.details);
      
      // Save updated workouts
      await AsyncStorage.setItem(historyKey, JSON.stringify(history));
      
      // Update state
      setWorkoutDays([...workoutDays, workout]);
      
      // Close modal
      setModalVisible(false);
      
      // Show success message
      Alert.alert('Success', 'Workout added successfully!');
    } catch (error) {
      console.error('Error adding workout:', error);
      Alert.alert('Error', 'Failed to add workout');
    }
  };

  // Get current month days
  const year = currentMonth.getFullYear();
  const month = currentMonth.getMonth();
  const daysInMonth = getDaysInMonth(year, month);
  const firstDayOfMonth = getFirstDayOfMonth(year, month);
  const daysArr = Array.from({ length: daysInMonth }, (_, i) => i + 1);
  
  // Add empty cells for days before the first day of the month
  const emptyCells = Array.from({ length: firstDayOfMonth }, (_, i) => null);
  const calendarCells = [...emptyCells, ...daysArr];

  // Workout type to icon/color
  const typeMap = {
    Cardio: { icon: 'walk', color: '#4CAF50' },
    Strength: { icon: 'barbell', color: '#FF9800' },
    Yoga: { icon: 'leaf', color: '#9C27B0' },
    HIIT: { icon: 'flash', color: '#F44336' },
    Flexibility: { icon: 'body', color: '#2196F3' },
    Other: { icon: 'fitness', color: '#00BFFF' },
  };

  const getWorkoutForDay = (day) => {
    return workoutDays.find(d => 
      d.day === day && 
      d.month === currentMonth.getMonth() && 
      d.year === currentMonth.getFullYear()
    );
  };
  
  const isStreak = (day) => streaks.includes(day);
  const isToday = (day) => {
    const today = new Date();
    return day === today.getDate() && 
           currentMonth.getMonth() === today.getMonth() && 
           currentMonth.getFullYear() === today.getFullYear();
  };

  return (
    <View>
      {/* Month navigation */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <TouchableOpacity onPress={goToPreviousMonth}>
          <Ionicons name="chevron-back" size={24} color="#fff" />
        </TouchableOpacity>
        
        <Text style={{ color: '#fff', fontSize: 16, fontWeight: 'bold' }}>
          {currentMonth.toLocaleString('default', { month: 'long', year: 'numeric' })}
        </Text>
        
        <TouchableOpacity onPress={goToNextMonth}>
          <Ionicons name="chevron-forward" size={24} color="#fff" />
        </TouchableOpacity>
      </View>
      
      {/* Day of week headers */}
      <View style={{ flexDirection: 'row', marginBottom: 8 }}>
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <Text key={day} style={{ 
            flex: 1, 
            textAlign: 'center', 
            color: '#aaa', 
            fontSize: 12 
          }}>
            {day}
          </Text>
        ))}
      </View>
      
      {/* Calendar grid */}
      <View style={{ 
        flexDirection: 'row', 
        flexWrap: 'wrap', 
        justifyContent: 'flex-start',
      }}>
        {calendarCells.map((day, index) => {
          if (day === null) {
            return <View key={`empty-${index}`} style={{ width: '14.28%', height: 40 }} />;
          }
          
          const workout = getWorkoutForDay(day);
          const streak = isStreak(day);
          const today = isToday(day);
          
          return (
            <TouchableOpacity
              key={`day-${day}`}
              style={{
                width: '14.28%',
                height: 40,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: workout ? `${typeMap[workout.type]?.color}22` : streak ? 'rgba(102,126,234,0.15)' : 'transparent',
                borderWidth: today ? 1 : 0,
                borderColor: '#667eea',
                borderRadius: 20,
                margin: 0,
              }}
              onPress={() => {
                if (workout) {
                  setSelectedDay(workout);
                  setModalVisible(true);
                } else {
                  // Allow adding a workout for this day
                  const newWorkout = {
                    day: day,
                    month: currentMonth.getMonth(),
                    year: currentMonth.getFullYear(),
                    type: 'Other',
                    details: {
                      date: new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day).toISOString(),
                      duration: 30,
                      calories: 150,
                      exercises: ['Custom Workout'],
                      completed: false,
                      type: 'Other'
                    }
                  };
                  setSelectedDay(newWorkout);
                  setModalVisible(true);
                }
              }}
            >
              <Text style={{ 
                color: '#fff', 
                fontWeight: today ? 'bold' : 'normal' 
              }}>
                {day}
              </Text>
              {workout && (
                <View style={{ 
                  position: 'absolute', 
                  bottom: 2, 
                  width: 6, 
                  height: 6, 
                  borderRadius: 3, 
                  backgroundColor: typeMap[workout.type]?.color || '#667eea' 
                }} />
              )}
            </TouchableOpacity>
          );
        })}
      </View>
      
      {/* Legend */}
      <View style={{ marginTop: 16, flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'center' }}>
        {Object.entries(typeMap).map(([type, { color }]) => (
          <View key={type} style={{ flexDirection: 'row', alignItems: 'center', marginRight: 12, marginBottom: 8 }}>
            <View style={{ width: 10, height: 10, borderRadius: 5, backgroundColor: color, marginRight: 4 }} />
            <Text style={{ color: '#fff', fontSize: 12 }}>{type}</Text>
          </View>
        ))}
      </View>
      
      {streaks.length > 0 && (
        <View style={{ marginTop: 16, paddingHorizontal: 16 }}>
          <Text style={{ color: '#fff', fontSize: 14, marginBottom: 4 }}>Current Streak: {streaks.length} days</Text>
          <View style={{ height: 4, backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 2 }}>
            <View style={{ 
              height: '100%', 
              width: `${Math.min(streaks.length * 10, 100)}%`, 
              backgroundColor: '#667eea', 
              borderRadius: 2 
            }} />
          </View>
        </View>
      )}
      
      {/* Add workout button */}
      <TouchableOpacity
        style={{
          backgroundColor: '#667eea',
          borderRadius: 8,
          paddingVertical: 10,
          alignItems: 'center',
          marginTop: 16,
        }}
        onPress={() => {
          router.push('/ai-workout-generator');
        }}
      >
        <Text style={{ color: '#fff', fontWeight: 'bold' }}>Start New Workout</Text>
      </TouchableOpacity>
      
      {/* Workout details modal */}
      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', alignItems: 'center' }}>
          <View style={{ backgroundColor: '#222', borderRadius: 16, padding: 20, width: '80%' }}>
            {selectedDay && (
              <>
                <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold', marginBottom: 8 }}>
                  {new Date(selectedDay.year || currentMonth.getFullYear(), 
                           selectedDay.month || currentMonth.getMonth(), 
                           selectedDay.day).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
                </Text>
                <Text style={{ color: '#667eea', fontSize: 16, marginBottom: 16 }}>
                  {selectedDay.type} Workout
                </Text>
                <View style={{ marginBottom: 16 }}>
                  <Text style={{ color: '#fff', marginBottom: 4 }}>Duration: {selectedDay.details?.duration || 0} minutes</Text>
                  <Text style={{ color: '#fff', marginBottom: 4 }}>Calories: {selectedDay.details?.calories || 0} cal</Text>
                  {selectedDay.details?.exercises && (
                    <Text style={{ color: '#fff', marginBottom: 4 }}>
                      Exercises: {selectedDay.details.exercises.join(', ')}
                    </Text>
                  )}
                </View>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <TouchableOpacity
                    style={{ backgroundColor: '#667eea', borderRadius: 8, paddingVertical: 10, paddingHorizontal: 16 }}
                    onPress={() => setModalVisible(false)}
                  >
                    <Text style={{ color: '#fff', fontWeight: 'bold' }}>Close</Text>
                  </TouchableOpacity>
                  
                  {selectedDay.details && !selectedDay.details.completed && (
                    <TouchableOpacity
                      style={{ backgroundColor: '#4CAF50', borderRadius: 8, paddingVertical: 10, paddingHorizontal: 16 }}
                      onPress={() => {
                        // Mark workout as completed or add new workout
                        const updatedWorkout = {
                          ...selectedDay,
                          details: {
                            ...selectedDay.details,
                            completed: true
                          }
                        };
                        addWorkout(updatedWorkout);
                      }}
                    >
                      <Text style={{ color: '#fff', fontWeight: 'bold' }}>
                        {selectedDay.details.completed ? 'Completed' : 'Complete'}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}
import FavoritesPage from './favorites';
import AdvancedParticle from '../components/AdvancedParticle';

SplashScreen.preventAutoHideAsync();

const { width, height } = Dimensions.get('window');

const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

// Add FloatingShape for animated background
const FloatingShape = ({ style, delay = 0, rotationSpeed = 1 }) => {
  const floatAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
        Animated.timing(floatAnim, {
          toValue: 0,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
    Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 15000 * rotationSpeed,
        useNativeDriver: true,
      })
    ).start();
    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [
            {
              translateY: floatAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -30],
              }),
            },
            {
              rotate: rotationAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '360deg'],
              }),
            },
            {
              scale: scaleAnim,
            },
          ],
        },
      ]}
    />
  );
};

// Update: Utility to get current user from AsyncStorage
const getCurrentUser = async () => {
  // Try to get the current user email or username from AsyncStorage
  let email = await AsyncStorage.getItem('@current_user');
  if (!email) email = await AsyncStorage.getItem('@FitnessApp:currentUser');
  if (email && email.includes('@')) return email;
  // fallback: try to get from user context
  return null;
};

// Define a custom hook to encapsulate all state and refs

function useHomeState() {
  const router = useRouter();
  const { currentUser: user, loading: userLoading, logoutUser } = useUser();
  const [username, setUsername] = useState('User');
  const [loading, setLoading] = useState(true);
  const [menuVisible, setMenuVisible] = useState(false);
  const [dietPlans, setDietPlans] = useState([]);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [stepCount, setStepCount] = useState(0);
  
  // Image slider data with fallback to ensure images are always available
  const [sliderImages, setSliderImages] = useState([
    {
      // Use require with fallback to ensure images are always available
      image: { uri: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80' },
      title: 'Summer Body Challenge',
      subtitle: 'Get fit for summer with our 30-day program',
      onPress: () => router.push('/ai-workout-generator')
    },
    {
      image: { uri: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80' },
      title: 'Personalized AI Workouts',
      subtitle: 'Let our AI create the perfect workout for you',
      onPress: () => router.push('/ai-workout-generator')
    },
    {
      image: { uri: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80' },
      title: 'Join Live Classes',
      subtitle: 'Connect with trainers and workout together',
      onPress: () => router.push('/virtual-classes')
    }
  ]);

  // Define all state in one place
  const [userData, setUserData] = useState({
    favoriteExercise: 'Running',
    totalSteps: 0,
    caloriesBurned: 0,
    dailyTraining: '30 min Cardio',
    challenges: [
      {
        id: 1,
        title: '7-Day Streak',
        description: 'Complete workouts for 7 consecutive days',
        progress: 3,
        total: 7,
        type: 'streak'
      },
      {
        id: 2,
        title: 'Burn 500 Cal',
        description: 'Burn 500 calories in a single workout',
        progress: 350,
        total: 500,
        type: 'calories'
      },
      {
        id: 3,
        title: 'Complete 5 Workouts',
        description: 'Finish 5 different workout sessions',
        progress: 2,
        total: 5,
        type: 'workouts'
      }
    ],
    waterIntake: 0,
    sleepHours: 0,
    selectedPlan: null,
    lastUpdated: new Date().toISOString(),
    waterGoal: 8,
    sleepGoal: 8,
    stepGoal: 10000,
    calorieGoal: 500,
    isTraining: false,
    trainingStartTime: null,
    trainingDuration: 0,
    // Enhanced tracking data
    distance: 0,
    activeMinutes: 0,
    heartRate: 0,
    weeklyWorkouts: 0,
    monthlyGoals: {
      workouts: 20,
      steps: 300000,
      calories: 15000,
      water: 240 // glasses
    }
  });

  // Load fonts
  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  // Progress state
  const [progress, setProgress] = useState({
    Strength: 0,
    Cardio: 0,
    Yoga: 0,
  });

  // Define ALL refs at the top level
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const trainingIntervalRef = useRef(null);
  const trackerScrollX = useRef(new Animated.Value(0)).current;
  const lottieRef = useRef(null);
  const scrollViewRef = useRef(null);
  const animationRef = useRef(null);
  const menuRef = useRef(null);
  const tabViewRef = useRef(null);

  // Error boundary for logout
  const [logoutError, setLogoutError] = useState(null);
  const [logoutLoading, setLogoutLoading] = useState(false);

  // Tab and badge state
  const [favoritesBadge, setFavoritesBadge] = useState(3);
  const iconScales = useRef([1, 2, 3, 4, 5].map(() => new RNAnimated.Value(1))).current;
  
  // Add state for manual input modals
  const [editModal, setEditModal] = useState({ open: false, type: null });
  const [manualValue, setManualValue] = useState('');

  const [healthTab, setHealthTab] = useState('Heart rate');
  const [healthMetrics, setHealthMetrics] = useState([
    {
      key: 'Walk',
      label: 'Walk',
      icon: 'footsteps',
      color: '#4CAF50',
      value: userData.totalSteps || 0,
      unit: 'steps',
      max: userData.stepGoal || 10000,
    },
    {
      key: 'Sleep',
      label: 'Sleep',
      icon: 'moon',
      color: '#9C27B0',
      value: userData.sleepHours || 0,
      unit: 'hrs',
      max: userData.sleepGoal || 8,
    },
    {
      key: 'Heart rate',
      label: 'Heart rate',
      icon: 'heart',
      color: '#F44336',
      value: userData.heartRate || 72,
      unit: 'bpm',
      max: 180,
      min: 60,
      avg: 72,
      maxVal: 80,
    },
    {
      key: 'Blood pressure',
      label: 'Blood pressure',
      icon: 'fitness',
      color: '#2196F3',
      value: userData.bloodPressure || '120/80',
      unit: 'mmHg',
      max: 140,
      extra: {
        bloodOxygen: userData.bloodOxygen || 98,
        fatigue: userData.fatigue || 'Low'
      },
    },
    {
      key: 'Exercise',
      label: 'Exercise',
      icon: 'barbell',
      color: '#FF9800',
      value: userData.distance || 0,
      unit: 'km',
      max: 10,
      calories: userData.caloriesBurned || 0,
      pace: userData.pace || '0.0 min/km',
    },
  ]);
  
  // Function to update health metrics locally
  const setHealthMetricsLocal = (updatedMetrics) => {
    setHealthMetrics(updatedMetrics);
  };
  
  // Cleanup function to stop tracking when component unmounts
  React.useEffect(() => {
    return () => {
      if (trainingIntervalRef.current) {
        if (typeof trainingIntervalRef.current === 'function') {
          // It's a subscription
          trainingIntervalRef.current();
        } else if (typeof trainingIntervalRef.current === 'number') {
          // It's an interval ID
          clearInterval(trainingIntervalRef.current);
        }
      }
    };
  }, []);
  
  return {
    router,
    user,
    userLoading,
    logoutUser,
    username,
    setUsername,
    loading,
    setLoading,
    menuVisible,
    setMenuVisible,
    dietPlans,
    setDietPlans,
    selectedPlan,
    setSelectedPlan,
    stepCount,
    setStepCount,
    userData,
    setUserData,
    fontsLoaded,
    progress,
    setProgress,
    fadeAnim,
    scaleAnim,
    trainingIntervalRef,
    trackerScrollX,
    lottieRef,
    scrollViewRef,
    animationRef,
    menuRef,
    tabViewRef,
    logoutError,
    setLogoutError,
    logoutLoading,
    setLogoutLoading,
    favoritesBadge,
    setFavoritesBadge,
    iconScales,
    editModal,
    setEditModal,
    manualValue,
    setManualValue,
    healthTab,
    setHealthTab,
    healthMetrics,
    setHealthMetrics,
    sliderImages,
    setSliderImages,
  };
}

export default function Home() {
  // Use our custom hook to get all state and refs
  const {
    router,
    user,
    userLoading,
    logoutUser,
    username,
    setUsername,
    loading,
    setLoading,
    menuVisible,
    setMenuVisible,
    dietPlans,
    setDietPlans,
    selectedPlan,
    setSelectedPlan,
    stepCount,
    setStepCount,
    userData,
    setUserData,
    fontsLoaded,
    progress,
    setProgress,
    fadeAnim,
    scaleAnim,
    trainingIntervalRef,
    trackerScrollX,
    lottieRef,
    scrollViewRef,
    animationRef,
    menuRef,
    tabViewRef,
    logoutError,
    setLogoutError,
    logoutLoading,
    setLogoutLoading,
    favoritesBadge,
    setFavoritesBadge,
    iconScales,
    editModal,
    setEditModal,
    manualValue,
    setManualValue,
    healthTab,
    setHealthTab,
    healthMetrics,
    setHealthMetrics,
    sliderImages,
    setSliderImages,
  } = useHomeState();

  // Add setHealthMetrics if it's not already included
  const setHealthMetricsLocal = (metrics) => {
    if (setHealthMetrics) {
      setHealthMetrics(metrics);
    } else {
      console.warn('setHealthMetrics is not available');
    }
  };

  // Tab navigation logic - moved inside the main component
  const layout = useWindowDimensions();
  const [tabIndex, setTabIndex] = useState(0);
  
  // Define tab routes - not using hooks inside this definition
  // Using constants to avoid any potential hooks issues
  const tabRoutes = [
    { key: 'realtime', title: 'RealTime', icon: 'pulse', component: RealTimeTracker },
    { key: 'gps', title: 'GPS', icon: 'navigate', component: GPSTracker },
    { key: 'sleep', title: 'Sleep', icon: 'moon', component: SleepTracker },
    { key: 'steps', title: 'Steps', icon: 'walk', component: StepCounter },
    { key: 'favorites', title: 'Favorites', icon: 'star', component: null }, // We'll handle this component conditionally
  ];
  
  // Define renderScene function for TabView
  const renderScene = ({ route }) => {
    // If component is null (like for Favorites tab), render a placeholder
    if (!route.component) {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'transparent', paddingHorizontal: 10, minHeight: 500 }}>
          <Text style={{ color: '#fff', fontSize: 18, fontFamily: 'Poppins_600SemiBold', textAlign: 'center' }}>
            {route.key === 'favorites' ? 'Favorites Coming Soon' : 'Component Not Available'}
          </Text>
          <TouchableOpacity 
            style={{ marginTop: 20, backgroundColor: '#667eea', padding: 12, borderRadius: 8 }}
            onPress={() => router.push('/favorites')}
          >
            <Text style={{ color: '#fff', fontFamily: 'Poppins_600SemiBold' }}>View All Favorites</Text>
          </TouchableOpacity>
        </View>
      );
    }
    
    // Otherwise render the component
    const Component = route.component;
    return (
      <View style={{ flex: 1, backgroundColor: 'transparent', paddingHorizontal: 10, minHeight: 500 }}>
        <Component 
          userEmail={user?.email} 
          userName={username}
          userData={userData}
          updateActivity={updateActivity}
          updateSteps={updateSteps}
          updateWaterIntake={updateWaterIntake}
          updateSleepHours={updateSleepHours}
          updateChallengeProgress={updateChallengeProgress}
        />
      </View>
    );
  };
  
  // Define handleTabPress function
  const handleTabPress = (i) => {
    setTabIndex(i);
    iconScales.forEach((scale, idx) => {
      RNAnimated.spring(scale, {
        toValue: idx === i ? 1.2 : 1,
        useNativeDriver: true,
      }).start();
    });
  };
  
  // Define handleLogout function - this is the main logout function
  const handleLogout = async () => {
    setLogoutError(null);
    setLogoutLoading(true);
    try {
      setMenuVisible(false);
      // Get current user email before logout
      const userEmail = user?.email || null;
      // Call context logout function
      if (logoutUser) await logoutUser();
      if (userEmail) {
        // Remove all user-specific data
        const keys = await AsyncStorage.getAllKeys();
        const userKeys = keys.filter(key => 
          key.includes(`@user_data:${userEmail}`) || 
          key.includes(`@user_profile:${userEmail}`) || 
          key.includes(`@userHistory:${userEmail}`) ||
          key.includes(`@challenges:${userEmail}`) ||
          key.includes(`@training_history:${userEmail}`) ||
          key.includes(`@progress:${userEmail}`)
        );
        if (userKeys.length > 0) {
          await AsyncStorage.multiRemove(userKeys);
        }
      }
      // Remove shared user data
      await AsyncStorage.removeItem('@current_user');
      await AsyncStorage.removeItem('@FitnessApp:currentUser');
      setLogoutLoading(false);
      router.replace('/user/login');
    } catch (error) {
      setLogoutLoading(false);
      setLogoutError('Logout failed. Please try again.');
      console.error('Error during logout:', error);
      // Fallback logout
      await AsyncStorage.removeItem('@current_user');
      await AsyncStorage.removeItem('@FitnessApp:currentUser');
      router.replace('/user/login');
    }
  };
  
  const renderTabBar = () => (
    <View style={styles.tabBarContainer}>
      {tabRoutes.map((route, i) => {
        const focused = i === tabIndex;
        return (
          <TouchableOpacity
            key={route.key}
            style={[styles.tabBarItem, focused && styles.tabBarItemActive]}
            onPress={() => handleTabPress(i)}
            activeOpacity={0.8}
            accessible={true}
            accessibilityLabel={`${route.title} tab`}
            accessibilityRole="tab"
            accessibilityState={{ selected: focused }}
          >
            <RNAnimated.View style={{ transform: [{ scale: iconScales[i] }] }}>
              <Ionicons name={route.icon} size={24} color={focused ? '#667eea' : '#fff'} />
              {route.key === 'favorites' && favoritesBadge > 0 && (
                <View style={styles.tabBadge}>
                  <Text style={styles.tabBadgeText}>{favoritesBadge}</Text>
                </View>
              )}
            </RNAnimated.View>
            <Text style={[styles.tabBarLabel, focused && styles.tabBarLabelActive]}>{route.title}</Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );

  // Fix quickActions to only show four buttons and remove 'Start'
  const quickActions = [
    { icon: 'barbell', label: 'AI Workout', onPress: () => router.push('/ai-workout-generator') },
    { icon: 'tv-outline', label: 'Virtual Classes', onPress: () => router.push('/virtual-classes') },
    { icon: 'trophy-outline', label: 'Achievements', onPress: () => router.push('/nft-achievements') },
    { icon: 'earth', label: 'Metaverse', onPress: () => router.push('/social-metaverse') },
  ];

  // Define the bottomNav array for the bottom navigation bar:
  const bottomNav = [
    { icon: 'barbell-outline', label: 'AI Workout', onPress: () => router.push('/ai-workout-generator'), iconLib: Ionicons },
    { icon: 'restaurant-outline', label: 'Diet Plans', onPress: () => router.push('/plans'), iconLib: Ionicons },
    { icon: 'analytics-outline', label: 'Daily Report', onPress: () => router.push('/analyze'), iconLib: Ionicons },
    { icon: 'trophy-outline', label: 'Achievements', onPress: () => router.push('/nft-achievements'), iconLib: Ionicons },
  ];

  // Menubar modal items (with icons and navigation)
  const menuModalItems = [
    { icon: 'person-outline', label: 'Profile', onPress: () => router.push('/profile/user-profile'), color: '#667eea', iconLib: Ionicons },
    { icon: 'notifications-outline', label: 'Notifications', onPress: () => router.push('/notifications'), color: '#FF6B6B' },
    { icon: 'star-outline', label: 'Favorites', onPress: () => router.push('/favorites'), color: '#FFD700' },
    { icon: 'robot', label: 'AI Coach', onPress: () => router.push('/ai-coach'), color: '#00BFFF', iconLib: MaterialCommunityIcons },
    { icon: 'barbell-outline', label: 'Workouts', onPress: () => router.push('/ai-workout-generator'), color: '#764ba2', iconLib: Ionicons },
    { icon: 'trophy-outline', label: 'Achievements', onPress: () => router.push('/nft-achievements'), color: '#f093fb', iconLib: Ionicons },
    { icon: 'earth', label: 'Social Metaverse', onPress: () => router.push('/social-metaverse'), color: '#4CAF50', iconLib: Ionicons },
    { icon: 'restaurant-outline', label: 'Diet Plans', onPress: () => router.push('/plans'), color: '#FF9800', iconLib: Ionicons },
    { icon: 'tv-outline', label: 'Classes', onPress: () => router.push('/virtual-classes'), color: '#00BFFF', iconLib: Ionicons },
    { icon: 'analytics-outline', label: 'Analytics', onPress: () => router.push('/analyze'), color: '#45B7D1', iconLib: Ionicons },
    { icon: 'log-out-outline', label: 'Logout', onPress: handleLogout, color: '#FF5252', iconLib: Ionicons },
  ];

  // Helper styles:
  const quickActionStyle = { backgroundColor: 'rgba(255,255,255,0.08)', borderRadius: 12, padding: 12, marginHorizontal: 4, minWidth: 60, alignItems: 'center' };
  const quickActionText = { color: '#fff', fontFamily: 'Poppins_600SemiBold', fontSize: 13, marginTop: 8, textAlign: 'center', letterSpacing: 0.3 };
  const navText = { color: '#fff', fontFamily: 'Poppins_600SemiBold', fontSize: 13 };

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync();
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 700,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          friction: 5,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [fontsLoaded]);

  useEffect(() => {
    if (!userLoading) {
      if (!user) {
        router.replace('/user/login');
      } else {
        // Set username from current user
        console.log('Homepage: Setting username from user:', user.name);
        setUsername(user.name || 'User');
        fetchDietPlans();
        fetchUserData();
      }
    }
  }, [user, userLoading]);

  // Fix: Always load username for the current user only
  useEffect(() => {
    const loadUsername = async () => {
      try {
        let email = await getCurrentUser();
        if (email) {
          const userDataString = await AsyncStorage.getItem(`@user_data:${email}`);
          if (userDataString) {
            const userData = JSON.parse(userDataString);
            setUsername(userData.name || 'User');
            return;
          }
        }
      } catch {}
      setUsername('User');
    };
    loadUsername();
  }, []);

  const fetchDietPlans = async () => {
    try {
      setLoading(true);
      const plansString = await AsyncStorage.getItem('@diet_plans');
      if (plansString) {
        const plans = JSON.parse(plansString);
        setDietPlans(plans);
      }
    } catch (error) {
      console.error('Error fetching diet plans:', error);
      Alert.alert('Error', 'Failed to fetch diet plans');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserData = async () => {
    try {
      setLoading(true);
      
      // First, try to get current logged in user
      const currentUserEmail = await AsyncStorage.getItem('@current_user');
      if (currentUserEmail) {
        console.log('Fetching data for current user:', currentUserEmail);
        
        // Get user data from AsyncStorage
        const currentUserData = await AsyncStorage.getItem(`@user_data:${currentUserEmail}`);
        if (currentUserData) {
          const currentUser = JSON.parse(currentUserData);
          console.log('Current user data found:', currentUser);
          
          // Set username from user data
          const displayName = currentUser.name || 'User';
          setUsername(displayName);
          
          // Update user data state with all available properties
          setUserData(prev => ({
            ...prev,
            favoriteExercise: currentUser.favoriteExercise || prev.favoriteExercise,
            totalSteps: currentUser.totalSteps || prev.totalSteps,
            caloriesBurned: currentUser.caloriesBurned || prev.caloriesBurned,
            waterIntake: currentUser.waterIntake || prev.waterIntake,
            sleepHours: currentUser.sleepHours || prev.sleepHours,
            selectedPlan: currentUser.selectedPlan || prev.selectedPlan,
            distance: currentUser.distance || prev.distance,
            activeMinutes: currentUser.activeMinutes || prev.activeMinutes,
            heartRate: currentUser.heartRate || prev.heartRate,
            weeklyWorkouts: currentUser.weeklyWorkouts || prev.weeklyWorkouts,
            challenges: currentUser.challenges || prev.challenges
          }));
          
          // Also store the complete user data for profile access
          const userDataKey = `@user_data:${currentUserEmail}`;
          await AsyncStorage.setItem(userDataKey, JSON.stringify(currentUser));
          
          // Fetch today's stats
          await fetchTodayStats();
          return;
        }
      }

      // Fallback: Get all keys from AsyncStorage
      const keys = await AsyncStorage.getAllKeys();
      const userDataKeys = keys.filter(key => key.startsWith('@user_data:'));
      
      if (userDataKeys.length > 0) {
        // Get the most recent user data
        const mostRecentKey = userDataKeys[userDataKeys.length - 1];
        const userDataString = await AsyncStorage.getItem(mostRecentKey);
        if (userDataString) {
          const userData = JSON.parse(userDataString);
          console.log('Most recent user data found:', userData);
          
          const displayName = userData.name || 'User';
          setUsername(displayName);
          setUserData(prev => ({
            ...prev,
            favoriteExercise: userData.favoriteExercise || prev.favoriteExercise,
            totalSteps: userData.totalSteps || prev.totalSteps,
            caloriesBurned: userData.caloriesBurned || prev.caloriesBurned,
            waterIntake: userData.waterIntake || prev.waterIntake,
            sleepHours: userData.sleepHours || prev.sleepHours,
            selectedPlan: userData.selectedPlan || prev.selectedPlan,
            distance: userData.distance || prev.distance,
            activeMinutes: userData.activeMinutes || prev.activeMinutes,
            heartRate: userData.heartRate || prev.heartRate,
            weeklyWorkouts: userData.weeklyWorkouts || prev.weeklyWorkouts,
            challenges: userData.challenges || prev.challenges
          }));
        }
      }
      
      // Fetch today's stats regardless
      await fetchTodayStats();
    } catch (error) {
      console.error('Error fetching user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStrengthExercisesClick = () => {
    router.push('/strength-exercises');
  };
  
  // handleLogout function is already defined above

  const handleNavigate = (screen) => {
    setMenuVisible(false);
    
    switch(screen) {
      case 'Settings':
        router.push('/settings');
        break;
      case 'Notifications':
        router.push('/notifications');
        break;
      case 'Chat':
        router.push('/ai-coach'); // Navigate to AI chat
        break;
      case 'Favorites':
        router.push('/favorites');
        break;
      case 'Profile':
        router.push('/profile/user-profile');
        break;
      case 'Analytics':
        router.push('/analyze');
        break;
      case 'Plans':
        router.push('/plans');
        break;
      default:
        router.push(`/${screen.toLowerCase()}`);
    }
  };

  const handlePlanSelect = async (plan) => {
    try {
      if (!user) {
        Alert.alert('Error', 'Please login to select a plan');
        router.replace('/user/login');
        return;
      }

      const historyKey = `@user_history:${user.email}`;
      const historyString = await AsyncStorage.getItem(historyKey);
      const history = historyString ? JSON.parse(historyString) : [];

      // Update previous active plan to inactive
      const updatedHistory = history.map(item => ({
        ...item,
        status: 'inactive'
      }));

      // Add new plan
      updatedHistory.push({
        plan,
        selectedAt: new Date().toISOString(),
        status: 'active'
      });

      await AsyncStorage.setItem(historyKey, JSON.stringify(updatedHistory));
      setSelectedPlan(plan);
      Alert.alert('Success', 'Plan selected successfully!');
    } catch (error) {
      console.error('Error selecting plan:', error);
      Alert.alert('Error', 'Failed to select plan');
    }
  };

  const fetchUserPlans = async () => {
    try {
      if (!user) return;

      const historyKey = `@user_history:${user.email}`;
      const historyString = await AsyncStorage.getItem(historyKey);
      if (historyString) {
        const history = JSON.parse(historyString);
        const activePlan = history.find(item => item.status === 'active');
        if (activePlan) {
          setSelectedPlan(activePlan.plan);
        }
      }
    } catch (error) {
      console.error('Error fetching user plans:', error);
    }
  };

  const handleViewPlans = () => {
    if (!user) {
      Alert.alert('Error', 'Please login to view plans');
      router.replace('/user/login');
      return;
    }
    router.push('/plans');
  };

  // Add missing navigation functions
  const handleAnalyze = () => {
    console.log('Analyze button clicked');
    console.log('Current user:', user);
    
    if (!user) {
      Alert.alert('Error', 'Please login to view analytics');
      router.replace('/user/login');
      return;
    }
    
    console.log('Navigating to analyze with email:', user.email);
    // Navigate to analyze page with user data
    router.push({
      pathname: '/analyze',
      params: { email: user.email }
    });
  };

  const handleStartWorkout = () => {
    console.log('Navigating to Home workout page');
    router.push('/Home');
  };

  // Handle health metric press
  const handleHealthMetricPress = (metric) => {
    console.log('Health metric pressed:', metric);
    
    // Handle different metrics with simplified functionality
    switch (metric) {
      case 'Walk':
        // Start step tracking with simplified approach
        Alert.alert(
          "Start Step Tracking",
          "Step tracking will begin. You can manually update your step count or let the app track automatically.",
          [
            { text: "Cancel", style: "cancel" },
            { 
              text: "Start Tracking", 
              onPress: () => {
                // Clear any existing interval
                if (trainingIntervalRef.current) {
                  clearInterval(trainingIntervalRef.current);
                }
                
                // Start simulated step tracking
                const interval = setInterval(() => {
                  // Simulate 5-15 steps every 10 seconds
                  const newSteps = Math.floor(Math.random() * 10) + 5;
                  setStepCount(prevCount => prevCount + newSteps);
                  
                  // Update user data
                  setUserData(prev => ({
                    ...prev,
                    totalSteps: (prev.totalSteps || 0) + newSteps
                  }));
                  
                  // Save to AsyncStorage
                  if (user?.email) {
                    AsyncStorage.setItem(`@steps:${user.email}:${new Date().toISOString().split('T')[0]}`, 
                      ((userData.totalSteps || 0) + newSteps).toString());
                  }
                }, 10000);
                
                trainingIntervalRef.current = interval;
                
                // Navigate to step counter tab
                handleTabPress(3);
                
                Alert.alert(
                  "Step Tracking Started", 
                  "Your steps are now being tracked. The app will automatically count steps every 10 seconds."
                );
              }
            }
          ]
        );
        break;
        
      case 'Sleep':
        // Start sleep tracking with simplified approach
        Alert.alert(
          "Start Sleep Tracking",
          "Sleep tracking will begin. The app will monitor your sleep duration and quality.",
          [
            { text: "Cancel", style: "cancel" },
            { 
              text: "Start Tracking", 
              onPress: () => {
                // Clear any existing interval
                if (trainingIntervalRef.current) {
                  clearInterval(trainingIntervalRef.current);
                }
                
                // Initialize sleep tracking
                const sleepStartTime = new Date();
                const sleepData = {
                  startTime: sleepStartTime.toISOString(),
                  duration: 0,
                  inProgress: true
                };
                
                // Save initial sleep data
                AsyncStorage.setItem(
                  `@sleep_tracking:${user?.email || 'user'}:current`,
                  JSON.stringify(sleepData)
                );
                
                // Start sleep tracking simulation
                const sleepInterval = setInterval(() => {
                  const now = new Date();
                  const durationMinutes = Math.floor((now - sleepStartTime) / 60000);
                  const durationHours = Math.floor(durationMinutes / 60);
                  const remainingMinutes = durationMinutes % 60;
                  
                  // Update sleep data
                  sleepData.duration = durationMinutes;
                  
                  AsyncStorage.setItem(
                    `@sleep_tracking:${user?.email || 'user'}:data`,
                    JSON.stringify(sleepData)
                  );
                  
                  // Update user data
                  setUserData(prev => ({
                    ...prev,
                    sleepHours: durationHours + (remainingMinutes / 60)
                  }));
                  
                }, 60000); // Update every minute
                
                trainingIntervalRef.current = sleepInterval;
                
                // Navigate to sleep tracker
                handleTabPress(2);
                
                Alert.alert(
                  "Sleep Tracking Started", 
                  "Your sleep is now being tracked. The app will monitor your sleep duration automatically."
                );
              }
            }
          ]
        );
        break;
        
      case 'Heart rate':
        // Start heart rate monitoring with simplified approach
        Alert.alert(
          "Start Heart Rate Monitoring",
          "Heart rate monitoring will begin. The app will simulate realistic heart rate readings.",
          [
            { text: "Cancel", style: "cancel" },
            { 
              text: "Start Monitoring", 
              onPress: () => {
                // Clear any existing interval
                if (trainingIntervalRef.current) {
                  clearInterval(trainingIntervalRef.current);
                }
                
                // Start heart rate monitoring simulation
                let baseHeartRate = Math.floor(Math.random() * 10) + 70; // Base rate between 70-80
                let min = baseHeartRate - 5;
                let max = baseHeartRate + 5;
                let readings = [];
                
                const heartRateInterval = setInterval(() => {
                  // Generate realistic heart rate with small variations
                  const variation = Math.sin(Date.now() / 10000) * 3;
                  const noise = (Math.random() - 0.5) * 2;
                  const newHeartRate = Math.round(baseHeartRate + variation + noise);
                  
                  // Keep track of min/max/avg
                  readings.push(newHeartRate);
                  if (readings.length > 20) readings.shift();
                  
                  min = Math.min(min, newHeartRate);
                  max = Math.max(max, newHeartRate);
                  const avg = Math.round(readings.reduce((sum, val) => sum + val, 0) / readings.length);
                  
                  // Update user data
                  setUserData(prev => ({
                    ...prev,
                    heartRate: newHeartRate
                  }));
                  
                  // Save heart rate data to AsyncStorage
                  const hrData = {
                    timestamp: new Date().toISOString(),
                    value: newHeartRate,
                    min,
                    max,
                    avg
                  };
                  
                  AsyncStorage.getItem(`@heart_rate:${user?.email || 'user'}`)
                    .then(data => {
                      const history = data ? JSON.parse(data) : [];
                      history.push(hrData);
                      if (history.length > 100) history.shift();
                      return AsyncStorage.setItem(
                        `@heart_rate:${user?.email || 'user'}`,
                        JSON.stringify(history)
                      );
                    })
                    .catch(err => console.error('Error saving heart rate data:', err));
                  
                }, 2000); // Update every 2 seconds
                
                trainingIntervalRef.current = heartRateInterval;
                
                // Navigate to heart rate monitor
                handleTabPress(0);
                
                Alert.alert(
                  "Heart Rate Monitoring Started", 
                  "Your heart rate is now being monitored. The app will provide realistic readings every 2 seconds."
                );
              }
            }
          ]
        );
        break;
        
      case 'Blood pressure':
        // Start blood pressure monitoring with simplified approach
        Alert.alert(
          "Start Blood Pressure Monitoring",
          "Blood pressure monitoring will begin. The app will simulate realistic readings.",
          [
            { text: "Cancel", style: "cancel" },
            { 
              text: "Start Monitoring", 
              onPress: () => {
                // Clear any existing interval
                if (trainingIntervalRef.current) {
                  clearInterval(trainingIntervalRef.current);
                }
                
                // Generate realistic blood pressure values
                const userAge = userData.age || 30;
                const userFitness = userData.fitnessLevel || 'Intermediate';
                
                // Base values
                let baseSystolic = 120;
                let baseDiastolic = 80;
                
                // Adjust for age
                if (userAge > 40) {
                  baseSystolic += Math.floor((userAge - 40) / 5) * 2;
                  baseDiastolic += Math.floor((userAge - 40) / 10) * 2;
                }
                
                // Adjust for fitness level
                if (userFitness === 'Beginner') {
                  baseSystolic += 5;
                  baseDiastolic += 3;
                } else if (userFitness === 'Advanced') {
                  baseSystolic -= 5;
                  baseDiastolic -= 3;
                }
                
                // Add some random variation
                const systolic = baseSystolic + Math.floor(Math.random() * 10) - 5;
                const diastolic = baseDiastolic + Math.floor(Math.random() * 8) - 4;
                const bloodPressure = `${systolic}/${diastolic}`;
                
                // Update user data
                setUserData(prev => ({
                  ...prev,
                  bloodPressure: bloodPressure
                }));
                
                // Save blood pressure data
                const bpData = {
                  timestamp: new Date().toISOString(),
                  systolic,
                  diastolic,
                  bloodPressure
                };
                
                AsyncStorage.setItem(
                  `@blood_pressure:${user?.email || 'user'}`,
                  JSON.stringify(bpData)
                );
                
                // Navigate to blood pressure tab
                handleTabPress(0);
                
                Alert.alert(
                  "Blood Pressure Reading Complete", 
                  `Your blood pressure reading: ${bloodPressure} mmHg\n\nThis is a simulated reading for demonstration purposes.`
                );
              }
            }
          ]
        );
        break;
        
      case 'Exercise':
        // Start exercise tracking
        Alert.alert(
          "Start Exercise Tracking",
          "Exercise tracking will begin. The app will monitor your workout session.",
          [
            { text: "Cancel", style: "cancel" },
            { 
              text: "Start Tracking", 
              onPress: () => {
                // Clear any existing interval
                if (trainingIntervalRef.current) {
                  clearInterval(trainingIntervalRef.current);
                }
                
                // Start exercise tracking simulation
                let exerciseDuration = 0;
                let caloriesBurned = 0;
                
                const exerciseInterval = setInterval(() => {
                  exerciseDuration += 1; // 1 minute
                  caloriesBurned += Math.floor(Math.random() * 5) + 3; // 3-8 calories per minute
                  
                  // Update user data
                  setUserData(prev => ({
                    ...prev,
                    exerciseDuration: exerciseDuration,
                    caloriesBurned: (prev.caloriesBurned || 0) + Math.floor(Math.random() * 5) + 3
                  }));
                  
                  // Save exercise data
                  const exerciseData = {
                    timestamp: new Date().toISOString(),
                    duration: exerciseDuration,
                    caloriesBurned: caloriesBurned
                  };
                  
                  AsyncStorage.setItem(
                    `@exercise:${user?.email || 'user'}`,
                    JSON.stringify(exerciseData)
                  );
                  
                }, 60000); // Update every minute
                
                trainingIntervalRef.current = exerciseInterval;
                
                // Navigate to exercise tab
                handleTabPress(0);
                
                Alert.alert(
                  "Exercise Tracking Started", 
                  "Your exercise session is now being tracked. The app will monitor your workout duration and calories burned."
                );
              }
            }
          ]
        );
        break;
        
      case 'Blood pressure':
        Alert.alert(
          "Start Blood Pressure Monitoring",
          "This will simulate connecting to a blood pressure monitor device.",
          [
            { text: "Cancel", style: "cancel" },
            { 
              text: "Start Monitoring", 
              onPress: async () => {
                try {
                  // Simulate searching for devices
                  Alert.alert(
                    "Searching for Devices", 
                    "Looking for nearby blood pressure monitors...",
                    [{ text: "Cancel", style: "cancel" }]
                  );
                  
                  // Simulate finding a device after 3 seconds
                  setTimeout(() => {
                    Alert.alert(
                      "Device Found",
                      "Blood Pressure Monitor XYZ-100 found. Starting measurement...",
                      [
                        { text: "Cancel", style: "cancel" },
                        { 
                          text: "Continue", 
                          onPress: () => {
                            // Simulate measurement process
                            setTimeout(() => {
                              // Generate realistic blood pressure values
                              // Normal range: 90-120 (systolic) / 60-80 (diastolic)
                              const systolic = Math.floor(Math.random() * 40) + 90;
                              const diastolic = Math.floor(Math.random() * 30) + 60;
                              const bloodPressure = `${systolic}/${diastolic}`;
                              
                              // Calculate blood oxygen level (95-100% for healthy individuals)
                              const bloodOxygen = Math.floor(Math.random() * 5) + 95;
                              
                              // Calculate fatigue level (Low, Medium, High)
                              const fatigueOptions = ['Low', 'Medium', 'High'];
                              const fatigueIndex = Math.floor(Math.random() * 3);
                              const fatigue = fatigueOptions[fatigueIndex];
                              
                              // Update state
                              setUserData(prev => ({
                                ...prev,
                                bloodPressure,
                                bloodOxygen,
                                fatigue
                              }));
                              
                              // Update health metrics
                              const updatedMetrics = [...healthMetrics];
                              updatedMetrics[3].value = bloodPressure;
                              updatedMetrics[3].extra = {
                                bloodOxygen,
                                fatigue
                              };
                              setHealthMetricsLocal(updatedMetrics);
                              
                              // Save blood pressure data to AsyncStorage
                              const bpData = {
                                timestamp: new Date().toISOString(),
                                systolic,
                                diastolic,
                                bloodOxygen,
                                fatigue
                              };
                              
                              AsyncStorage.getItem(`@blood_pressure:${user?.email || 'user'}`)
                                .then(data => {
                                  const history = data ? JSON.parse(data) : [];
                                  history.push(bpData);
                                  // Keep only last 50 readings
                                  if (history.length > 50) history.shift();
                                  return AsyncStorage.setItem(
                                    `@blood_pressure:${user?.email || 'user'}`,
                                    JSON.stringify(history)
                                  );
                                })
                                .catch(err => console.error('Error saving blood pressure data:', err));
                              
                              // Show results
                              let bpCategory = 'Normal';
                              let bpColor = '#4CAF50'; // Green
                              
                              if (systolic >= 140 || diastolic >= 90) {
                                bpCategory = 'High';
                                bpColor = '#F44336'; // Red
                              } else if (systolic >= 130 || diastolic >= 85) {
                                bpCategory = 'Elevated';
                                bpColor = '#FF9800'; // Orange
                              } else if (systolic <= 90 || diastolic <= 60) {
                                bpCategory = 'Low';
                                bpColor = '#2196F3'; // Blue
                              }
                              
                              Alert.alert(
                                "Blood Pressure Results",
                                `Your blood pressure is ${bloodPressure} mmHg (${bpCategory}).\n\nBlood Oxygen: ${bloodOxygen}%\nFatigue Level: ${fatigue}`,
                                [{ text: "OK" }]
                              );
                            }, 5000); // Simulate 5 second measurement
                          }
                        }
                      ]
                    );
                  }, 3000); // Simulate 3 second device search
                } catch (error) {
                  console.error('Error starting blood pressure monitoring:', error);
                  Alert.alert(
                    "Error", 
                    "Failed to start blood pressure monitoring: " + error.message,
                    [{ text: "OK" }]
                  );
                }
              }
            }
          ]
        );
        break;
        
      case 'Exercise':
        // Start exercise tracking with options
        Alert.alert(
          "Start Exercise Tracking",
          "Choose an exercise type to begin tracking:",
          [
            { text: "Cancel", style: "cancel" },
            { 
              text: "Cardio", 
              onPress: () => {
                // Clear any existing interval
                if (trainingIntervalRef.current) {
                  clearInterval(trainingIntervalRef.current);
                }
                
                // Initialize exercise tracking
                const exerciseStartTime = new Date();
                const exerciseData = {
                  type: 'Cardio',
                  startTime: exerciseStartTime.toISOString(),
                  duration: 0,
                  caloriesBurned: 0,
                  inProgress: true
                };
                
                // Save initial exercise data
                AsyncStorage.setItem(
                  `@exercise_tracking:${user?.email || 'user'}:current`,
                  JSON.stringify(exerciseData)
                );
                
                // Start exercise tracking simulation
                const exerciseInterval = setInterval(() => {
                  const now = new Date();
                  const durationMinutes = Math.floor((now - exerciseStartTime) / 60000);
                  const durationHours = Math.floor(durationMinutes / 60);
                  const remainingMinutes = durationMinutes % 60;
                  
                  // Calculate calories burned (rough estimate)
                  // Average person burns ~7-10 calories per minute during cardio
                  const caloriesBurned = Math.floor(durationMinutes * 8);
                  
                  // Update exercise data
                  exerciseData.duration = durationMinutes;
                  exerciseData.caloriesBurned = caloriesBurned;
                  
                  AsyncStorage.setItem(
                    `@exercise_tracking:${user?.email || 'user'}:data`,
                    JSON.stringify(exerciseData)
                  );
                  
                  // Update user data
                  setUserData(prev => ({
                    ...prev,
                    exerciseDuration: `${durationHours}h ${remainingMinutes}m`,
                    caloriesBurned: prev.caloriesBurned + 8 // Add 8 calories per minute
                  }));
                  
                }, 60000); // Update every minute
                
                trainingIntervalRef.current = exerciseInterval;
                
                // Navigate to exercise tab
                handleTabPress(0);
                
                Alert.alert(
                  "Exercise Tracking Started", 
                  "Your exercise session is now being tracked. The app will monitor your workout duration and calories burned."
                );
              }
            }
          ]
        );
        break;
        
      default:
        Alert.alert("Feature Coming Soon", "This feature will be available in the next update.");
        break;
    }
  };
                  // Request location permissions for GPS tracking
                  const { status } = await Location.requestForegroundPermissionsAsync();
                  
                  if (status !== 'granted') {
                    Alert.alert(
                      "Permission Required", 
                      "Running tracking requires location permissions for GPS.",
                      [{ text: "OK" }]
                    );
                    return;
                  }
                  
                  // Start location tracking
                  Alert.alert(
                    "Starting Run Tracking", 
                    "GPS tracking will begin when you press Start. Your route, pace, and calories will be tracked.",
                    [
                      { text: "Cancel", style: "cancel" },
                      { 
                        text: "Start", 
                        onPress: () => {
                          // In a real app, we would start GPS tracking here
                          // For this demo, we'll simulate the process
                          
                          // Navigate to exercise tab
                          handleTabPress(4);
                          
                          // Clear any existing interval
                          if (trainingIntervalRef.current) {
                            clearInterval(trainingIntervalRef.current);
                          }
                          
                          // Initialize run data
                          const runData = {
                            startTime: new Date().toISOString(),
                            distance: 0,
                            pace: 0,
                            calories: 0,
                            route: [],
                            inProgress: true
                          };
                          
                          // Save initial run data
                          AsyncStorage.setItem(
                            `@current_run:${user?.email || 'user'}`,
                            JSON.stringify(runData)
                          );
                          
                          // Simulate run tracking
                          const runInterval = setInterval(() => {
                            // Simulate distance increase (0.1-0.2 km per minute)
                            const distanceIncrement = (Math.random() * 0.1 + 0.1) / 6; // Per 10 seconds
                            
                            // Update run data
                            AsyncStorage.getItem(`@current_run:${user?.email || 'user'}`)
                              .then(data => {
                                const currentRun = JSON.parse(data);
                                const updatedRun = {
                                  ...currentRun,
                                  distance: currentRun.distance + distanceIncrement,
                                  pace: (currentRun.distance > 0) ? 
                                    (new Date() - new Date(currentRun.startTime)) / (currentRun.distance * 60000) : 0,
                                  calories: Math.round(currentRun.distance * 65), // ~65 calories per km
                                  route: [
                                    ...currentRun.route,
                                    { 
                                      latitude: 37.7749 + (Math.random() - 0.5) * 0.01, 
                                      longitude: -122.4194 + (Math.random() - 0.5) * 0.01,
                                      timestamp: new Date().toISOString()
                                    }
                                  ]
                                };
                                
                                // Update health metrics
                                const updatedMetrics = [...healthMetrics];
                                updatedMetrics[4].value = updatedRun.distance.toFixed(2) + ' km';
                                updatedMetrics[4].calories = updatedRun.calories;
                                updatedMetrics[4].pace = updatedRun.pace.toFixed(2) + ' min/km';
                                setHealthMetricsLocal(updatedMetrics);
                                
                                return AsyncStorage.setItem(
                                  `@current_run:${user?.email || 'user'}`,
                                  JSON.stringify(updatedRun)
                                );
                              })
                              .catch(err => console.error('Error updating run data:', err));
                            
                          }, 10000); // Update every 10 seconds
                          
                          trainingIntervalRef.current = runInterval;
                          
                          // Show tracking started notification
                          Alert.alert(
                            "Run Tracking Started", 
                            "Your run is now being tracked. Press STOP when finished."
                          );
                        }
                      }
                    ]
                  );
                } catch (error) {
                  console.error('Error starting run tracking:', error);
                  Alert.alert(
                    "Error", 
                    "Failed to start run tracking: " + error.message,
                    [{ text: "OK" }]
                  );
                }
              }
            },
            { 
              text: "Strength Training", 
              onPress: () => {
                // Navigate to strength training screen
                router.push({
                  pathname: '/Home',
                  params: {
                    workoutType: 'Strength',
                    workoutData: JSON.stringify({
                      name: 'Custom Strength Workout',
                      exercises: [
                        { name: 'Push-ups', sets: 3, reps: 12 },
                        { name: 'Squats', sets: 3, reps: 15 },
                        { name: 'Plank', sets: 3, duration: '30 sec' },
                        { name: 'Lunges', sets: 3, reps: 10 },
                        { name: 'Dumbbell Rows', sets: 3, reps: 12 }
                      ]
                    })
                  }
                });
              }
            }
          ]
        );
        break;
        
      default:
        Alert.alert("Feature Coming Soon", "This feature will be available in the next update.");
    }
  };

  // Add new functions for tracking
  const updateSteps = async (steps) => {
    try {
      const today = new Date().toISOString().split('T')[0];
      const stepsKey = `@steps:${user?.email}:${today}`;
      const caloriesKey = `@calories:${user?.email}:${today}`;
      
      // Calculate calories (rough estimate: 0.04 calories per step)
      const calories = Math.round(steps * 0.04);
      
      await AsyncStorage.setItem(stepsKey, steps.toString());
      await AsyncStorage.setItem(caloriesKey, calories.toString());
      
      setUserData(prev => ({
        ...prev,
        totalSteps: steps,
        caloriesBurned: calories,
        lastUpdated: new Date().toISOString()
      }));
    } catch (error) {
      console.error('Error updating steps:', error);
    }
  };

  const fetchTodayStats = async () => {
    try {
      if (!user?.email) return;
      
      const today = new Date().toISOString().split('T')[0];
      const stats = ['steps', 'calories', 'water', 'sleep'];
      
      for (const stat of stats) {
        const key = `@${stat}:${user.email}:${today}`;
        const value = await AsyncStorage.getItem(key);
        if (value) {
          setUserData(prev => ({
            ...prev,
            [stat === 'steps' ? 'totalSteps' : 
             stat === 'calories' ? 'caloriesBurned' : 
             stat === 'water' ? 'waterIntake' : 'sleepHours']: parseInt(value)
          }));
        }
      }
    } catch (error) {
      console.error('Error fetching today stats:', error);
    }
  };

  // Add step tracking simulation
  useEffect(() => {
    if (user?.email) {
      fetchTodayStats();
      
      // Simulate step updates every 5 seconds
      const interval = setInterval(() => {
        const randomSteps = Math.floor(Math.random() * 100) + 50;
        const newSteps = userData.totalSteps + randomSteps;
        const calories = Math.round(newSteps * 0.04);
        
        updateSteps(newSteps);
        // Update calories separately
        const today = new Date().toISOString().split('T')[0];
        const caloriesKey = `@calories:${user?.email}:${today}`;
        AsyncStorage.setItem(caloriesKey, calories.toString());
        
        setUserData(prev => ({
          ...prev,
          caloriesBurned: calories,
          lastUpdated: new Date().toISOString()
        }));
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [user?.email]);

  // Add new tracking functions
  const updateActivity = async (type, value) => {
    try {
      const today = new Date().toISOString().split('T')[0];
      const key = `@${type}:${user?.email}:${today}`;
      await AsyncStorage.setItem(key, value.toString());
      
      setUserData(prev => ({
        ...prev,
        [type]: value,
        lastUpdated: new Date().toISOString()
      }));
    } catch (error) {
      console.error(`Error updating ${type}:`, error);
    }
  };

  // Update training tracking - using trainingIntervalRef instead of state
  const startTraining = () => {
    setUserData(prev => ({
      ...prev,
      isTraining: true,
      trainingStartTime: new Date(),
      trainingDuration: 0
    }));

    // Start real-time tracking - store interval in ref instead of state
    if (trainingIntervalRef.current) {
      clearInterval(trainingIntervalRef.current);
    }
    
    trainingIntervalRef.current = setInterval(() => {
      setUserData(prev => {
        if (!prev.isTraining) {
          if (trainingIntervalRef.current) {
            clearInterval(trainingIntervalRef.current);
            trainingIntervalRef.current = null;
          }
          return prev;
        }
        const now = new Date();
        const duration = Math.round((now - prev.trainingStartTime) / 1000 / 60);
        const steps = Math.round(duration * 100); // Approximate steps per minute
        const calories = Math.round(steps * 0.04);
        
        return {
          ...prev,
          trainingDuration: duration,
          totalSteps: prev.totalSteps + steps,
          caloriesBurned: prev.caloriesBurned + calories
        };
      });
    }, 1000);
  };

  const stopTraining = () => {
    const endTime = new Date();
    const duration = Math.round((endTime - userData.trainingStartTime) / 1000 / 60);
    
    // Clear the training interval using ref
    if (trainingIntervalRef.current) {
      clearInterval(trainingIntervalRef.current);
      trainingIntervalRef.current = null;
    }

    // Save training session
    const trainingSession = {
      type: userData.favoriteExercise,
      duration,
      date: new Date().toISOString(),
      calories: Math.round(duration * 5),
      steps: Math.round(duration * 100)
    };

    saveTrainingSession(trainingSession);
    
    setUserData(prev => ({
      ...prev,
      isTraining: false,
      dailyTraining: `${duration} min ${prev.favoriteExercise}`
    }));
  };

  // Update water tracking
  const updateWaterIntake = async () => {
    try {
      const newWaterIntake = userData.waterIntake + 1;
      const today = new Date().toISOString().split('T')[0];
      const waterKey = `@water:${user?.email}:${today}`;
      
      await AsyncStorage.setItem(waterKey, newWaterIntake.toString());
      
      setUserData(prev => ({
        ...prev,
        waterIntake: newWaterIntake,
        lastUpdated: new Date().toISOString()
      }));

      // Save to history
      const historyKey = `@water_history:${user?.email}`;
      const historyString = await AsyncStorage.getItem(historyKey);
      const history = historyString ? JSON.parse(historyString) : [];
      
      history.push({
        amount: 1,
        date: new Date().toISOString()
      });
      
      await AsyncStorage.setItem(historyKey, JSON.stringify(history));
    } catch (error) {
      console.error('Error updating water intake:', error);
    }
  };

  // Update sleep tracking
  const updateSleepHours = async () => {
    try {
      const newSleepHours = userData.sleepHours + 0.5;
      const today = new Date().toISOString().split('T')[0];
      const sleepKey = `@sleep:${user?.email}:${today}`;
      
      await AsyncStorage.setItem(sleepKey, newSleepHours.toString());
      
      setUserData(prev => ({
        ...prev,
        sleepHours: newSleepHours,
        lastUpdated: new Date().toISOString()
      }));

      // Save to history
      const historyKey = `@sleep_history:${user?.email}`;
      const historyString = await AsyncStorage.getItem(historyKey);
      const history = historyString ? JSON.parse(historyString) : [];
      
      history.push({
        hours: 0.5,
        date: new Date().toISOString()
      });
      
      await AsyncStorage.setItem(historyKey, JSON.stringify(history));
    } catch (error) {
      console.error('Error updating sleep hours:', error);
    }
  };

  // Function to handle quick action and track progress
  const handleQuickAction = async (type) => {
    try {
      if (!user?.email) {
        Alert.alert('Error', 'Please login to start a workout');
        router.replace('/user/login');
        return;
      }

      // Generate workout based on type
      const workoutPlans = {
        Strength: {
          exercises: ['Push-ups', 'Squats', 'Lunges', 'Plank'],
          duration: 30,
          sets: 3,
          reps: '8-12',
          description: 'Build muscle strength and endurance'
        },
        Cardio: {
          exercises: ['Jumping Jacks', 'High Knees', 'Burpees', 'Mountain Climbers'],
          duration: 25,
          sets: 4,
          reps: '30 seconds',
          description: 'Boost cardiovascular health and burn calories'
        },
        Yoga: {
          exercises: ['Downward Dog', 'Warrior Pose', 'Tree Pose', 'Child\'s Pose'],
          duration: 20,
          sets: 1,
          reps: '30-60 seconds hold',
          description: 'Improve flexibility and mindfulness'
        }
      };

      const workout = workoutPlans[type];
      
      Alert.alert(
        `🏋️ ${type} Workout Ready!`,
        `${workout.description}\n\nDuration: ${workout.duration} minutes\nExercises: ${workout.exercises.join(', ')}\n\nSets: ${workout.sets} | Reps: ${workout.reps}`,
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Start Workout', 
            onPress: async () => {
              // Track progress
              setProgress(prev => ({ ...prev, [type]: prev[type] + 1 }));
              const progressKey = `@progress:${user.email}:${type}`;
              await AsyncStorage.setItem(progressKey, (progress[type] + 1).toString());
              
              // Save workout session
              const session = {
                type: type,
                exercises: workout.exercises,
                duration: workout.duration,
                date: new Date().toISOString(),
                completed: false
              };
              
              const sessionKey = `@workout_session:${user.email}`;
              await AsyncStorage.setItem(sessionKey, JSON.stringify(session));
              
              // Navigate to Home workout screen with workout data
              router.push({
                pathname: '/Home',
                params: {
                  workoutType: type,
                  workoutData: JSON.stringify(workout)
                }
              });
            }
          }
        ]
      );
    } catch (error) {
      console.error(`Error starting ${type} session:`, error);
      Alert.alert('Error', `Failed to start ${type} workout`);
    }
  };

  // Load progress from AsyncStorage on mount
  useEffect(() => {
    if (!user?.email) return;
    const loadProgress = async () => {
      const types = ['Strength', 'Cardio', 'Yoga'];
      const newProgress = {};
      for (const type of types) {
        const progressKey = `@progress:${user.email}:${type}`;
        const value = await AsyncStorage.getItem(progressKey);
        newProgress[type] = value ? parseInt(value) : 0;
      }
      setProgress(newProgress);
    };
    loadProgress();
  }, [user?.email]);

  // Add function to update challenge progress
  const updateChallengeProgress = async (challengeId, progress) => {
    try {
      const updatedChallenges = userData.challenges.map(challenge => {
        if (challenge.id === challengeId) {
          return {
            ...challenge,
            progress: Math.min(challenge.progress + progress, challenge.total)
          };
        }
        return challenge;
      });

      setUserData(prev => ({
        ...prev,
        challenges: updatedChallenges
      }));

      // Save to AsyncStorage
      const challengesKey = `@challenges:${user?.email}`;
      await AsyncStorage.setItem(challengesKey, JSON.stringify(updatedChallenges));
    } catch (error) {
      console.error('Error updating challenge progress:', error);
    }
  };

  const saveTrainingSession = async (session) => {
    try {
      const historyKey = `@training_history:${user?.email}`;
      const historyString = await AsyncStorage.getItem(historyKey);
      const history = historyString ? JSON.parse(historyString) : [];
      
      history.push(session);
      await AsyncStorage.setItem(historyKey, JSON.stringify(history));
    } catch (error) {
      console.error('Error saving training session:', error);
    }
  };

  const completeWorkout = async () => {
    // ...other code...
    const progressKey = `@progress:${user?.email}:Strength`;
    const prev = await AsyncStorage.getItem(progressKey);
    const newVal = prev ? parseInt(prev) + 1 : 1;
    await AsyncStorage.setItem(progressKey, newVal.toString());
    // ...other code...
  };

  // Main cleanup effect - this will run when component unmounts
  useEffect(() => {
    // Setup pedometer
    const subscription = Pedometer.watchStepCount(result => {
      setStepCount(result.steps);
      // Save to AsyncStorage if needed
    });
    
    // Comprehensive cleanup function to clear all intervals and subscriptions
    return () => {
      console.log('Cleaning up all resources...');
      
      // Clear Pedometer subscription
      if (subscription && subscription.remove) {
        subscription.remove();
      }
      
      // Clear training interval if active
      if (trainingIntervalRef.current) {
        clearInterval(trainingIntervalRef.current);
        trainingIntervalRef.current = null;
      }
      
      // Clear any other timers or intervals that might be running
      const timers = [
        trainingIntervalRef.current
      ].filter(Boolean);
      
      timers.forEach(timer => {
        if (timer) clearInterval(timer);
      });
    };
  }, []);

  if (!fontsLoaded || loading || userLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#0c0c0c' }}>
        <LinearGradient colors={['#0c0c0c', '#1a1a2e']} style={StyleSheet.absoluteFill} />
        <ActivityIndicator size="large" color="#fff" />
      </View>
    );
  }

  // Navigation handlers
  const nav = {
    notifications: () => router.push('/notifications'),
    favorites: () => router.push('/favorites'),
    aiCoach: () => {
      console.log('Navigating to AI Coach');
      router.push('/ai-coach');
    },
    aiWorkout: () => router.push('/ai-workout-generator'),
    achievements: () => router.push('/nft-achievements'),
    exercises: () => router.push('/homepage'),
    dietPlans: () => router.push('/plans'),
    virtualClasses: () => router.push('/virtual-classes'),
    analyze: () => router.push('/analyze'),
    socialMetaverse: () => router.push('/social-metaverse'),
    startWorkout: () => router.push('/ai-workout-generator'), // fallback to AI Workout Generator
    chat: () => router.push('/chat'), // fallback to ai-coach as chat.jsx not found
  };

  // Animated button component
  const MenuButton = ({ icon, label, onPress, iconLib = Ionicons }) => (
    <AnimatedTouchable
      style={[
        styles.menuButton,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
      activeOpacity={0.8}
      onPress={onPress}
    >
      {iconLib === Ionicons && <Ionicons name={icon} size={24} color="#fff" />}
      {iconLib === MaterialIcons && <MaterialIcons name={icon} size={24} color="#fff" />}
      {iconLib === FontAwesome5 && <FontAwesome5 name={icon} size={22} color="#fff" />}
      {iconLib === MaterialCommunityIcons && <MaterialCommunityIcons name={icon} size={24} color="#fff" />}
      <Text style={styles.menuButtonText}>{label}</Text>
    </AnimatedTouchable>
  );

  // Top menu bar buttons
  const topMenu = [
    { icon: 'notifications-outline', label: 'Notification', onPress: nav.notifications },
    { icon: 'star-outline', label: 'Favorite', onPress: nav.favorites },
    { 
      icon: 'robot', 
      label: 'AI Chat', 
      onPress: () => {
        console.log('Navigating to AI Coach from top menu');
        router.push('/ai-coach');
      }, 
      iconLib: MaterialCommunityIcons 
    },
    { icon: 'dumbbell', label: 'AI Workout', onPress: nav.aiWorkout, iconLib: MaterialCommunityIcons },
    { icon: 'trophy-outline', label: 'Achievements', onPress: nav.achievements, iconLib: Ionicons },
    { icon: 'yoga', label: 'Exercise', onPress: nav.exercises, iconLib: MaterialCommunityIcons },
    { icon: 'restaurant-outline', label: 'Diet Plans', onPress: nav.dietPlans },
    { icon: 'tv-outline', label: 'Online Classes', onPress: nav.virtualClasses },
    { icon: 'analytics-outline', label: 'Daily Report', onPress: nav.analyze },
  ];

  // Update trackerCards to show edit icon for heart rate, sleep, steps, calories, and distance
  const trackerCards = [
    {
      key: 'realtime',
      label: 'Heart Rate',
      icon: 'heart',
      color: '#F44336',
      value: userData.heartRate || 72,
      valueLabel: 'BPM',
      progress: Math.min((userData.heartRate || 72) / 120, 1),
      onPress: () => handleTabPress(0),
      editable: true,
    },
    {
      key: 'sleep',
      label: 'Sleep',
      icon: 'moon',
      color: '#9C27B0',
      value: userData.sleepHours || 0,
      valueLabel: 'hrs',
      progress: Math.min((userData.sleepHours || 0) / (userData.sleepGoal || 8), 1),
      onPress: () => handleTabPress(2),
      editable: true,
    },
    {
      key: 'steps',
      label: 'Steps',
      icon: 'footsteps',
      color: '#4CAF50',
      value: userData.totalSteps || 0,
      valueLabel: 'steps',
      progress: Math.min((userData.totalSteps || 0) / (userData.stepGoal || 10000), 1),
      onPress: () => handleTabPress(3),
      editable: true,
    },
    {
      key: 'calories',
      label: 'Calories',
      icon: 'flame',
      color: '#FF5722',
      value: userData.caloriesBurned || 0,
      valueLabel: 'cal',
      progress: Math.min((userData.caloriesBurned || 0) / (userData.calorieGoal || 500), 1),
      onPress: () => handleTabPress(0),
      editable: true,
    },
    {
      key: 'gps',
      label: 'Distance',
      icon: 'navigate',
      color: '#00BFFF',
      value: userData.distance || 0,
      valueLabel: 'km',
      progress: Math.min((userData.distance || 0) / (userData.distanceGoal || 5), 1),
      onPress: () => handleTabPress(1),
      editable: true,
    },
  ];

  // Using trackerScrollX from our custom hook - no need to redeclare

  return (
    <View style={styles.container}>
      {/* Animated Gradient Background */}
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      {/* Animated Particles and Floating Shapes */}
      <AdvancedParticle style={{ position: 'absolute', width: 24, height: 24, left: width * 0.1, top: 120, zIndex: 0 }} delay={0} speed={1.2} />
      <AdvancedParticle style={{ position: 'absolute', width: 18, height: 18, left: width * 0.8, top: 200, zIndex: 0 }} delay={500} speed={0.8} />
      <AdvancedParticle style={{ position: 'absolute', width: 12, height: 12, left: width * 0.2, top: 500, zIndex: 0 }} delay={1000} speed={1.5} />
      <FloatingShape style={{ position: 'absolute', width: 32, height: 32, backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 16, left: width * 0.85, top: 100, zIndex: 0 }} delay={0} rotationSpeed={0.5} />
      <FloatingShape style={{ position: 'absolute', width: 20, height: 20, backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 10, left: width * 0.05, top: 600, zIndex: 0 }} delay={1000} rotationSpeed={1.2} />
      <View style={[styles.glowCircle, { top: 60, right: 30 }]} />
      <View style={[styles.glowCircle, { bottom: 80, left: 20 }]} />
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar style="light" />
        <ScrollView 
          showsVerticalScrollIndicator={false} 
          contentContainerStyle={styles.scrollContainer}
          overScrollMode={Platform.OS === 'android' ? 'never' : 'auto'}
          bounces={Platform.OS === 'ios'}
          scrollEventThrottle={16}
          decelerationRate="normal"
        >
          {/* Header / Menubar */}
          <View style={styles.headerContainer}>
            <View style={styles.greetingContainer}>
              <Text style={styles.greeting}>Hello, {user?.name || username || 'User'}</Text>
              <Text style={styles.greetingSubtext}>Welcome to your fitness journey!</Text>
            </View>
            <TouchableOpacity onPress={() => setMenuVisible(true)} style={styles.menuIconContainer}>
              <Ionicons name="menu" size={32} color="#fff" />
            </TouchableOpacity>
          </View>
          
          {/* Image Slider */}
          <ImageSlider images={sliderImages} />
          
          {/* Tab View with boxes */}
          <View style={{ marginTop: 16, marginHorizontal: 16 }}>
            <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold', marginBottom: 12, fontFamily: 'Poppins_600SemiBold' }}>
              Activity Trackers
            </Text>
            <View style={styles.trackerBoxContainer}>
              <TouchableOpacity 
                style={styles.trackerBox} 
                onPress={() => router.push('/realtime-tracker')}
              >
                <Ionicons name="pulse" size={24} color="#4CAF50" />
                <Text style={styles.trackerBoxText}>Realtime</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.trackerBox} 
                onPress={() => router.push('/gps-tracker')}
              >
                <Ionicons name="navigate" size={24} color="#2196F3" />
                <Text style={styles.trackerBoxText}>GPS</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.trackerBox} 
                onPress={() => router.push('/sleep-tracker')}
              >
                <Ionicons name="moon" size={24} color="#9C27B0" />
                <Text style={styles.trackerBoxText}>Sleep</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.trackerBox} 
                onPress={() => router.push('/step-counter')}
              >
                <Ionicons name="footsteps" size={24} color="#FF9800" />
                <Text style={styles.trackerBoxText}>Steps</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.trackerBox} 
                onPress={() => router.push('/favorites')}
              >
                <Ionicons name="star" size={24} color="#F44336" />
                <Text style={styles.trackerBoxText}>Favorites</Text>
              </TouchableOpacity>
            </View>
          </View>
          
          {/* Workout Calendar */}
          <View style={{ marginTop: 16, marginHorizontal: 16 }}>
            <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold', marginBottom: 12, fontFamily: 'Poppins_600SemiBold' }}>
              Workout Calendar
            </Text>
            <View style={{ 
              backgroundColor: 'rgba(255,255,255,0.08)', 
              borderRadius: 16, 
              padding: 16,
              borderWidth: 1,
              borderColor: 'rgba(255,255,255,0.1)'
            }}>
              <WorkoutCalendar userEmail={user?.email} />
            </View>
          </View>
          
          {/* Health Trackers */}
          <View style={{ marginTop: 16, marginHorizontal: 16 }}>
            <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold', marginBottom: 12, fontFamily: 'Poppins_600SemiBold' }}>
              Health Trackers
            </Text>
            <View style={styles.healthTrackersContainer}>
              <TouchableOpacity 
                style={styles.healthTrackerItem} 
                onPress={() => startTracking('Walk')}
              >
                <Ionicons name="walk" size={24} color="#4CAF50" />
                <Text style={styles.healthTrackerText}>Walk</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.healthTrackerItem} 
                onPress={() => startTracking('Sleep')}
              >
                <Ionicons name="moon" size={24} color="#9C27B0" />
                <Text style={styles.healthTrackerText}>Sleep</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.healthTrackerItem} 
                onPress={() => startTracking('Heart rate')}
              >
                <Ionicons name="heart" size={24} color="#F44336" />
                <Text style={styles.healthTrackerText}>Heart Rate</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.healthTrackerItem} 
                onPress={() => startTracking('Blood pressure')}
              >
                <Ionicons name="fitness" size={24} color="#2196F3" />
                <Text style={styles.healthTrackerText}>Blood Pressure</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.healthTrackerItem} 
                onPress={() => startTracking('Exercise')}
              >
                <Ionicons name="barbell" size={24} color="#FF9800" />
                <Text style={styles.healthTrackerText}>Exercise</Text>
              </TouchableOpacity>
            </View>
          </View>
          
          {/* Today's Progress */}
          <View style={{ marginTop: 16, marginHorizontal: 16, marginBottom: 16 }}>
            <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold', marginBottom: 12, fontFamily: 'Poppins_600SemiBold' }}>
              Today's Progress
            </Text>
            <View style={styles.todayProgressContainer}>
              {trackers.map((tracker) => (
                <TouchableOpacity 
                  key={tracker.key}
                  style={styles.progressItem}
                  onPress={tracker.onPress}
                >
                  <View style={styles.progressIconContainer}>
                    <Ionicons name={tracker.icon} size={24} color={tracker.color} />
                  </View>
                  <View style={styles.progressContent}>
                    <Text style={styles.progressLabel}>{tracker.label}</Text>
                    <View style={styles.progressBarContainer}>
                      <View 
                        style={[
                          styles.progressBar, 
                          { width: `${tracker.progress * 100}%`, backgroundColor: tracker.color }
                        ]} 
                      />
                    </View>
                    <Text style={styles.progressValue}>
                      {tracker.value} {tracker.valueLabel}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          

          {/* MenuBar Modal */}
          <Modal
            visible={menuVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setMenuVisible(false)}
          >
            <Pressable style={styles.modalOverlay} onPress={() => setMenuVisible(false)}>
              <View style={styles.menuModalContainer}>
                <View style={styles.menuModalHeader}>
                  <Text style={styles.menuModalTitle}>Menu</Text>
                  <TouchableOpacity onPress={() => setMenuVisible(false)} style={styles.menuModalCloseButton}>
                    <Ionicons name="close" size={24} color="#fff" />
                  </TouchableOpacity>
                </View>
                
                <View style={styles.menuModalDivider} />
                
                {logoutError && (
                  <Text style={{ color: 'red', marginBottom: 8 }}>{logoutError}</Text>
                )}
                {menuModalItems.map((item, idx) => (
                  <TouchableOpacity 
                    key={item.label} 
                    style={[
                      styles.menuModalItem, 
                      item.label === 'Logout' && styles.logoutButton
                    ]} 
                    onPress={item.label === 'Logout' ? handleLogout : item.onPress}
                    disabled={item.label === 'Logout' && logoutLoading}
                  >
                    {item.iconLib === MaterialCommunityIcons ? (
                      <MaterialCommunityIcons name={item.icon} size={24} color={item.color} />
                    ) : (
                      <Ionicons name={item.icon} size={24} color={item.color} />
                    )}
                    <Text style={[
                      styles.menuModalText,
                      item.label === 'Logout' && styles.logoutText
                    ]}>{item.label}{item.label === 'Logout' && logoutLoading ? ' (Logging out...)' : ''}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </Pressable>
          </Modal>
          {/* Tracker Tabs with Swipe and Animation */}
          <View style={styles.tabBarWrapper}>{renderTabBar()}</View>
          

          <View style={{ marginBottom: 18 }}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ flexDirection: 'row', marginBottom: 10 }}>
              {healthMetrics.map(metric => (
                <TouchableOpacity
                  key={metric.key}
                  onPress={() => setHealthTab(metric.key)}
                  style={{
                    paddingVertical: 8,
                    paddingHorizontal: 18,
                    borderBottomWidth: 2,
                    borderBottomColor: healthTab === metric.key ? metric.color : 'transparent',
                    marginRight: 10,
                  }}
                >
                  <Text style={{ color: healthTab === metric.key ? metric.color : '#fff', fontWeight: 'bold', fontSize: 16 }}>{metric.label}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            <View style={{ alignItems: 'center', marginBottom: 10 }}>
              {/* Circular Progress for selected metric */}
              <View style={{ width: 160, height: 160, justifyContent: 'center', alignItems: 'center', marginBottom: 8 }}>
                <View style={{ position: 'absolute', width: 160, height: 160, borderRadius: 80, borderWidth: 10, borderColor: healthMetrics.find(m => m.key === healthTab).color + '55', borderTopColor: healthMetrics.find(m => m.key === healthTab).color, transform: [{ rotate: '135deg' }], }} />
                <Ionicons name={healthMetrics.find(m => m.key === healthTab).icon} size={36} color={healthMetrics.find(m => m.key === healthTab).color} style={{ marginBottom: 8 }} />
                <Text style={{ color: '#fff', fontSize: 36, fontWeight: 'bold' }}>{healthMetrics.find(m => m.key === healthTab).value}</Text>
                <Text style={{ color: '#fff', fontSize: 16 }}>{healthMetrics.find(m => m.key === healthTab).unit}</Text>
                {/* Extra info for Heart rate and Blood pressure */}
                {healthTab === 'Heart rate' && (
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: 140, marginTop: 8 }}>
                    <View style={{ alignItems: 'center' }}><Text style={{ color: '#fff', fontSize: 12 }}>Min.</Text><Text style={{ color: '#F44336', fontWeight: 'bold' }}>{healthMetrics[2].min} BPM</Text></View>
                    <View style={{ alignItems: 'center' }}><Text style={{ color: '#fff', fontSize: 12 }}>Avg.</Text><Text style={{ color: '#F44336', fontWeight: 'bold' }}>{healthMetrics[2].avg} BPM</Text></View>
                    <View style={{ alignItems: 'center' }}><Text style={{ color: '#fff', fontSize: 12 }}>Max.</Text><Text style={{ color: '#F44336', fontWeight: 'bold' }}>{healthMetrics[2].maxVal} BPM</Text></View>
                  </View>
                )}
                {healthTab === 'Blood pressure' && (
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: 180, marginTop: 8 }}>
                    <View style={{ alignItems: 'center' }}><Text style={{ color: '#fff', fontSize: 12 }}>Blood oxygen</Text><Text style={{ color: '#AB47BC', fontWeight: 'bold' }}>{healthMetrics[3].extra.bloodOxygen} %</Text></View>
                    <View style={{ alignItems: 'center' }}><Text style={{ color: '#fff', fontSize: 12 }}>Fatigue</Text><Text style={{ color: '#AB47BC', fontWeight: 'bold' }}>{healthMetrics[3].extra.fatigue}</Text></View>
                  </View>
                )}
              </View>
              <TouchableOpacity 
                style={{ 
                  backgroundColor: healthMetrics.find(m => m.key === healthTab).color, 
                  borderRadius: 20, 
                  paddingHorizontal: 28, 
                  paddingVertical: 8, 
                  marginTop: 8,
                  elevation: 3,
                  shadowColor: healthMetrics.find(m => m.key === healthTab).color,
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.3,
                  shadowRadius: 4,
                }}
                onPress={() => {
                  // Handle different health tracking actions based on the selected tab
                  switch(healthTab) {
                    case 'Walk':
                      // Start step counter with real Pedometer integration
                      Alert.alert(
                        "Start Step Tracking",
                        "This will begin tracking your steps in real-time using your device's sensors.",
                        [
                          { text: "Cancel", style: "cancel" },
                          { 
                            text: "Start Tracking", 
                            onPress: async () => {
                              try {
                                // Request permission if needed
                                const { status } = await Pedometer.requestPermissionsAsync();
                                
                                if (status !== 'granted') {
                                  Alert.alert(
                                    "Permission Required", 
                                    "Step tracking requires motion sensor permissions.",
                                    [{ text: "OK" }]
                                  );
                                  return;
                                }
                                
                                // Check if pedometer is available
                                const isAvailable = await Pedometer.isAvailableAsync();
                                
                                if (!isAvailable) {
                                  Alert.alert(
                                    "Sensor Not Available", 
                                    "Step counting is not available on this device.",
                                    [{ text: "OK" }]
                                  );
                                  return;
                                }
                                
                                // Start real-time step tracking
                                const subscription = Pedometer.watchStepCount(result => {
                                  console.log('New step count:', result.steps);
                                  setStepCount(prevCount => prevCount + result.steps);
                                  
                                  // Update health metrics
                                  const updatedMetrics = [...healthMetrics];
                                  updatedMetrics[0].value = stepCount.toString();
                                  setHealthMetrics(updatedMetrics);
                                });
                                
                                // Store subscription for cleanup
                                if (trainingIntervalRef.current && trainingIntervalRef.current.remove) {
                                  trainingIntervalRef.current.remove();
                                }
                                trainingIntervalRef.current = subscription;
                                
                                // Navigate to step counter tab
                                handleTabPress(3); // Index for steps tab
                                
                                // Update UI to show tracking has started
                                Alert.alert(
                                  "Step Tracking Started", 
                                  "Your steps are now being tracked in real-time using your device's pedometer."
                                );
                              } catch (error) {
                                console.error('Error starting step tracking:', error);
                                Alert.alert(
                                  "Error", 
                                  "Failed to start step tracking: " + error.message,
                                  [{ text: "OK" }]
                                );
                                
                                // Fallback to simulated tracking
                                const interval = setInterval(() => {
                                  // Simulate 5-15 steps every 5 seconds
                                  const newSteps = Math.floor(Math.random() * 10) + 5;
                                  setStepCount(prevCount => prevCount + newSteps);
                                  
                                  // Update health metrics
                                  const updatedMetrics = [...healthMetrics];
                                  updatedMetrics[0].value = stepCount.toString();
                                  setHealthMetrics(updatedMetrics);
                                }, 5000);
                                
                                trainingIntervalRef.current = interval;
                                
                                // Navigate to step counter tab
                                handleTabPress(3);
                                
                                Alert.alert(
                                  "Simulated Step Tracking", 
                                  "Using simulated step data as a fallback."
                                );
                              }
                            }
                          }
                        ]
                      );
                      break;
                      
                    case 'Sleep':
                      // Start sleep tracking with device sensors
                      Alert.alert(
                        "Start Sleep Tracking",
                        "This will monitor your sleep patterns using device sensors. Place your device near you while sleeping.",
                        [
                          { text: "Cancel", style: "cancel" },
                          { 
                            text: "Start Tracking", 
                            onPress: async () => {
                              try {
                                // Request motion sensor permissions if needed
                                const { status } = await Location.requestForegroundPermissionsAsync();
                                
                                if (status !== 'granted') {
                                  Alert.alert(
                                    "Permission Required", 
                                    "Sleep tracking requires motion sensor permissions.",
                                    [{ text: "OK" }]
                                  );
                                  return;
                                }
                                
                                // Start sleep tracking session
                                const sleepStartTime = new Date();
                                
                                // Store sleep start time in AsyncStorage
                                await AsyncStorage.setItem(
                                  `@sleep_tracking:${user?.email || 'user'}:start`,
                                  sleepStartTime.toISOString()
                                );
                                
                                // Set up accelerometer monitoring for movement detection
                                // This would normally use a device motion API, but we'll simulate it
                                
                                // Clear any existing interval
                                if (trainingIntervalRef.current) {
                                  clearInterval(trainingIntervalRef.current);
                                }
                                
                                // Set up sleep quality monitoring (simulated)
                                const sleepInterval = setInterval(() => {
                                  // Simulate sleep quality data
                                  // In a real app, this would analyze accelerometer data
                                  
                                  // Generate sleep quality metrics (0-100%)
                                  const deepSleepPercentage = Math.floor(Math.random() * 30) + 10; // 10-40%
                                  const lightSleepPercentage = Math.floor(Math.random() * 40) + 30; // 30-70%
                                  const remSleepPercentage = 100 - deepSleepPercentage - lightSleepPercentage;
                                  
                                  // Calculate duration so far
                                  const now = new Date();
                                  const durationMs = now - sleepStartTime;
                                  const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
                                  const durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
                                  
                                  // Update sleep metrics in AsyncStorage
                                  const sleepData = {
                                    startTime: sleepStartTime.toISOString(),
                                    currentTime: now.toISOString(),
                                    duration: `${durationHours}h ${durationMinutes}m`,
                                    deepSleep: deepSleepPercentage,
                                    lightSleep: lightSleepPercentage,
                                    remSleep: remSleepPercentage,
                                    inProgress: true
                                  };
                                  
                                  AsyncStorage.setItem(
                                    `@sleep_tracking:${user?.email || 'user'}:data`,
                                    JSON.stringify(sleepData)
                                  );
                                  
                                  // Update health metrics
                                  const updatedMetrics = [...healthMetrics];
                                  updatedMetrics[1].value = `${durationHours}h ${durationMinutes}m`;
                                  updatedMetrics[1].quality = Math.floor((deepSleepPercentage * 0.7) + (remSleepPercentage * 0.3));
                                  setHealthMetrics(updatedMetrics);
                                  
                                }, 60000); // Update every minute
                                
                                trainingIntervalRef.current = sleepInterval;
                                
                                // Navigate to sleep tracker
                                handleTabPress(2); // Index for sleep tab
                                
                                // Update UI to show tracking has started
                                Alert.alert(
                                  "Sleep Tracking Started", 
                                  "Your sleep patterns are now being monitored. Keep your device nearby while sleeping. To stop tracking, press the STOP button when you wake up."
                                );
                              } catch (error) {
                                console.error('Error starting sleep tracking:', error);
                                Alert.alert(
                                  "Error", 
                                  "Failed to start sleep tracking: " + error.message,
                                  [{ text: "OK" }]
                                );
                              }
                            }
                          }
                        ]
                      );
                      break;
                      
                    case 'Heart rate':
                      // Start heart rate monitoring with camera-based detection
                      Alert.alert(
                        "Start Heart Rate Monitoring",
                        "This will use your device's camera to monitor your heart rate. Place your finger over the camera lens when prompted.",
                        [
                          { text: "Cancel", style: "cancel" },
                          { 
                            text: "Start Monitoring", 
                            onPress: async () => {
                              try {
                                // Request camera permissions
                                const { status } = await Notifications.requestPermissionsAsync();
                                
                                if (status !== 'granted') {
                                  Alert.alert(
                                    "Permission Required", 
                                    "Heart rate monitoring requires camera permissions.",
                                    [{ text: "OK" }]
                                  );
                                  return;
                                }
                                
                                // Navigate to heart rate monitor
                                handleTabPress(0); // Index for realtime tab
                                
                                // In a real app, we would initialize the camera and process frames
                                // to detect pulse through changes in finger color
                                // For this demo, we'll simulate the process
                                
                                Alert.alert(
                                  "Place Your Finger", 
                                  "Place your finger over the camera lens. Keep it still for accurate readings.",
                                  [{ text: "OK" }]
                                );
                                
                                // Clear any existing interval
                                if (trainingIntervalRef.current) {
                                  clearInterval(trainingIntervalRef.current);
                                }
                                
                                // Simulate calibration period
                                setTimeout(() => {
                                  // Start heart rate monitoring simulation
                                  let baseHeartRate = Math.floor(Math.random() * 10) + 70; // Base rate between 70-80
                                  let min = baseHeartRate - 5;
                                  let max = baseHeartRate + 5;
                                  let readings = [];
                                  
                                  const heartRateInterval = setInterval(() => {
                                    // Generate realistic heart rate with small variations
                                    // This simulates the natural variability in heart rate
                                    const variation = Math.sin(Date.now() / 10000) * 3; // Slow sinusoidal variation
                                    const noise = (Math.random() - 0.5) * 2; // Random noise
                                    const newHeartRate = Math.round(baseHeartRate + variation + noise);
                                    
                                    // Keep track of min/max/avg
                                    readings.push(newHeartRate);
                                    if (readings.length > 20) readings.shift(); // Keep last 20 readings
                                    
                                    min = Math.min(min, newHeartRate);
                                    max = Math.max(max, newHeartRate);
                                    const avg = Math.round(readings.reduce((sum, val) => sum + val, 0) / readings.length);
                                    
                                    // Update state
                                    setUserData(prev => ({
                                      ...prev,
                                      heartRate: newHeartRate
                                    }));
                                    
                                    // Update health metrics
                                    const updatedMetrics = [...healthMetrics];
                                    updatedMetrics[2].value = newHeartRate.toString();
                                    updatedMetrics[2].min = min;
                                    updatedMetrics[2].avg = avg;
                                    updatedMetrics[2].maxVal = max;
                                    setHealthMetrics(updatedMetrics);
                                    
                                    // Save heart rate data to AsyncStorage
                                    const heartRateData = {
                                      timestamp: new Date().toISOString(),
                                      value: newHeartRate,
                                      min,
                                      max,
                                      avg
                                    };
                                    
                                    AsyncStorage.getItem(`@heart_rate:${user?.email || 'user'}`)
                                      .then(data => {
                                        const history = data ? JSON.parse(data) : [];
                                        history.push(heartRateData);
                                        // Keep only last 100 readings
                                        if (history.length > 100) history.shift();
                                        return AsyncStorage.setItem(
                                          `@heart_rate:${user?.email || 'user'}`,
                                          JSON.stringify(history)
                                        );
                                      })
                                      .catch(err => console.error('Error saving heart rate data:', err));
                                    
                                  }, 2000); // Update every 2 seconds
                                  
                                  trainingIntervalRef.current = heartRateInterval;
                                  
                                  // Update UI to show monitoring has started
                                  Alert.alert(
                                    "Heart Rate Monitoring Active", 
                                    "Your heart rate is now being monitored. Keep your finger steady on the camera for accurate readings."
                                  );
                                  
                                }, 3000); // 3 second calibration period
                              } catch (error) {
                                console.error('Error starting heart rate monitoring:', error);
                                Alert.alert(
                                  "Error", 
                                  "Failed to start heart rate monitoring: " + error.message,
                                  [{ text: "OK" }]
                                );
                                
                                // Fallback to simple simulation
                                const interval = setInterval(() => {
                                  // Generate random heart rate between 65-85
                                  const newHeartRate = Math.floor(Math.random() * 20) + 65;
                                  
                                  // Update state
                                  setUserData(prev => ({
                                    ...prev,
                                    heartRate: newHeartRate
                                  }));
                                  
                                  // Update health metrics
                                  const updatedMetrics = [...healthMetrics];
                                  updatedMetrics[2].value = newHeartRate.toString();
                                  setHealthMetrics(updatedMetrics);
                                }, 3000);
                                
                                // Store interval ID for cleanup
                                trainingIntervalRef.current = interval;
                                
                                handleTabPress(0); // Index for realtime tab
                                
                                // Update UI to show monitoring has started
                                Alert.alert(
                                  "Simulated Heart Rate Monitoring", 
                                  "Using simulated heart rate data as a fallback."
                                );
                              }
                            }
                          }
                        ]
                      );
                      break;
                      
                    case 'Blood pressure':
                      // Start blood pressure monitoring with external device integration
                      Alert.alert(
                        "Start Blood Pressure Monitoring",
                        "This will connect to your Bluetooth blood pressure monitor or simulate readings if no device is found.",
                        [
                          { text: "Cancel", style: "cancel" },
                          { 
                            text: "Start Monitoring", 
                            onPress: async () => {
                              try {
                                // In a real app, we would scan for Bluetooth devices here
                                // For this demo, we'll simulate the process
                                
                                // Show searching for devices
                                Alert.alert(
                                  "Searching for Devices", 
                                  "Looking for compatible blood pressure monitors...",
                                  [{ text: "OK" }]
                                );
                                
                                // Simulate device search
                                setTimeout(() => {
                                  // Simulate no devices found for demo purposes
                                  Alert.alert(
                                    "No Devices Found", 
                                    "No compatible blood pressure monitors were found. Would you like to use manual mode?",
                                    [
                                      { text: "Cancel", style: "cancel" },
                                      { 
                                        text: "Use Manual Mode", 
                                        onPress: () => {
                                          // Navigate to blood pressure tab
                                          handleTabPress(0);
                                          
                                          // Show measurement in progress
                                          Alert.alert(
                                            "Measurement in Progress", 
                                            "Please remain still while we take your blood pressure reading...",
                                            [{ text: "OK" }]
                                          );
                                          
                                          // Simulate measurement process
                                          setTimeout(() => {
                                            // Generate realistic blood pressure values based on age/fitness
                                            // For a healthy adult:
                                            const userAge = userData.age || 30;
                                            const userFitness = userData.fitnessLevel || 'Intermediate';
                                            
                                            // Base values
                                            let baseSystolic = 120;
                                            let baseDiastolic = 80;
                                            
                                            // Adjust for age
                                            if (userAge > 40) {
                                              baseSystolic += Math.floor((userAge - 40) / 5) * 2;
                                              baseDiastolic += Math.floor((userAge - 40) / 10) * 2;
                                            }
                                            
                                            // Adjust for fitness level
                                            if (userFitness === 'Beginner') {
                                              baseSystolic += 5;
                                              baseDiastolic += 3;
                                            } else if (userFitness === 'Advanced') {
                                              baseSystolic -= 5;
                                              baseDiastolic -= 3;
                                            }
                                            
                                            // Add some random variation
                                            const systolic = baseSystolic + Math.floor(Math.random() * 10) - 5;
                                            const diastolic = baseDiastolic + Math.floor(Math.random() * 8) - 4;
                                            const bloodPressure = `${systolic}/${diastolic}`;
                                            
                                            // Calculate blood oxygen level (95-100% for healthy individuals)
                                            const bloodOxygen = Math.floor(Math.random() * 5) + 95;
                                            
                                            // Calculate fatigue level (Low, Medium, High)
                                            const fatigueOptions = ['Low', 'Medium', 'High'];
                                            const fatigueIndex = Math.floor(Math.random() * 3);
                                            const fatigue = fatigueOptions[fatigueIndex];
                                            
                                            // Update state
                                            setUserData(prev => ({
                                              ...prev,
                                              bloodPressure,
                                              bloodOxygen,
                                              fatigue
                                            }));
                                            
                                            // Update health metrics
                                            const updatedMetrics = [...healthMetrics];
                                            updatedMetrics[3].value = bloodPressure;
                                            updatedMetrics[3].extra = {
                                              bloodOxygen,
                                              fatigue
                                            };
                                            setHealthMetrics(updatedMetrics);
                                            
                                            // Save blood pressure data to AsyncStorage
                                            const bpData = {
                                              timestamp: new Date().toISOString(),
                                              systolic,
                                              diastolic,
                                              bloodOxygen,
                                              fatigue
                                            };
                                            
                                            AsyncStorage.getItem(`@blood_pressure:${user?.email || 'user'}`)
                                              .then(data => {
                                                const history = data ? JSON.parse(data) : [];
                                                history.push(bpData);
                                                // Keep only last 50 readings
                                                if (history.length > 50) history.shift();
                                                return AsyncStorage.setItem(
                                                  `@blood_pressure:${user?.email || 'user'}`,
                                                  JSON.stringify(history)
                                                );
                                              })
                                              .catch(err => console.error('Error saving blood pressure data:', err));
                                            
                                            // Show results
                                            let bpCategory = 'Normal';
                                            let bpColor = '#4CAF50'; // Green
                                            
                                            if (systolic >= 140 || diastolic >= 90) {
                                              bpCategory = 'High';
                                              bpColor = '#F44336'; // Red
                                            } else if (systolic >= 130 || diastolic >= 85) {
                                              bpCategory = 'Elevated';
                                              bpColor = '#FF9800'; // Orange
                                            } else if (systolic <= 90 || diastolic <= 60) {
                                              bpCategory = 'Low';
                                              bpColor = '#2196F3'; // Blue
                                            }
                                            
                                            Alert.alert(
                                              "Blood Pressure Results",
                                              `Your blood pressure is ${bloodPressure} mmHg (${bpCategory}).\n\nBlood Oxygen: ${bloodOxygen}%\nFatigue Level: ${fatigue}`,
                                              [{ text: "OK" }]
                                            );
                                          }, 5000); // Simulate 5 second measurement
                                        }
                                      }
                                    ]
                                  );
                                }, 3000); // Simulate 3 second device search
                              } catch (error) {
                                console.error('Error starting blood pressure monitoring:', error);
                                Alert.alert(
                                  "Error", 
                                  "Failed to start blood pressure monitoring: " + error.message,
                                  [{ text: "OK" }]
                                );
                              }
                            }
                          }
                        ]
                      );
                      break;
                      
                    case 'Exercise':
                      // Start exercise tracking with options
                      Alert.alert(
                        "Start Exercise Tracking",
                        "Choose an exercise type to begin tracking:",
                        [
                          { text: "Cancel", style: "cancel" },
                          { 
                            text: "AI Workout", 
                            onPress: () => router.push('/ai-workout-generator')
                          },
                          { 
                            text: "Running", 
                            onPress: async () => {
                              try {
                                // Request location permissions for GPS tracking
                                const { status } = await Location.requestForegroundPermissionsAsync();
                                
                                if (status !== 'granted') {
                                  Alert.alert(
                                    "Permission Required", 
                                    "Running tracking requires location permissions for GPS.",
                                    [{ text: "OK" }]
                                  );
                                  return;
                                }
                                
                                // Start location tracking
                                Alert.alert(
                                  "Starting Run Tracking", 
                                  "GPS tracking will begin when you press Start. Your route, pace, and calories will be tracked.",
                                  [
                                    { text: "Cancel", style: "cancel" },
                                    { 
                                      text: "Start", 
                                      onPress: () => {
                                        // In a real app, we would start GPS tracking here
                                        // For this demo, we'll simulate the process
                                        
                                        // Navigate to exercise tab
                                        handleTabPress(4);
                                        
                                        // Clear any existing interval
                                        if (trainingIntervalRef.current) {
                                          clearInterval(trainingIntervalRef.current);
                                        }
                                        
                                        // Initialize run data
                                        const runData = {
                                          startTime: new Date().toISOString(),
                                          distance: 0,
                                          pace: 0,
                                          calories: 0,
                                          route: [],
                                          inProgress: true
                                        };
                                        
                                        // Save initial run data
                                        AsyncStorage.setItem(
                                          `@current_run:${user?.email || 'user'}`,
                                          JSON.stringify(runData)
                                        );
                                        
                                        // Simulate run tracking
                                        const runInterval = setInterval(() => {
                                          // Simulate distance increase (0.1-0.2 km per minute)
                                          const distanceIncrement = (Math.random() * 0.1 + 0.1) / 6; // Per 10 seconds
                                          
                                          // Update run data
                                          AsyncStorage.getItem(`@current_run:${user?.email || 'user'}`)
                                            .then(data => {
                                              const currentRun = JSON.parse(data);
                                              const updatedRun = {
                                                ...currentRun,
                                                distance: currentRun.distance + distanceIncrement,
                                                pace: (currentRun.distance > 0) ? 
                                                  (new Date() - new Date(currentRun.startTime)) / (currentRun.distance * 60000) : 0,
                                                calories: Math.round(currentRun.distance * 65), // ~65 calories per km
                                                route: [
                                                  ...currentRun.route,
                                                  { 
                                                    latitude: 37.7749 + (Math.random() - 0.5) * 0.01, 
                                                    longitude: -122.4194 + (Math.random() - 0.5) * 0.01,
                                                    timestamp: new Date().toISOString()
                                                  }
                                                ]
                                              };
                                              
                                              // Update health metrics
                                              const updatedMetrics = [...healthMetrics];
                                              updatedMetrics[4].value = updatedRun.distance.toFixed(2) + ' km';
                                              updatedMetrics[4].calories = updatedRun.calories;
                                              updatedMetrics[4].pace = updatedRun.pace.toFixed(2) + ' min/km';
                                              setHealthMetrics(updatedMetrics);
                                              
                                              return AsyncStorage.setItem(
                                                `@current_run:${user?.email || 'user'}`,
                                                JSON.stringify(updatedRun)
                                              );
                                            })
                                            .catch(err => console.error('Error updating run data:', err));
                                          
                                        }, 10000); // Update every 10 seconds
                                        
                                        trainingIntervalRef.current = runInterval;
                                        
                                        // Show tracking started notification
                                        Alert.alert(
                                          "Run Tracking Started", 
                                          "Your run is now being tracked. Press STOP when finished."
                                        );
                                      }
                                    }
                                  ]
                                );
                              } catch (error) {
                                console.error('Error starting run tracking:', error);
                                Alert.alert(
                                  "Error", 
                                  "Failed to start run tracking: " + error.message,
                                  [{ text: "OK" }]
                                );
                              }
                            }
                          },
                          { 
                            text: "Strength Training", 
                            onPress: () => {
                              // Navigate to strength training screen
                              router.push({
                                pathname: '/Home',
                                params: {
                                  workoutType: 'Strength',
                                  workoutData: JSON.stringify({
                                    name: 'Custom Strength Workout',
                                    exercises: [
                                      { name: 'Push-ups', sets: 3, reps: 12 },
                                      { name: 'Squats', sets: 3, reps: 15 },
                                      { name: 'Plank', sets: 3, duration: '30 sec' },
                                      { name: 'Lunges', sets: 3, reps: 10 },
                                      { name: 'Dumbbell Rows', sets: 3, reps: 12 }
                                    ]
                                  })
                                }
                              });
                            }
                          }
                        ]
                      );
                      break;
                      
                    default:
                      Alert.alert("Feature Coming Soon", "This feature will be available in the next update.");
                  }
                }}
              >
                <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 16 }}>START</Text>
              </TouchableOpacity>
            </View>
          </View>
          {/* Today's Progress Section */}
          <View style={{ marginHorizontal: 12, marginTop: 20, marginBottom: 20 }}>
            <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold', marginBottom: 16, textAlign: 'center', fontFamily: 'Poppins_600SemiBold' }}>Today's Progress</Text>
            <View style={{ 
              flexDirection: 'row', 
              justifyContent: 'space-between', 
              alignItems: 'stretch',
              paddingHorizontal: 4
            }}>
              {trackerCards.map((card, i) => (
                  <AnimatedTouchable
                    key={card.key}
                    onPress={card.onPress}
                    activeOpacity={0.8}
                    style={{
                    flex: 1,
                    backgroundColor: 'rgba(255,255,255,0.1)',
                    borderRadius: 12,
                    paddingVertical: 12,
                    paddingHorizontal: 6,
                    marginHorizontal: 3,
                      alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: 100,
                      borderWidth: 1,
                    borderColor: 'rgba(255,255,255,0.1)',
                  }}
                >
                  <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                    <Ionicons name={card.icon} size={22} color={card.color} style={{ marginBottom: 4 }} />
                    <Text style={{ 
                      color: card.color, 
                      fontWeight: '600', 
                      fontSize: 10, 
                      textAlign: 'center',
                      marginBottom: 2,
                      fontFamily: 'Poppins_600SemiBold'
                    }}>
                      {card.label}
                      </Text>
                    <Text style={{ 
                      color: '#fff', 
                      fontSize: 12, 
                      fontWeight: 'bold', 
                      marginBottom: 1,
                      fontFamily: 'Poppins_700Bold'
                    }}>
                      {card.value}
                    </Text>
                    <Text style={{ 
                      color: '#bbb', 
                      fontSize: 9,
                      fontFamily: 'Poppins_400Regular'
                    }}>
                        {card.valueLabel}
                      </Text>
                    {card.editable && (
                      <TouchableOpacity 
                        onPress={() => { 
                          setEditModal({ open: true, type: card.key }); 
                          setManualValue(''); 
                        }} 
                        style={{ 
                          marginTop: 4,
                          padding: 2
                        }}
                      >
                        <Ionicons name="create-outline" size={12} color="#fff" />
                      </TouchableOpacity>
                    )}
                    </View>
                  </AnimatedTouchable>
              ))}
          </View>
          </View>
          


        </ScrollView>
        {/* Bottom Navigation Bar */}
        <View style={styles.bottomNavBar}> 
          {bottomNav.slice(0, 2).map((item, idx) => (
            <TouchableOpacity 
              key={item.label} 
              style={[
                styles.bottomNavButton, 
                { 
                  borderRadius: 12, 
                  backgroundColor: tabIndex === idx ? 'rgba(102,126,234,0.12)' : 'transparent',
                  paddingVertical: 8
                }
              ]} 
              onPress={item.onPress}
            >
              <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                {item.iconLib === MaterialCommunityIcons
                  ? <MaterialCommunityIcons name={item.icon} size={22} color={tabIndex === idx ? '#667eea' : '#fff'} />
                  : <Ionicons name={item.icon} size={22} color={tabIndex === idx ? '#667eea' : '#fff'} />}
                <Text style={[styles.bottomNavText, { color: tabIndex === idx ? '#667eea' : '#fff' }]}>{item.label}</Text>
              </View>
            </TouchableOpacity>
          ))}
          {/* Floating Action Button */}
          <TouchableOpacity
            style={{
              width: 56,
              height: 56,
              borderRadius: 28,
              backgroundColor: '#667eea',
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 25,
              elevation: 0, // Removed elevation
              zIndex: 20,
              borderWidth: 1,
              borderColor: 'rgba(255,255,255,0.2)',
              position: 'relative',
              top: -28,
            }}
            onPress={() => router.push('/Home')}
            activeOpacity={0.92}
          >
            <Ionicons name="play" size={28} color="#fff" />
          </TouchableOpacity>
          {bottomNav.slice(2).map((item, idx) => (
            <TouchableOpacity 
              key={item.label} 
              style={[
                styles.bottomNavButton, 
                { 
                  borderRadius: 12, 
                  backgroundColor: tabIndex === idx + 2 ? 'rgba(102,126,234,0.12)' : 'transparent',
                  paddingVertical: 8
                }
              ]} 
              onPress={item.onPress}
            >
              <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                {item.iconLib === MaterialCommunityIcons
                  ? <MaterialCommunityIcons name={item.icon} size={22} color={tabIndex === idx + 2 ? '#667eea' : '#fff'} />
                  : <Ionicons name={item.icon} size={22} color={tabIndex === idx + 2 ? '#667eea' : '#fff'} />}
                <Text style={[styles.bottomNavText, { color: tabIndex === idx + 2 ? '#667eea' : '#fff' }]}>{item.label}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
        {/* Add modal for manual input */}
        <Modal
          visible={editModal.open}
          transparent
          animationType="fade"
          onRequestClose={() => setEditModal({ open: false, type: null })}
        >
          <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', alignItems: 'center' }}>
            <View style={{ backgroundColor: '#222', borderRadius: 16, padding: 24, width: 300, alignItems: 'center' }}>
              <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold', marginBottom: 12 }}>
                Enter {editModal.type === 'realtime' ? 'Heart Rate (BPM)' : editModal.type === 'sleep' ? 'Sleep Hours' : editModal.type === 'steps' ? 'Steps' : editModal.type === 'calories' ? 'Calories' : editModal.type === 'gps' ? 'Distance (km)' : ''}
              </Text>
              <TextInput
                style={{ backgroundColor: '#333', color: '#fff', borderRadius: 8, padding: 10, width: '100%', fontSize: 16, marginBottom: 16 }}
                placeholder={editModal.type === 'realtime' ? 'e.g. 72' : editModal.type === 'sleep' ? 'e.g. 7.5' : editModal.type === 'steps' ? 'e.g. 10000' : editModal.type === 'calories' ? 'e.g. 500' : editModal.type === 'gps' ? 'e.g. 5' : ''}
                placeholderTextColor="#bbb"
                keyboardType="numeric"
                value={manualValue}
                onChangeText={setManualValue}
                autoFocus
              />
              <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' }}>
                <TouchableOpacity
                  style={{ backgroundColor: '#7ed957', borderRadius: 8, paddingVertical: 10, paddingHorizontal: 18, marginRight: 10 }}
                  onPress={async () => {
                    if (!manualValue) return;
                    const value = editModal.type === 'realtime' ? parseInt(manualValue)
                      : editModal.type === 'sleep' ? parseFloat(manualValue)
                      : editModal.type === 'steps' ? parseInt(manualValue)
                      : editModal.type === 'calories' ? parseInt(manualValue)
                      : editModal.type === 'gps' ? parseFloat(manualValue) : null;
                    if (isNaN(value) || value <= 0) return;
                    if (editModal.type === 'realtime') {
                      setUserData(prev => ({ ...prev, heartRate: value }));
                      if (user?.email) await AsyncStorage.setItem(`@heartRate:${user.email}:${new Date().toISOString().split('T')[0]}`, value.toString());
                    } else if (editModal.type === 'sleep') {
                      setUserData(prev => ({ ...prev, sleepHours: value }));
                      if (user?.email) await AsyncStorage.setItem(`@sleep:${user.email}:${new Date().toISOString().split('T')[0]}`, value.toString());
                    } else if (editModal.type === 'steps') {
                      setUserData(prev => ({ ...prev, totalSteps: value }));
                      if (user?.email) await AsyncStorage.setItem(`@steps:${user.email}:${new Date().toISOString().split('T')[0]}`, value.toString());
                    } else if (editModal.type === 'calories') {
                      setUserData(prev => ({ ...prev, caloriesBurned: value }));
                      if (user?.email) await AsyncStorage.setItem(`@calories:${user.email}:${new Date().toISOString().split('T')[0]}`, value.toString());
                    } else if (editModal.type === 'gps') {
                      setUserData(prev => ({ ...prev, distance: value }));
                      if (user?.email) await AsyncStorage.setItem(`@distance:${user.email}:${new Date().toISOString().split('T')[0]}`, value.toString());
                    }
                    setEditModal({ open: false, type: null });
                  }}
                >
                  <Text style={{ color: '#222', fontWeight: 'bold', fontSize: 16 }}>Save</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={{ backgroundColor: '#F44336', borderRadius: 8, paddingVertical: 10, paddingHorizontal: 18 }}
                  onPress={() => setEditModal({ open: false, type: null })}
                >
                  <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 16 }}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  scrollContainer: {
    paddingBottom: Platform.OS === 'android' ? 140 : 120, // Increased padding for larger TabView
    paddingTop: Platform.OS === 'android' ? 15 : 10,
    flexGrow: 1,
    paddingHorizontal: Platform.OS === 'android' ? 5 : 0, // Add horizontal padding for Android
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'android' ? 25 : 10, // More padding for Android status bar
    marginBottom: 15,
  },
  greetingContainer: {
    flex: 1,
  },
  greeting: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 4,
  },
  greetingSubtext: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.7)',
  },
  menuIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerBar: {
    marginTop: Platform.OS === 'android' ? 80 : 0,
    marginBottom: 10,
    paddingHorizontal: 18,
  },
  welcomeBox: {
    marginBottom: 10,
  },
  welcomeText: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    backgroundColor: 'rgba(255,255,255,0.03)', // Subtle background
    paddingBottom: 15,
    marginHorizontal: Platform.OS === 'android' ? 5 : 0,
  },
  welcomeSubtext: {
    fontSize: 15,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 8,
  },
  menuBar: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 6,
  },
  menuButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 18,
    paddingHorizontal: 12,
    paddingVertical: 7,
    marginRight: 8,
    marginBottom: 2,
    elevation: 0, // Removed elevation
  },
  menuButtonText: {
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    fontSize: 14,
    marginLeft: 7,
  },
  trackerBox: {
    marginHorizontal: 10,
    marginVertical: 10,
    borderRadius: 18,
    overflow: 'hidden',
    backgroundColor: 'rgba(255,255,255,0.04)',
    elevation: 0, // Removed elevation
  },
  trackerTabsContainer: {
    flex: 1,
    minHeight: 600, // Significantly increased height for full visibility
    marginBottom: Platform.OS === 'android' ? 25 : 20,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: 'rgba(255,255,255,0.03)', // Subtle background
    paddingBottom: 20,
    marginHorizontal: 10,
    marginTop: 10,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.1)',
  },
  quickActionsGridBox: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 16,
    marginTop: 10,
    marginBottom: 20,
    gap: 10,
  },
  quickActionCard: {
    flex: 1,
    minWidth: 70,
    maxWidth: '23%',
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 14,
    paddingVertical: 16,
    paddingHorizontal: 8,
    alignItems: 'center',
    marginHorizontal: 4,
    marginBottom: 8,
    elevation: 2,
  },
  quickActionText: {
    color: '#fff',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 13,
    marginTop: 8,
    textAlign: 'center',
    letterSpacing: 0.3,
  },
  bottomNavBar: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: 'rgba(20,20,30,0.98)',
    paddingVertical: Platform.OS === 'android' ? 10 : 8,
    paddingHorizontal: 12,
    paddingBottom: Platform.OS === 'android' ? 18 : 15,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.1)',
    elevation: 0,
    zIndex: 10,
  },
  bottomNavButton: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    paddingVertical: Platform.OS === 'android' ? 10 : 8,
    paddingHorizontal: Platform.OS === 'android' ? 6 : 4,
    borderRadius: 12,
    minHeight: 60,
  },
  bottomNavText: {
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    fontSize: Platform.OS === 'android' ? 11 : 10,
    marginTop: Platform.OS === 'android' ? 6 : 5,
    letterSpacing: 0.2,
    textAlign: 'center',
    lineHeight: Platform.OS === 'android' ? 14 : 13,
  },
  footer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    paddingVertical: 8,
    backgroundColor: 'rgba(0,0,0,0.18)',
    zIndex: 20,
  },
  footerText: {
    color: '#fff',
    fontFamily: 'Poppins_400Regular',
    fontSize: 12,
    opacity: 0.7,
  },
  glowCircle: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    elevation: 0, // Removed elevation
    zIndex: 0,
  },
  menuIconButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255,255,255,0.08)',
    alignSelf: 'flex-end',
    marginLeft: 10,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuModalContainer: {
    backgroundColor: '#1e1e2d',
    borderRadius: 16,
    padding: 20,
    width: '80%',
    maxWidth: 320,
    alignItems: 'flex-start',
    elevation: 0, // Removed elevation
  },
  menuModalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    width: '100%',
    borderRadius: 10,
    marginVertical: 4,
  },
  menuModalText: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginLeft: 16,
  },
  menuModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingBottom: 10,
  },
  menuModalTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginLeft: 8,
  },
  menuModalCloseButton: {
    padding: 4,
  },
  menuModalDivider: {
    height: 1,
    backgroundColor: 'rgba(255,255,255,0.1)',
    width: '100%',
    marginBottom: 10,
  },
  logoutButton: {
    marginTop: 10,
    backgroundColor: 'rgba(255,82,82,0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255,82,82,0.3)',
  },
  logoutText: {
    color: '#FF5252',
  },
  bottomNavCopyright: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 2,
    textAlign: 'center',
    color: '#fff',
    fontFamily: 'Poppins_400Regular',
    fontSize: 11,
    opacity: 0.7,
  },
  tabBarWrapper: {
    marginTop: 10,
    marginBottom: 10,
  },
  tabBarContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(30,30,50,0.85)',
    borderRadius: 16,
    marginHorizontal: 10,
    marginTop: 10,
    paddingVertical: 8,
    paddingHorizontal: 5,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
    elevation: 0, // Removed elevation
  },
  tabBarItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    marginHorizontal: 3,
    borderRadius: 12,
    height: 70,
  },
  tabBarItemActive: {
    backgroundColor: 'rgba(102,126,234,0.15)',
    borderRadius: 12,
    // Remove shadows for cleaner look
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.3)',
  },
  tabBarLabel: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 11,
    fontWeight: '600',
    marginTop: 6,
    fontFamily: 'Poppins_600SemiBold',
    textAlign: 'center',
    letterSpacing: 0.3,
  },
  tabBarLabelActive: {
    color: '#667eea',
    fontFamily: 'Poppins_700Bold',
    fontSize: 12,
  },
  tabBadge: {
    position: 'absolute',
    top: -4,
    right: -10,
    backgroundColor: '#ff4d4f',
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 3,
  },
  tabBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  // New styles for tracker boxes
  trackerBoxContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  trackerBox: {
    width: '18%',
    aspectRatio: 1,
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  trackerBoxText: {
    color: '#fff',
    fontSize: 12,
    marginTop: 8,
    fontFamily: 'Poppins_400Regular',
    textAlign: 'center',
  },
  // Health trackers styles
  healthTrackersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  healthTrackerItem: {
    width: '18%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  healthTrackerText: {
    color: '#fff',
    fontSize: 12,
    marginTop: 8,
    fontFamily: 'Poppins_400Regular',
    textAlign: 'center',
  },
  // Today's progress styles
  todayProgressContainer: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  progressItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  progressContent: {
    flex: 1,
  },
  progressLabel: {
    color: '#fff',
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    marginBottom: 4,
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 3,
    marginBottom: 4,
  },
  progressBar: {
    height: '100%',
    borderRadius: 3,
  },
  progressValue: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
  },
});