const fs = require('fs');
const path = require('path');

// List of potentially problematic image files
const problematicImages = [
  'muscle.png',
  'run.png', 
  'leaf.png',
  'lifter.png',
  'food.png',
  'cardio.png'
];

// Check file sizes and log potential issues
console.log('Checking image files for potential issues...\n');

problematicImages.forEach(filename => {
  const filePath = path.join(__dirname, '../assets/images', filename);
  
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    const sizeInKB = Math.round(stats.size / 1024);
    
    console.log(`${filename}:`);
    console.log(`  Size: ${sizeInKB} KB`);
    
    if (sizeInKB > 500) {
      console.log(`  ⚠️  WARNING: File is large (${sizeInKB} KB) - may cause loading issues`);
    }
    
    if (sizeInKB < 1) {
      console.log(`  ⚠️  WARNING: File is very small (${sizeInKB} KB) - may be corrupted`);
    }
    
    console.log('');
  } else {
    console.log(`${filename}: ❌ File not found`);
  }
});

console.log('Recommendations:');
console.log('1. Consider compressing large PNG files to reduce size');
console.log('2. Convert problematic files to JPEG format for better compatibility');
console.log('3. Use the updated SafeImage component with better error handling');
console.log('4. Replace problematic images with more reliable alternatives'); 