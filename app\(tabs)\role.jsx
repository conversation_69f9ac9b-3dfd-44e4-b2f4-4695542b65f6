<<<<<<<
import React, { useState } from 'react';



import { ScrollView, StyleSheet, View, TouchableOpacity, Alert } from 'react-native';



import { ThemedView } from '@/components/ThemedView';



import { ThemedText } from '@/components/ThemedText';



import SafeImage from '@/components/SafeImage';



import { useThemeColor } from '@/hooks/useThemeColor';







const Role = () => {



  const [userStats, setUserStats] = useState({



    workoutsCompleted: 42,



    totalMinutes: 1250,



    currentStreak: 7,



    favoriteExercises: 12,



  });







  const backgroundColor = useThemeColor({}, 'background');



  const textColor = useThemeColor({}, 'text');



  const tintColor = useThemeColor({}, 'tint');







  // User profile data



  const userProfile = {



    name: '<PERSON>',



    email: '<EMAIL>',



    avatar: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400&h=300&fit=crop',



    fallbackAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=300&fit=crop',



    joinDate: 'January 2024',



    fitnessLevel: 'Intermediate',



    goals: ['Weight Loss', 'Muscle Building', 'Endurance'],



  };







  // Achievement data



  const achievements = [



    {



      id: '1',



      title: 'First Workout',



      description: 'Completed your first workout session',



      icon: '🏃‍♂️',



      unlocked: true,



    },



    {



      id: '2',



      title: 'Week Warrior',



      description: 'Maintained a 7-day workout streak',



      icon: '🔥',



      unlocked: true,



    },



    {



      id: '3',



      title: 'Push-up Pro',



      description: 'Completed 100 push-ups in a session',



      icon: '💪',



      unlocked: true,



    },



    {



      id: '4',



      title: 'Marathon Master',



      description: 'Complete 1000 minutes of workouts',



      icon: '🏆',



      unlocked: true,



    },



    {



      id: '5',



      title: 'Consistency King',



      description: 'Maintain a 30-day workout streak',



      icon: '👑',



      unlocked: false,



    },



    {



      id: '6',



      title: 'Strength Legend',



      description: 'Complete 100 strength workouts',



      icon: '⚡',



      unlocked: false,



    },



  ];







  const handleEditProfile = () => {



    Alert.alert('Edit Profile', 'Profile editing functionality coming soon!');



  };







  const handleSettingsPress = () => {



    Alert.alert('Settings', 'Settings page coming soon!');



  };







  const handleAchievementPress = (achievement) => {



    Alert.alert(



      achievement.title,



      achievement.description,



      [{ text: 'OK' }]



    );



  };







  const renderStatCard = (title, value, subtitle) => (



    <View style={[styles.statCard, { backgroundColor }]}>



      <ThemedText style={styles.statValue}>{value}</ThemedText>



      <ThemedText style={styles.statTitle}>{title}</ThemedText>



      {subtitle && (



        <ThemedText style={styles.statSubtitle}>{subtitle}</ThemedText>



      )}



    </View>



  );







  const renderAchievement = (achievement) => (



    <TouchableOpacity



      key={achievement.id}



      style={[



        styles.achievementCard,



        { 



          backgroundColor,



          opacity: achievement.unlocked ? 1 : 0.6,



        }



      ]}



      onPress={() => handleAchievementPress(achievement)}



      activeOpacity={0.8}



    >



      <View style={styles.achievementIcon}>



        <ThemedText style={styles.achievementEmoji}>



          {achievement.icon}



        </ThemedText>



      </View>



      <View style={styles.achievementInfo}>



        <ThemedText style={[



          styles.achievementTitle,



          { color: achievement.unlocked ? textColor : `${textColor}80` }



        ]}>



          {achievement.title}



        </ThemedText>



        <ThemedText style={[



          styles.achievementDescription,



          { color: achievement.unlocked ? textColor : `${textColor}60` }



        ]}>



          {achievement.description}



        </ThemedText>



      </View>



      {achievement.unlocked && (



        <View style={styles.unlockedBadge}>



          <ThemedText style={styles.unlockedText}>✓</ThemedText>



        </View>



      )}



    </TouchableOpacity>



  );







  return (



    <ScrollView style={[styles.container, { backgroundColor }]}>



      <ThemedView style={styles.content}>



        {/* Profile Header */}



        <View style={styles.profileHeader}>



          <View style={styles.avatarContainer}>



            <SafeImage



              source={{ uri: userProfile.avatar }}



              fallbackSource={{ uri: userProfile.fallbackAvatar }}



              style={styles.avatar}



              resizeMode="cover"



              showLoadingIndicator={true}



              errorText="Avatar not available"



            />



          </View>



          



          <View style={styles.profileInfo}>



            <ThemedText type="title" style={styles.userName}>



              {userProfile.name}



            </ThemedText>



            <ThemedText style={styles.userEmail}>



              {userProfile.email}



            </ThemedText>



            <ThemedText style={styles.userDetails}>



              {userProfile.fitnessLevel} • Joined {userProfile.joinDate}



            </ThemedText>



          </View>



          



          <TouchableOpacity



            style={[styles.editButton, { borderColor: tintColor }]}



            onPress={handleEditProfile}



          >



            <ThemedText style={[styles.editButtonText, { color: tintColor }]}>



              Edit



            </ThemedText>



          </TouchableOpacity>



        </View>







        {/* Goals Section */}



        <View style={styles.section}>



          <ThemedText type="subtitle" style={styles.sectionTitle}>



            Fitness Goals



          </ThemedText>



          <View style={styles.goalsContainer}>



            {userProfile.goals.map((goal, index) => (



              <View key={index} style={[styles.goalTag, { backgroundColor: `${tintColor}20` }]}>



                <ThemedText style={[styles.goalText, { color: tintColor }]}>



                  {goal}



                </ThemedText>



              </View>



            ))}



          </View>



        </View>







        {/* Stats Section */}



        <View style={styles.section}>



          <ThemedText type="subtitle" style={styles.sectionTitle}>



            Your Progress



          </ThemedText>



          <View style={styles.statsGrid}>



            {renderStatCard('Workouts', userStats.workoutsCompleted, 'Completed')}



            {renderStatCard('Minutes', userStats.totalMinutes, 'Total time')}



            {renderStatCard('Streak', userStats.currentStreak, 'Days')}



            {renderStatCard('Favorites', userStats.favoriteExercises, 'Exercises')}



          </View>



        </View>







        {/* Achievements Section */}



        <View style={styles.section}>



          <ThemedText type="subtitle" style={styles.sectionTitle}>



            Achievements



          </ThemedText>



          <View style={styles.achievementsContainer}>



            {achievements.map(renderAchievement)}



          </View>



        </View>







        {/* Settings Button */}



        <TouchableOpacity



          style={[styles.settingsButton, { backgroundColor: tintColor }]}



          onPress={handleSettingsPress}



        >



          <ThemedText style={styles.settingsButtonText}>



            Settings & Preferences



          </ThemedText>



        </TouchableOpacity>



      </ThemedView>



    </ScrollView>



  );



};







const styles = StyleSheet.create({



  container: {



    flex: 1,



  },



  content: {



    padding: 16,



  },



  profileHeader: {



    flexDirection: 'row',



    alignItems: 'center',



    marginBottom: 32,



    paddingBottom: 16,



    borderBottomWidth: 1,



    borderBottomColor: '#E0E0E0',



  },



  avatarContainer: {



    marginRight: 16,



  },



  avatar: {



    width: 80,



    height: 80,



    borderRadius: 40,



  },



  profileInfo: {



    flex: 1,



  },



  userName: {



    marginBottom: 4,



  },



  userEmail: {



    opacity: 0.7,



    marginBottom: 4,



  },



  userDetails: {



    fontSize: 12,



    opacity: 0.6,



  },



  editButton: {



    paddingHorizontal: 16,



    paddingVertical: 8,



    borderWidth: 1,



    borderRadius: 20,



  },



  editButtonText: {



    fontSize: 14,



    fontWeight: '500',



  },



  section: {



    marginBottom: 32,



  },



  sectionTitle: {



    marginBottom: 16,



    fontWeight: '600',



  },



  goalsContainer: {



    flexDirection: 'row',



    flexWrap: 'wrap',



    gap: 8,



  },



  goalTag: {



    paddingHorizontal: 12,



    paddingVertical: 6,



    borderRadius: 16,



  },



  goalText: {



    fontSize: 14,



    fontWeight: '500',



  },



  statsGrid: {



    flexDirection: 'row',



    flexWrap: 'wrap',



    justifyContent: 'space-between',



    gap: 12,



  },



  statCard: {



    width: '48%',



    padding: 16,



    borderRadius: 12,



    alignItems: 'center',



    elevation: 1,



    shadowColor: '#000',



    shadowOffset: { width: 0, height: 1 },



    shadowOpacity: 0.1,



    shadowRadius: 2,



  },



  statValue: {



    fontSize: 24,



    fontWeight: 'bold',



    marginBottom: 4,



  },



  statTitle: {



    fontSize: 14,



    fontWeight: '500',



    marginBottom: 2,



  },



  statSubtitle: {



    fontSize: 12,



    opacity: 0.6,



  },



  achievementsContainer: {



    gap: 12,



  },



  achievementCard: {



    flexDirection: 'row',



    alignItems: 'center',



    padding: 16,



    borderRadius: 12,



    elevation: 1,



    shadowColor: '#000',



    shadowOffset: { width: 0, height: 1 },



    shadowOpacity: 0.1,



    shadowRadius: 2,



  },



  achievementIcon: {



    width: 40,



    height: 40,



    borderRadius: 20,



    justifyContent: 'center',



    alignItems: 'center',



    marginRight: 12,



  },



  achievementEmoji: {



    fontSize: 20,



  },



  achievementInfo: {



    flex: 1,



  },



  achievementTitle: {



    fontSize: 16,



    fontWeight: '600',



    marginBottom: 4,



  },



  achievementDescription: {



    fontSize: 14,



    opacity: 0.8,



  },



  unlockedBadge: {



    width: 24,



    height: 24,



    borderRadius: 12,



    backgroundColor: '#4CAF50',



    justifyContent: 'center',



    alignItems: 'center',



  },



  unlockedText: {



    color: 'white',



    fontSize: 12,



    fontWeight: 'bold',



  },



  settingsButton: {



    padding: 16,



    borderRadius: 12,



    alignItems: 'center',



    marginTop: 16,



  },



  settingsButtonText: {



    color: 'white',



    fontSize: 16,



    fontWeight: '600',



  },



});







export default Role;



=======
import React, { useState } from 'react';

import { ScrollView, StyleSheet, View, TouchableOpacity, Alert } from 'react-native';

import { ThemedView } from '@/components/ThemedView';

import { ThemedText } from '@/components/ThemedText';

import SafeImage from '@/components/SafeImage';

import { useThemeColor } from '@/hooks/useThemeColor';



const Role = () => {

  const [userStats, setUserStats] = useState({

    workoutsCompleted: 42,

    totalMinutes: 1250,

    currentStreak: 7,

    favoriteExercises: 12,

  });



  const backgroundColor = useThemeColor({}, 'background');

  const textColor = useThemeColor({}, 'text');

  const tintColor = useThemeColor({}, 'tint');



  // User profile data

  const userProfile = {

    name: 'John Doe',

    email: '<EMAIL>',

    avatar: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400&h=300&fit=crop',

    fallbackAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=300&fit=crop',

    joinDate: 'January 2024',

    fitnessLevel: 'Intermediate',

    goals: ['Weight Loss', 'Muscle Building', 'Endurance'],

  };



  // Achievement data

  const achievements = [

    {

      id: '1',

      title: 'First Workout',

      description: 'Completed your first workout session',

      icon: '🏃‍♂️',

      unlocked: true,

    },

    {

      id: '2',

      title: 'Week Warrior',

      description: 'Maintained a 7-day workout streak',

      icon: '🔥',

      unlocked: true,

    },

    {

      id: '3',

      title: 'Push-up Pro',

      description: 'Completed 100 push-ups in a session',

      icon: '💪',

      unlocked: true,

    },

    {

      id: '4',

      title: 'Marathon Master',

      description: 'Complete 1000 minutes of workouts',

      icon: '🏆',

      unlocked: true,

    },

    {

      id: '5',

      title: 'Consistency King',

      description: 'Maintain a 30-day workout streak',

      icon: '👑',

      unlocked: false,

    },

    {

      id: '6',

      title: 'Strength Legend',

      description: 'Complete 100 strength workouts',

      icon: '⚡',

      unlocked: false,

    },

  ];



  const handleEditProfile = () => {

    Alert.alert('Edit Profile', 'Profile editing functionality coming soon!');

  };



  const handleSettingsPress = () => {

    Alert.alert('Settings', 'Settings page coming soon!');

  };



  const handleAchievementPress = (achievement) => {

    Alert.alert(

      achievement.title,

      achievement.description,

      [{ text: 'OK' }]

    );

  };



  const renderStatCard = (title, value, subtitle) => (

    <View style={[styles.statCard, { backgroundColor }]}>

      <ThemedText style={styles.statValue}>{value}</ThemedText>

      <ThemedText style={styles.statTitle}>{title}</ThemedText>

      {subtitle && (

        <ThemedText style={styles.statSubtitle}>{subtitle}</ThemedText>

      )}

    </View>

  );



  const renderAchievement = (achievement) => (

    <TouchableOpacity

      key={achievement.id}

      style={[

        styles.achievementCard,

        { 

          backgroundColor,

          opacity: achievement.unlocked ? 1 : 0.6,

        }

      ]}

      onPress={() => handleAchievementPress(achievement)}

      activeOpacity={0.8}

    >

      <View style={styles.achievementIcon}>

        <ThemedText style={styles.achievementEmoji}>

          {achievement.icon}

        </ThemedText>

      </View>

      <View style={styles.achievementInfo}>

        <ThemedText style={[

          styles.achievementTitle,

          { color: achievement.unlocked ? textColor : `${textColor}80` }

        ]}>

          {achievement.title}

        </ThemedText>

        <ThemedText style={[

          styles.achievementDescription,

          { color: achievement.unlocked ? textColor : `${textColor}60` }

        ]}>

          {achievement.description}

        </ThemedText>

      </View>

      {achievement.unlocked && (

        <View style={styles.unlockedBadge}>

          <ThemedText style={styles.unlockedText}>✓</ThemedText>

        </View>

      )}

    </TouchableOpacity>

  );



  return (

    <ScrollView style={[styles.container, { backgroundColor }]}>

      <ThemedView style={styles.content}>

        {/* Profile Header */}

        <View style={styles.profileHeader}>

          <View style={styles.avatarContainer}>

            <SafeImage

              source={{ uri: userProfile.avatar }}

              fallbackSource={{ uri: userProfile.fallbackAvatar }}

              style={styles.avatar}

              resizeMode="cover"

              showLoadingIndicator={true}

              errorText="Avatar not available"

            />

          </View>

          

          <View style={styles.profileInfo}>

            <ThemedText type="title" style={styles.userName}>

              {userProfile.name}

            </ThemedText>

            <ThemedText style={styles.userEmail}>

              {userProfile.email}

            </ThemedText>

            <ThemedText style={styles.userDetails}>

              {userProfile.fitnessLevel} • Joined {userProfile.joinDate}

            </ThemedText>

          </View>

          

          <TouchableOpacity

            style={[styles.editButton, { borderColor: tintColor }]}

            onPress={handleEditProfile}

          >

            <ThemedText style={[styles.editButtonText, { color: tintColor }]}>

              Edit

            </ThemedText>

          </TouchableOpacity>

        </View>



        {/* Goals Section */}

        <View style={styles.section}>

          <ThemedText type="subtitle" style={styles.sectionTitle}>

            Fitness Goals

          </ThemedText>

          <View style={styles.goalsContainer}>

            {userProfile.goals.map((goal, index) => (

              <View key={index} style={[styles.goalTag, { backgroundColor: `${tintColor}20` }]}>

                <ThemedText style={[styles.goalText, { color: tintColor }]}>

                  {goal}

                </ThemedText>

              </View>

            ))}

          </View>

        </View>



        {/* Stats Section */}

        <View style={styles.section}>

          <ThemedText type="subtitle" style={styles.sectionTitle}>

            Your Progress

          </ThemedText>

          <View style={styles.statsGrid}>

            {renderStatCard('Workouts', userStats.workoutsCompleted, 'Completed')}

            {renderStatCard('Minutes', userStats.totalMinutes, 'Total time')}

            {renderStatCard('Streak', userStats.currentStreak, 'Days')}

            {renderStatCard('Favorites', userStats.favoriteExercises, 'Exercises')}

          </View>

        </View>



        {/* Achievements Section */}

        <View style={styles.section}>

          <ThemedText type="subtitle" style={styles.sectionTitle}>

            Achievements

          </ThemedText>

          <View style={styles.achievementsContainer}>

            {achievements.map(renderAchievement)}

          </View>

        </View>



        {/* Settings Button */}

        <TouchableOpacity

          style={[styles.settingsButton, { backgroundColor: tintColor }]}

          onPress={handleSettingsPress}

        >

          <ThemedText style={styles.settingsButtonText}>

            Settings & Preferences

          </ThemedText>

        </TouchableOpacity>

      </ThemedView>

    </ScrollView>

  );

};



const styles = StyleSheet.create({

  container: {

    flex: 1,

  },

  content: {

    padding: 16,

  },

  profileHeader: {

    flexDirection: 'row',

    alignItems: 'center',

    marginBottom: 32,

    paddingBottom: 16,

    borderBottomWidth: 1,

    borderBottomColor: '#E0E0E0',

  },

  avatarContainer: {

    marginRight: 16,

  },

  avatar: {

    width: 80,

    height: 80,

    borderRadius: 40,

  },

  profileInfo: {

    flex: 1,

  },

  userName: {

    marginBottom: 4,

  },

  userEmail: {

    opacity: 0.7,

    marginBottom: 4,

  },

  userDetails: {

    fontSize: 12,

    opacity: 0.6,

  },

  editButton: {

    paddingHorizontal: 16,

    paddingVertical: 8,

    borderWidth: 1,

    borderRadius: 20,

  },

  editButtonText: {

    fontSize: 14,

    fontWeight: '500',

  },

  section: {

    marginBottom: 32,

  },

  sectionTitle: {

    marginBottom: 16,

    fontWeight: '600',

  },

  goalsContainer: {

    flexDirection: 'row',

    flexWrap: 'wrap',

    gap: 8,

  },

  goalTag: {

    paddingHorizontal: 12,

    paddingVertical: 6,

    borderRadius: 16,

  },

  goalText: {

    fontSize: 14,

    fontWeight: '500',

  },

  statsGrid: {

    flexDirection: 'row',

    flexWrap: 'wrap',

    justifyContent: 'space-between',

    gap: 12,

  },

  statCard: {

    width: '48%',

    padding: 16,

    borderRadius: 12,

    alignItems: 'center',

    elevation: 1,

    shadowColor: '#000',

    shadowOffset: { width: 0, height: 1 },

    shadowOpacity: 0.1,

    shadowRadius: 2,

  },

  statValue: {

    fontSize: 24,

    fontWeight: 'bold',

    marginBottom: 4,

  },

  statTitle: {

    fontSize: 14,

    fontWeight: '500',

    marginBottom: 2,

  },

  statSubtitle: {

    fontSize: 12,

    opacity: 0.6,

  },

  achievementsContainer: {

    gap: 12,

  },

  achievementCard: {

    flexDirection: 'row',

    alignItems: 'center',

    padding: 16,

    borderRadius: 12,

    elevation: 1,

    shadowColor: '#000',

    shadowOffset: { width: 0, height: 1 },

    shadowOpacity: 0.1,

    shadowRadius: 2,

  },

  achievementIcon: {

    width: 40,

    height: 40,

    borderRadius: 20,

    justifyContent: 'center',

    alignItems: 'center',

    marginRight: 12,

  },

  achievementEmoji: {

    fontSize: 20,

  },

  achievementInfo: {

    flex: 1,

  },

  achievementTitle: {

    fontSize: 16,

    fontWeight: '600',

    marginBottom: 4,

  },

  achievementDescription: {

    fontSize: 14,

    opacity: 0.8,

  },

  unlockedBadge: {

    width: 24,

    height: 24,

    borderRadius: 12,

    backgroundColor: '#4CAF50',

    justifyContent: 'center',

    alignItems: 'center',

  },

  unlockedText: {

    color: 'white',

    fontSize: 12,

    fontWeight: 'bold',

  },

  settingsButton: {

    padding: 16,

    borderRadius: 12,

    alignItems: 'center',

    marginTop: 16,

  },

  settingsButtonText: {

    color: 'white',

    fontSize: 16,

    fontWeight: '600',

  },

});



export default Role;

>>>>>>>
