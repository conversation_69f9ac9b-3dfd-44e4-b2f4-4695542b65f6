import React, { useState, useCallback } from 'react';
import { View, StyleSheet, ActivityIndicator, Text, ImageStyle, ViewStyle } from 'react-native';
import FastImage from 'react-native-fast-image';
import { Ionicons } from '@expo/vector-icons';

interface SafeImageProps {
  source: { uri: string } | number;
  fallbackSource?: { uri: string } | number;
  style?: ViewStyle;
  resizeMode?: 'contain' | 'cover' | 'stretch' | 'center';
  onLoad?: () => void;
  onError?: () => void;
  showLoadingIndicator?: boolean;
  loadingColor?: string;
  errorIcon?: string;
  errorText?: string;
}

const SafeImage: React.FC<SafeImageProps> = ({
  source,
  fallbackSource,
  style,
  resizeMode = 'cover',
  onLoad,
  onError,
  showLoadingIndicator = true,
  loadingColor = '#667eea',
  errorIcon = 'image-outline',
  errorText = 'Image not available',
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [currentSource, setCurrentSource] = useState(source);

  const handleImageLoad = useCallback(() => {
    setLoading(false);
    setError(false);
    onLoad?.();
  }, [onLoad]);

  const handleImageError = useCallback(() => {
    setLoading(false);
    if (fallbackSource && currentSource !== fallbackSource) {
      setCurrentSource(fallbackSource);
      setError(false);
    } else {
      setError(true);
      onError?.();
    }
  }, [fallbackSource, currentSource, onError]);

  if (error) {
    return (
      <View style={[styles.errorContainer, style]}>
        <Ionicons name={errorIcon as any} size={40} color="#666" />
        <Text style={styles.errorText}>{errorText}</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {loading && showLoadingIndicator && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={loadingColor} />
        </View>
      )}
      <FastImage
        source={currentSource}
        style={styles.image}
        resizeMode={resizeMode}
        onLoad={handleImageLoad}
        onError={handleImageError}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    backgroundColor: '#f0f0f0',
    borderRadius: 12,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.1)',
    zIndex: 1,
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
    borderRadius: 12,
  },
  errorText: {
    marginTop: 8,
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
});

export default SafeImage;