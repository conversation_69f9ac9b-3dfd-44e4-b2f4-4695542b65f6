// Remote image URLs for the fitness app
// High-quality fitness images from Unsplash - Title-specific images

export const remoteImages = {
  // Morning Workout Images - Sunrise, early morning exercise
  morning1: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?w=400&h=300&fit=crop',
  morning2: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?w=400&h=300&fit=crop',
  sunrise: 'https://images.unsplash.com/photo-1502082553048-f009c37129b9?w=400&h=300&fit=crop',

  // Strength Training Images - Weightlifting, muscle building
  strength1: 'https://images.unsplash.com/photo-1517960413843-0aee8e2d471c?w=400&h=300&fit=crop',
  strength2: 'https://images.unsplash.com/photo-1519864600265-abb23847ef2c?w=400&h=300&fit=crop',
  muscle: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?w=400&h=300&fit=crop',

  // Cardio Images - Running, cycling, HIIT
  cardio1: 'https://images.unsplash.com/photo-1509228468518-180dd4864904?w=400&h=300&fit=crop',
  cardio2: 'https://images.unsplash.com/photo-1461897104016-0b3b00cc81ee?w=400&h=300&fit=crop',
  running: 'https://images.unsplash.com/photo-1517960413843-0aee8e2d471c?w=400&h=300&fit=crop',

  // Flexibility & Yoga Images - Stretching, yoga poses
  yoga1: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=300&fit=crop',
  yoga2: 'https://images.unsplash.com/photo-1504196606672-aef5c9cefc92?w=400&h=300&fit=crop',
  flexibility: 'https://images.unsplash.com/photo-1519864600265-abb23847ef2c?w=400&h=300&fit=crop',

  // Nutrition Images - Healthy food, meal prep
  nutrition1: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400&h=300&fit=crop',
  nutrition2: 'https://images.unsplash.com/photo-1464306076886-debca5e8a6b0?w=400&h=300&fit=crop',
  diet: 'https://images.unsplash.com/photo-1519864600265-abb23847ef2c?w=400&h=300&fit=crop',

  // Exercise and workout images (general)
  exercise1: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?w=400&h=300&fit=crop',
  exercise2: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?w=400&h=300&fit=crop',
  exercise3: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?w=400&h=300&fit=crop',

  // Community and social images
  community1: 'https://images.unsplash.com/photo-1502082553048-f009c37129b9?w=400&h=300&fit=crop',
  community2: 'https://images.unsplash.com/photo-1461897104016-0b3b00cc81ee?w=400&h=300&fit=crop',
  social: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=300&fit=crop',

  // User and admin images
  user: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
  admin: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
  
  // Default fallback images
  default: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
  placeholder: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
};

// Helper function to get image URL by category - Title-specific mapping
export const getImageByCategory = (category) => {
  const categoryMap = {
    // Morning Workout - Sunrise, early morning exercise
    'morning': remoteImages.morning1,
    
    // Strength Training - Weightlifting, muscle building
    'strength': remoteImages.strength1,
    
    // Cardio Blast - Running, cycling, HIIT
    'cardio': remoteImages.cardio1,
    
    // Flexibility & Yoga - Stretching, yoga poses
    'flexibility': remoteImages.flexibility,
    'yoga': remoteImages.yoga1,
    
    // Nutrition Guide - Healthy food, meal prep
    'nutrition': remoteImages.nutrition1,
    'diet': remoteImages.diet,
    
    // General categories
    'exercise': remoteImages.exercise1,
    'workout': remoteImages.exercise2,
    'community': remoteImages.community1,
    'social': remoteImages.social,
    'user': remoteImages.user,
    'admin': remoteImages.admin,
    'wellness': remoteImages.yoga1,
  };
  
  return categoryMap[category.toLowerCase()] || remoteImages.default;
};

// Helper function to get random image from category - Title-specific images
export const getRandomImage = (category) => {
  const categoryImages = {
    // Morning Workout - Multiple morning exercise images
    'morning': [remoteImages.morning1, remoteImages.morning2, remoteImages.sunrise],
    
    // Strength Training - Multiple strength training images
    'strength': [remoteImages.strength1, remoteImages.strength2, remoteImages.muscle],
    
    // Cardio Blast - Multiple cardio exercise images
    'cardio': [remoteImages.cardio1, remoteImages.cardio2, remoteImages.running],
    
    // Flexibility & Yoga - Multiple yoga and flexibility images
    'yoga': [remoteImages.yoga1, remoteImages.yoga2, remoteImages.flexibility],
    'flexibility': [remoteImages.flexibility, remoteImages.yoga1, remoteImages.yoga2],
    
    // Nutrition Guide - Multiple nutrition images
    'nutrition': [remoteImages.nutrition1, remoteImages.nutrition2, remoteImages.diet],
    
    // General exercise images
    'exercise': [remoteImages.exercise1, remoteImages.exercise2, remoteImages.exercise3],
  };
  
  const images = categoryImages[category.toLowerCase()] || [remoteImages.default];
  return images[Math.floor(Math.random() * images.length)];
};

export default remoteImages; 