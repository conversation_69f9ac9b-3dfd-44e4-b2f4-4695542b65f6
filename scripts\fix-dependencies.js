#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing React Native dependencies and cache issues...');

try {
  // Clear npm cache
  console.log('📦 Clearing npm cache...');
  execSync('npm cache clean --force', { stdio: 'inherit' });

  // Remove node_modules and package-lock.json
  console.log('🗑️ Removing node_modules and package-lock.json...');
  if (fs.existsSync('node_modules')) {
    execSync('rm -rf node_modules', { stdio: 'inherit' });
  }
  if (fs.existsSync('package-lock.json')) {
    fs.unlinkSync('package-lock.json');
  }

  // Reinstall dependencies
  console.log('📥 Reinstalling dependencies...');
  execSync('npm install', { stdio: 'inherit' });

  // Clear Metro cache
  console.log('🧹 Clearing Metro cache...');
  execSync('npx expo start --clear', { stdio: 'inherit' });

  console.log('✅ Dependencies fixed successfully!');
  console.log('🚀 You can now run: npm start');
} catch (error) {
  console.error('❌ Error fixing dependencies:', error.message);
  process.exit(1);
} 