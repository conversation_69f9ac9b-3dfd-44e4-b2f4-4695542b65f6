import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  ScrollView,
  Image,
  Dimensions,
  Animated
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';

const { width, height } = Dimensions.get('window');

// Advanced Particle component
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animSpeed = 4000 * speed + Math.random() * 2000;
    const animDelay = Math.random() * 3000 + delay;
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, { toValue: 1, duration: animSpeed, useNativeDriver: true }),
        Animated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = Animated.sequence([
      Animated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 1, duration: animSpeed - 1000, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);
    
    const timeoutId = setTimeout(() => {
      animation.start();
      Animated.loop(opacitySequence).start();
    }, animDelay);

    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, [delay, speed]);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <Animated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

const FloatingShape = ({ style, delay = 0, rotationSpeed = 1 }) => {
  const floatAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const floatAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
        Animated.timing(floatAnim, {
          toValue: 0,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
      ])
    );
    
    const rotateAnimation = Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 15000 * rotationSpeed,
        useNativeDriver: true,
      })
    );
    
    const timeoutId = setTimeout(() => {
      floatAnimation.start();
      rotateAnimation.start();
    }, delay);

    return () => {
      clearTimeout(timeoutId);
      floatAnimation.stop();
      rotateAnimation.stop();
    };
  }, [delay, rotationSpeed]);

  const translateY = floatAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });

  const rotate = rotationAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{ translateY }, { rotate }],
        },
      ]}
    />
  );
};

export default function ManagePlans() {
  const router = useRouter();
  const [plans, setPlans] = useState([]);
  const [filteredPlans, setFilteredPlans] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  useEffect(() => {
    fetchPlans();
  }, []);

  useEffect(() => {
    filterPlans();
  }, [searchQuery, plans]);

  const fetchPlans = async () => {
    try {
      const plansData = await AsyncStorage.getItem('@diet_plans');
      if (plansData) {
        const parsedPlans = JSON.parse(plansData);
        setPlans(parsedPlans);
        setFilteredPlans(parsedPlans);
      } else {
        // Create default plans if none exist
        const defaultPlans = [
          {
            id: '1',
            title: 'Intermittent Fasting',
            description: 'A popular eating pattern that cycles between periods of fasting and eating.',
            days: 30,
            imageKey: 'fasting.png',
            category: 'Diet',
            difficulty: 'Intermediate',
            calories: 1800,
            active: true
          },
          {
            id: '2',
            title: 'Keto Diet',
            description: 'A low-carb, high-fat diet that can help you burn fat more effectively.',
            days: 21,
            imageKey: 'keto.png',
            category: 'Diet',
            difficulty: 'Advanced',
            calories: 1600,
            active: true
          },
          {
            id: '3',
            title: 'Low Carb Diet',
            description: 'Reduce carbohydrate intake to promote weight loss and improve health.',
            days: 14,
            imageKey: 'lowcarb.png',
            category: 'Diet',
            difficulty: 'Beginner',
            calories: 1700,
            active: true
          },
          {
            id: '4',
            title: 'Vegan Diet',
            description: 'Plant-based nutrition for optimal health and environmental sustainability.',
            days: 28,
            imageKey: 'vegan.png',
            category: 'Diet',
            difficulty: 'Intermediate',
            calories: 1900,
            active: true
          }
        ];
        await AsyncStorage.setItem('@diet_plans', JSON.stringify(defaultPlans));
        setPlans(defaultPlans);
        setFilteredPlans(defaultPlans);
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
      Alert.alert('Error', 'Failed to load plans');
    } finally {
      setLoading(false);
    }
  };

  const filterPlans = () => {
    if (!searchQuery.trim()) {
      setFilteredPlans(plans);
      return;
    }

    const filtered = plans.filter(plan =>
      plan.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plan.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plan.category?.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredPlans(filtered);
  };

  const handlePlanAction = (plan, action) => {
    setSelectedPlan(plan);
    switch (action) {
      case 'view':
        setModalVisible(true);
        break;
      case 'edit':
        setEditModalVisible(true);
        break;
      case 'toggle':
        handleTogglePlan(plan);
        break;
      case 'delete':
        handleDeletePlan(plan);
        break;
    }
  };

  const handleTogglePlan = async (plan) => {
    try {
      const updatedPlans = plans.map(p =>
        p.id === plan.id ? { ...p, active: !p.active } : p
      );
      await AsyncStorage.setItem('@diet_plans', JSON.stringify(updatedPlans));
      setPlans(updatedPlans);
      Alert.alert('Success', `Plan ${plan.active ? 'deactivated' : 'activated'} successfully`);
    } catch (error) {
      Alert.alert('Error', 'Failed to update plan status');
    }
  };

  const handleDeletePlan = (plan) => {
    Alert.alert(
      'Delete Plan',
      `Are you sure you want to delete "${plan.title}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const updatedPlans = plans.filter(p => p.id !== plan.id);
              await AsyncStorage.setItem('@diet_plans', JSON.stringify(updatedPlans));
              setPlans(updatedPlans);
              Alert.alert('Success', 'Plan deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete plan');
            }
          }
        }
      ]
    );
  };

  const handleSavePlan = async (updatedPlan) => {
    try {
      const updatedPlans = plans.map(p =>
        p.id === updatedPlan.id ? updatedPlan : p
      );
      await AsyncStorage.setItem('@diet_plans', JSON.stringify(updatedPlans));
      setPlans(updatedPlans);
      setEditModalVisible(false);
      Alert.alert('Success', 'Plan updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to update plan');
    }
  };

  const getImageSource = (filename) => {
    const images = {
      'fasting.png': 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=400&h=300&fit=crop&crop=center&auto=format&q=80',
      'keto.png': 'https://images.unsplash.com/photo-1498837167922-ddd27525d352?w=400&h=300&fit=crop&crop=center&auto=format&q=80',
      'lowcarb.png': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&crop=center&auto=format&q=80',
      'vegan.png': 'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=400&h=300&fit=crop&crop=center&auto=format&q=80',
    };
    return { uri: images[filename] || images['vegan.png'] };
  };

  const renderPlanItem = ({ item }) => (
    <View style={[styles.planCard, { borderRadius: 18, backgroundColor: 'rgba(255,255,255,0.10)', shadowColor: '#764ba2', shadowOpacity: 0.15, shadowRadius: 12, elevation: 6 }]}>
      <View style={styles.planHeader}>
        <Image source={getImageSource(item.imageKey)} style={styles.planImage} />
        <View style={styles.planInfo}>
          <Text style={styles.planTitle}>{item.title}</Text>
          <Text style={styles.planCategory}>{item.category} • {item.difficulty}</Text>
          <Text style={styles.planDuration}>{item.days} days • {item.calories} cal/day</Text>
        </View>
        <View style={styles.planStatus}>
          {item.active ? (
            <View style={styles.activeBadge}>
              <Text style={styles.activeText}>ACTIVE</Text>
            </View>
          ) : (
            <View style={styles.inactiveBadge}>
              <Text style={styles.inactiveText}>INACTIVE</Text>
            </View>
          )}
        </View>
      </View>

      <Text style={styles.planDescription} numberOfLines={2}>
        {item.description}
      </Text>

      <View style={[styles.actionButtons, { borderRadius: 25, backgroundColor: 'transparent', overflow: 'hidden' }]}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#4ECDC4' }]}
          onPress={() => handlePlanAction(item, 'view')}
        >
          <Ionicons name="eye" size={16} color="#fff" />
          <Text style={styles.actionButtonText}>View</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#45B7D1' }]}
          onPress={() => handlePlanAction(item, 'edit')}
        >
          <Ionicons name="create" size={16} color="#fff" />
          <Text style={styles.actionButtonText}>Edit</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: item.active ? '#FECA57' : '#4ECDC4' }]}
          onPress={() => handlePlanAction(item, 'toggle')}
        >
          <Ionicons name={item.active ? "pause" : "play"} size={16} color="#fff" />
          <Text style={styles.actionButtonText}>{item.active ? 'Pause' : 'Activate'}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#FF6B6B' }]}
          onPress={() => handlePlanAction(item, 'delete')}
        >
          <Ionicons name="trash" size={16} color="#fff" />
          <Text style={styles.actionButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (!fontsLoaded) return null;

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2', '#f093fb']}
      style={{ flex: 1 }}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      {/* Example floating shape/particle */}
      <Animated.View style={{ position: 'absolute', top: 40, left: 30, width: 60, height: 60, borderRadius: 30, backgroundColor: 'rgba(255,255,255,0.08)' }} />
      {/* Main content below, unchanged logic */}
      <View style={{ flex: 1 }}>
        <View style={styles.outerContainer}>
          {/* Animated Particles and Floating Shapes */}
          <AdvancedParticle style={styles.particle1} delay={0} speed={1.2} />
          <AdvancedParticle style={styles.particle2} delay={500} speed={0.8} />
          <AdvancedParticle style={styles.particle3} delay={1000} speed={1.5} />
          <FloatingShape style={styles.floatingShape1} delay={0} rotationSpeed={0.5} />
          <FloatingShape style={styles.floatingShape2} delay={1000} rotationSpeed={1.2} />
          <View style={[styles.glowCircle, { top: 60, right: 30 }]} />
          <View style={[styles.glowCircle, { bottom: 80, left: 20 }]} />
          {/* Main Content */}
          <LinearGradient
            colors={['#1E90FF', '#00C9A7', '#21D4FD']}
            style={styles.container}
          >
            {/* Header */}
            <View style={styles.header}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
              >
                <Ionicons name="arrow-back" size={24} color="#fff" />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Manage Plans</Text>
              <TouchableOpacity
                style={styles.addButton}
                onPress={() => router.push('/admin/add-plan')}
              >
                <Ionicons name="add" size={24} color="#fff" />
              </TouchableOpacity>
            </View>

            {/* Search */}
            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color="#7f8c8d" style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search plans by title, description, or category..."
                placeholderTextColor="#7f8c8d"
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </View>

            {/* Plans List */}
            <FlatList
              data={filteredPlans}
              renderItem={renderPlanItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.listContainer}
              showsVerticalScrollIndicator={false}
            />

            {/* Plan Detail Modal */}
            <Modal
              animationType="slide"
              transparent={true}
              visible={modalVisible}
              onRequestClose={() => setModalVisible(false)}
            >
              <View style={styles.modalOverlay}>
                <View style={styles.modalContent}>
                  <View style={styles.modalHeader}>
                    <Text style={styles.modalTitle}>Plan Details</Text>
                    <TouchableOpacity onPress={() => setModalVisible(false)}>
                      <Ionicons name="close" size={24} color="#2c3e50" />
                    </TouchableOpacity>
                  </View>

                  {selectedPlan && (
                    <ScrollView style={styles.modalBody}>
                      <Image source={getImageSource(selectedPlan.imageKey)} style={styles.modalImage} />
                      
                      <Text style={styles.modalLabel}>Title:</Text>
                      <Text style={styles.modalValue}>{selectedPlan.title}</Text>

                      <Text style={styles.modalLabel}>Description:</Text>
                      <Text style={styles.modalValue}>{selectedPlan.description}</Text>

                      <Text style={styles.modalLabel}>Category:</Text>
                      <Text style={styles.modalValue}>{selectedPlan.category}</Text>

                      <Text style={styles.modalLabel}>Difficulty:</Text>
                      <Text style={styles.modalValue}>{selectedPlan.difficulty}</Text>

                      <Text style={styles.modalLabel}>Duration:</Text>
                      <Text style={styles.modalValue}>{selectedPlan.days} days</Text>

                      <Text style={styles.modalLabel}>Daily Calories:</Text>
                      <Text style={styles.modalValue}>{selectedPlan.calories} calories</Text>

                      <Text style={styles.modalLabel}>Status:</Text>
                      <Text style={styles.modalValue}>{selectedPlan.active ? 'Active' : 'Inactive'}</Text>
                    </ScrollView>
                  )}
                </View>
              </View>
            </Modal>

            {/* Edit Plan Modal */}
            <Modal
              animationType="slide"
              transparent={true}
              visible={editModalVisible}
              onRequestClose={() => setEditModalVisible(false)}
            >
              <EditPlanModal
                plan={selectedPlan}
                onSave={handleSavePlan}
                onClose={() => setEditModalVisible(false)}
              />
            </Modal>
          </LinearGradient>
        </View>
      </View>
    </LinearGradient>
  );
}

// Edit Plan Modal Component
const EditPlanModal = ({ plan, onSave, onClose }) => {
  const [editedPlan, setEditedPlan] = useState(plan || {});

  const handleSave = () => {
    if (!editedPlan.title || !editedPlan.description) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }
    onSave(editedPlan);
  };

  return (
    <View style={styles.modalOverlay}>
      <View style={styles.modalContent}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Edit Plan</Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#2c3e50" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalBody}>
          <Text style={styles.inputLabel}>Title *</Text>
          <TextInput
            style={styles.textInput}
            value={editedPlan.title}
            onChangeText={(text) => setEditedPlan({...editedPlan, title: text})}
            placeholder="Enter plan title"
          />

          <Text style={styles.inputLabel}>Description *</Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            value={editedPlan.description}
            onChangeText={(text) => setEditedPlan({...editedPlan, description: text})}
            placeholder="Enter plan description"
            multiline
            numberOfLines={4}
          />

          <Text style={styles.inputLabel}>Duration (days)</Text>
          <TextInput
            style={styles.textInput}
            value={String(editedPlan.days || '')}
            onChangeText={(text) => setEditedPlan({...editedPlan, days: parseInt(text) || 0})}
            placeholder="Enter duration in days"
            keyboardType="numeric"
          />

          <Text style={styles.inputLabel}>Daily Calories</Text>
          <TextInput
            style={styles.textInput}
            value={String(editedPlan.calories || '')}
            onChangeText={(text) => setEditedPlan({...editedPlan, calories: parseInt(text) || 0})}
            placeholder="Enter daily calories"
            keyboardType="numeric"
          />

          <View style={styles.modalActions}>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
              <Text style={styles.saveButtonText}>Save Changes</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  particle1: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderRadius: 12,
    left: width * 0.1,
    top: 120,
    zIndex: 0,
  },
  particle2: {
    position: 'absolute',
    width: 18,
    height: 18,
    borderRadius: 9,
    left: width * 0.8,
    top: 200,
    zIndex: 0,
  },
  particle3: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    left: width * 0.2,
    top: 500,
    zIndex: 0,
  },
  particleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  floatingShape1: {
    position: 'absolute',
    width: 32,
    height: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    left: width * 0.85,
    top: 100,
    zIndex: 0,
  },
  floatingShape2: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    left: width * 0.05,
    top: 600,
    zIndex: 0,
  },
  glowCircle: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 20,
    elevation: 10,
    zIndex: 0,
  },
  container: {
    flex: 1,
    paddingTop: 50,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  addButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 12,
    paddingHorizontal: 15,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  planCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  planHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  planImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  planInfo: {
    flex: 1,
  },
  planTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  planCategory: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: '#7f8c8d',
    marginBottom: 2,
  },
  planDuration: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
  },
  planStatus: {
    alignItems: 'flex-end',
  },
  activeBadge: {
    backgroundColor: '#4ECDC4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  activeText: {
    fontSize: 10,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  inactiveBadge: {
    backgroundColor: '#95a5a6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  inactiveText: {
    fontSize: 10,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  planDescription: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    marginBottom: 16,
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 2,
    justifyContent: 'center',
  },
  actionButtonText: {
    fontSize: 10,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginLeft: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
  },
  modalBody: {
    maxHeight: 400,
  },
  modalImage: {
    width: '100%',
    height: 150,
    borderRadius: 12,
    marginBottom: 16,
  },
  modalLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginTop: 12,
    marginBottom: 4,
  },
  modalValue: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    marginBottom: 8,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginTop: 12,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ecf0f1',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
    marginBottom: 12,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    backgroundColor: '#95a5a6',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    marginRight: 10,
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: '#4ECDC4',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    marginLeft: 10,
  },
  saveButtonText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    textAlign: 'center',
  },
});