import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Accelerometer } from 'expo-sensors';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import * as Progress from 'react-native-progress';
import Animated, { FadeInUp } from 'react-native-reanimated';


const StepCounter = ({ userEmail, onStepUpdate, userName = 'User' }) => {
  const [steps, setSteps] = useState(0);
  const [dailyGoal, setDailyGoal] = useState(10000);
  const [isActive, setIsActive] = useState(false);
  const [calories, setCalories] = useState(0);
  const [distance, setDistance] = useState(0);
  const [loading, setLoading] = useState(true);
  const [showMetrics, setShowMetrics] = useState({
    steps: true,
    calories: true,
    distance: true,
    goal: true,
  });
  const [darkMode, setDarkMode] = useState(false);
  const motivationalQuotes = [
    "Every step is progress!",
    "Push your limits today!",
    "Consistency beats intensity!",
    "You are your only limit!",
    "Stay strong, stay healthy!",
    "Great things never come from comfort zones!",
    "Sweat now, shine later!",
    "Believe in yourself!"
  ];
  const todayQuote = motivationalQuotes[new Date().getDate() % motivationalQuotes.length];

  // Step detection variables
  const accelerometerData = useRef([]);
  const lastStepTime = useRef(0);
  const stepThreshold = useRef(1.2);
  const subscription = useRef(null);

  // Filtering variables for step detection
  const filterWindow = useRef([]);
  const windowSize = 10;
  const minStepInterval = 300; // Minimum time between steps (ms)

  useEffect(() => {
    initializeStepCounter();
    return () => {
      stopStepCounting();
    };
  }, []);

  useEffect(() => {
    if (steps > 0) {
      calculateMetrics();
      saveStepData();
      
      if (onStepUpdate) {
        onStepUpdate({
          steps,
          calories,
          distance,
          progress: (steps / dailyGoal) * 100,
        });
      }
    }
  }, [steps]);

  const initializeStepCounter = async () => {
    try {
      setLoading(true);
      
      // Load today's step data
      await loadTodaySteps();
      
      // Load user preferences
      await loadUserPreferences();
      
      // Start step counting
      startStepCounting();
      
    } catch (error) {
      console.error('Error initializing step counter:', error);
      Alert.alert('Error', 'Failed to initialize step counter');
    } finally {
      setLoading(false);
    }
  };

  const loadTodaySteps = async () => {
    try {
      const today = new Date().toDateString();
      const stepKey = `@steps:${userEmail}:${today}`;
      const savedSteps = await AsyncStorage.getItem(stepKey);
      
      if (savedSteps) {
        const stepData = JSON.parse(savedSteps);
        setSteps(stepData.steps || 0);
        setCalories(stepData.calories || 0);
        setDistance(stepData.distance || 0);
      }
    } catch (error) {
      console.error('Error loading today steps:', error);
    }
  };

  const loadUserPreferences = async () => {
    try {
      const prefsKey = `@step_preferences:${userEmail}`;
      const savedPrefs = await AsyncStorage.getItem(prefsKey);
      
      if (savedPrefs) {
        const prefs = JSON.parse(savedPrefs);
        setDailyGoal(prefs.dailyGoal || 10000);
        stepThreshold.current = prefs.sensitivity || 1.2;
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  const startStepCounting = async () => {
    try {
      // Check if accelerometer is available
      const isAvailable = await Accelerometer.isAvailableAsync();
      if (!isAvailable) {
        Alert.alert('Sensor Not Available', 'Accelerometer is not available on this device');
        return;
      }

      // Set update interval (100ms for responsive step detection)
      Accelerometer.setUpdateInterval(100);

      // Start listening to accelerometer data
      subscription.current = Accelerometer.addListener(handleAccelerometerData);
      setIsActive(true);

      console.log('Step counting started');
    } catch (error) {
      console.error('Error starting step counting:', error);
      Alert.alert('Error', 'Failed to start step counting');
    }
  };

  const stopStepCounting = () => {
    if (subscription.current) {
      subscription.current.remove();
      subscription.current = null;
    }
    setIsActive(false);
    console.log('Step counting stopped');
  };

  const handleAccelerometerData = (data) => {
    const { x, y, z } = data;
    const currentTime = Date.now();
    
    // Calculate magnitude of acceleration
    const magnitude = Math.sqrt(x * x + y * y + z * z);
    
    // Add to filter window
    filterWindow.current.push(magnitude);
    if (filterWindow.current.length > windowSize) {
      filterWindow.current.shift();
    }

    // Apply simple moving average filter
    if (filterWindow.current.length === windowSize) {
      const average = filterWindow.current.reduce((sum, val) => sum + val, 0) / windowSize;
      
      // Detect step using peak detection
      if (detectStep(average, currentTime)) {
        setSteps(prevSteps => prevSteps + 1);
        lastStepTime.current = currentTime;
      }
    }
  };

  const detectStep = (magnitude, currentTime) => {
    // Check if enough time has passed since last step
    if (currentTime - lastStepTime.current < minStepInterval) {
      return false;
    }

    // Simple threshold-based step detection
    // In a real implementation, you'd use more sophisticated algorithms
    // like peak detection, frequency analysis, or machine learning
    if (magnitude > stepThreshold.current) {
      // Additional validation: check if this is a genuine step
      // by analyzing the pattern in recent data
      return validateStep(magnitude);
    }

    return false;
  };

  const validateStep = (magnitude) => {
    // Simple validation: ensure the magnitude is within reasonable bounds
    // and not too similar to recent readings (to avoid double counting)
    const recentData = filterWindow.current.slice(-5);
    const avgRecent = recentData.reduce((sum, val) => sum + val, 0) / recentData.length;
    
    // Step should be significantly higher than recent average
    return magnitude > avgRecent * 1.1 && magnitude < 3.0; // Upper bound to filter out non-walking activities
  };

  const calculateMetrics = () => {
    // Calculate calories burned (rough estimation)
    // Formula: steps * 0.04 (calories per step for average person)
    const estimatedCalories = steps * 0.04;
    setCalories(estimatedCalories);

    // Calculate distance (rough estimation)
    // Formula: steps * average step length (0.762 meters for average person)
    const estimatedDistance = (steps * 0.762) / 1000; // Convert to kilometers
    setDistance(estimatedDistance);
  };

  const saveStepData = async () => {
    try {
      const today = new Date().toDateString();
      const stepKey = `@steps:${userEmail}:${today}`;
      const trackerKey = `tracker_${userEmail}_${today}`;
      const stepData = {
        steps,
        calories,
        distance,
      };
      await AsyncStorage.setItem(stepKey, JSON.stringify(stepData));
      // Also update analytics tracker key
      const trackerSummary = {
        steps,
        calories,
        distance,
      };
      await AsyncStorage.mergeItem(trackerKey, JSON.stringify(trackerSummary));
    } catch (error) {
      console.error('Error saving step data:', error);
    }
  };

  const getMotivationalMessage = () => {
    const progress = (steps / dailyGoal) * 100;
    
    if (progress >= 100) {
      return "🎉 Goal achieved! You're crushing it today!";
    } else if (progress >= 75) {
      return "🔥 Almost there! Just a few more steps to your goal!";
    } else if (progress >= 50) {
      return "💪 Halfway there! Keep up the great work!";
    } else if (progress >= 25) {
      return "🚶 Good start! Every step counts towards your goal!";
    } else {
      return "🌟 Ready to start your step journey today?";
    }
  };

  const getProgressColor = () => {
    const progress = (steps / dailyGoal) * 100;
    
    if (progress >= 100) return '#4CAF50'; // Green
    if (progress >= 75) return '#FF9800'; // Orange
    if (progress >= 50) return '#2196F3'; // Blue
    if (progress >= 25) return '#9C27B0'; // Purple
    return '#757575'; // Gray
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#667eea" />
        <Text style={styles.loadingText}>Initializing step counter...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient colors={['#667eea', '#764ba2']} style={styles.stepCard}>
        <View style={styles.header}>
          <Text style={styles.title}>👟 Step Counter</Text>
          <View style={[styles.statusIndicator, isActive && styles.statusActive]}>
            <Text style={styles.statusText}>
              {isActive ? '🟢 Active' : '⚪ Inactive'}
            </Text>
          </View>
        </View>

        {/* Main Step Display */}
        <View style={styles.stepDisplay}>
          <Text style={styles.stepCount}>{steps.toLocaleString()}</Text>
          <Text style={styles.stepLabel}>steps today</Text>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <Progress.Bar
            progress={Math.min(steps / dailyGoal, 1)}
            width={null}
            height={8}
            color={getProgressColor()}
            unfilledColor="rgba(255,255,255,0.3)"
            borderWidth={0}
            borderRadius={4}
          />
          <Text style={styles.progressText}>
            {Math.round((steps / dailyGoal) * 100)}% of daily goal
          </Text>
        </View>

        {/* Motivational Message */}
        <View style={styles.motivationContainer}>
          <Text style={styles.motivationText}>
            {getMotivationalMessage()}
          </Text>
        </View>

        {/* Step Detection Info */}
        <View style={styles.infoContainer}>
          <Text style={styles.infoText}>
            📱 Using device sensors for automatic step detection
          </Text>
          <Text style={styles.infoSubtext}>
            Keep your phone with you for accurate tracking
          </Text>
        </View>

        <View style={{ position: 'relative', width: '100%' }}>
          {/* Animated Background Elements */}
          <View style={{ position: 'absolute', top: -60, left: -60, width: 120, height: 120, borderRadius: 60, backgroundColor: darkMode ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)' }} />
          <View style={{ position: 'absolute', bottom: -40, right: -40, width: 80, height: 80, borderRadius: 40, backgroundColor: darkMode ? 'rgba(255,255,255,0.07)' : 'rgba(0,0,0,0.07)' }} />
          <View style={{ position: 'absolute', top: '30%', right: -20, width: 40, height: 40, borderRadius: 20, backgroundColor: darkMode ? 'rgba(255,255,255,0.04)' : 'rgba(0,0,0,0.04)' }} />
          {/* Greeting & Quote */}
          <Text style={{ fontSize: 20, fontWeight: '700', color: darkMode ? '#fff' : '#222', marginTop: 12, marginBottom: 2, textAlign: 'center' }}>
            {`Hi, ${userName}!`}
          </Text>
          <Text style={{ fontSize: 14, color: darkMode ? '#fff' : '#444', marginBottom: 8, textAlign: 'center', fontStyle: 'italic' }}>
            {todayQuote}
          </Text>
          {/* Dark/Light Mode Toggle */}
          <TouchableOpacity onPress={() => setDarkMode((d) => !d)} style={{ alignSelf: 'center', marginBottom: 8 }}>
            <Ionicons name={darkMode ? 'moon' : 'sunny'} size={22} color={darkMode ? '#fff' : '#222'} />
          </TouchableOpacity>
          {/* Dashboard Customization: Show/Hide Metrics */}
          <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 8 }}>
            {Object.keys(showMetrics).map((metric) => (
              <TouchableOpacity
                key={metric}
                onPress={() => setShowMetrics((prev) => ({ ...prev, [metric]: !prev[metric] }))}
                style={{ marginHorizontal: 6, padding: 4, borderRadius: 6, backgroundColor: showMetrics[metric] ? (darkMode ? '#333' : '#eee') : 'transparent' }}
              >
                <Text style={{ color: showMetrics[metric] ? (darkMode ? '#fff' : '#222') : (darkMode ? '#aaa' : '#888'), fontSize: 12 }}>
                  {metric.charAt(0).toUpperCase() + metric.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Metrics Row */}
        <View style={styles.metricsRow}>
          {showMetrics.steps && (
            <Animated.View entering={FadeInUp} style={styles.metric}>
              <Ionicons name="walk" size={20} color="#fff" />
              <Text style={styles.metricValue}>{steps.toLocaleString()}</Text>
              <Text style={styles.metricLabel}>steps</Text>
            </Animated.View>
          )}
          {showMetrics.calories && (
            <Animated.View entering={FadeInUp} style={styles.metric}>
              <Ionicons name="flame" size={20} color="#fff" />
              <Text style={styles.metricValue}>{Math.round(calories)}</Text>
              <Text style={styles.metricLabel}>calories</Text>
            </Animated.View>
          )}
          {showMetrics.distance && (
            <Animated.View entering={FadeInUp} style={styles.metric}>
              <Ionicons name="map" size={20} color="#fff" />
              <Text style={styles.metricValue}>{distance.toFixed(2)}</Text>
              <Text style={styles.metricLabel}>km</Text>
            </Animated.View>
          )}
          {showMetrics.goal && (
            <Animated.View entering={FadeInUp} style={styles.metric}>
              <Ionicons name="flag" size={20} color="#fff" />
              <Text style={styles.metricValue}>{dailyGoal.toLocaleString()}</Text>
              <Text style={styles.metricLabel}>goal</Text>
            </Animated.View>
          )}
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
    color: '#666',
  },
  stepCard: {
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#fff',
  },
  statusIndicator: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusActive: {
    backgroundColor: 'rgba(76, 175, 80, 0.3)',
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  stepDisplay: {
    alignItems: 'center',
    marginBottom: 20,
  },
  stepCount: {
    fontSize: 48,
    fontWeight: '700',
    color: '#fff',
  },
  stepLabel: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 4,
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressText: {
    fontSize: 14,
    color: '#fff',
    textAlign: 'center',
    marginTop: 8,
    fontWeight: '600',
  },
  motivationContainer: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  motivationText: {
    fontSize: 14,
    color: '#fff',
    textAlign: 'center',
    fontWeight: '600',
  },
  infoContainer: {
    alignItems: 'center',
  },
  infoText: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    textAlign: 'center',
  },
  infoSubtext: {
    fontSize: 10,
    color: 'rgba(255,255,255,0.6)',
    textAlign: 'center',
    marginTop: 2,
  },
  metricsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  metric: {
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#fff',
    marginTop: 4,
  },
  metricLabel: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 2,
  },
});

export default StepCounter;