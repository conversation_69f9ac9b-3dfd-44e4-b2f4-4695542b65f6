import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  Animated,
  Platform,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import SafeImage from '../components/SafeImage';
import { getImageByCategory } from '../constants/remoteImages';

const { width, height } = Dimensions.get('window');

// Advanced Particle component for background effects
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, {
          toValue: 1,
          duration: (4000 + Math.random() * 3000) * speed,
          useNativeDriver: true,
        }),
        Animated.timing(particleAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    );
    
    const timeoutId = setTimeout(() => {
      animation.start();
    }, delay);
    
    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, []);

  const top = particleAnim.interpolate({ 
    inputRange: [0, 1], 
    outputRange: [height, -20] 
  });
  
  return (
    <Animated.View 
      style={[
        style, 
        { 
          transform: [{ translateY: top }], 
          opacity: opacityAnim 
        }
      ]} 
    />
  );
};

// Video Player Component with sophisticated design
const VideoPlayer = ({ videoUrl, title, duration, style }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  const handlePlayPress = () => {
    setIsLoading(true);
    // Simulate video loading
    setTimeout(() => {
      setIsLoading(false);
      setIsPlaying(true);
    }, 1000);
  };

  const handlePressIn = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.8,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  return (
    <Animated.View 
      style={[
        styles.videoCard,
        style,
        {
          transform: [{ scale: scaleAnim }],
          opacity: opacityAnim,
        }
      ]}
    >
      <TouchableOpacity
        style={styles.videoTouchable}
        onPress={handlePlayPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        <LinearGradient
          colors={['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']}
          style={styles.videoGradient}
        >
          <SafeImage 
            source={{ uri: getImageByCategory('nutrition') }}
            style={styles.videoThumbnail}
            resizeMode="cover"
          />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.8)']}
            style={styles.videoOverlay}
          >
            <View style={styles.videoInfo}>
              <View style={styles.videoHeader}>
                <View style={styles.videoIconContainer}>
                  <Ionicons name="play-circle" size={24} color="#fff" />
                </View>
                <View style={styles.videoMeta}>
                  <Text style={styles.videoTitle}>{title}</Text>
                  <Text style={styles.videoDuration}>{duration}</Text>
                </View>
              </View>
              
              <View style={styles.videoControls}>
                <TouchableOpacity 
                  style={styles.playButton}
                  onPress={handlePlayPress}
                >
                  <LinearGradient
                    colors={['#667eea', '#764ba2']}
                    style={styles.playButtonGradient}
                  >
                    <Ionicons 
                      name={isPlaying ? "pause" : "play"} 
                      size={20} 
                      color="#fff" 
                    />
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
          
          {isLoading && (
            <View style={styles.loadingOverlay}>
              <LinearGradient
                colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)']}
                style={styles.loadingGradient}
              >
                <Ionicons name="refresh" size={24} color="#fff" />
                <Text style={styles.loadingText}>Loading...</Text>
              </LinearGradient>
            </View>
          )}
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Image Slider with improved design
const ImageSlider = ({ images }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef(null);

  const handleScroll = (event) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / width);
    setCurrentIndex(index);
  };

  return (
    <View style={styles.sliderContainer}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        {images.map((image, index) => (
          <View key={index} style={styles.slide}>
            <SafeImage 
              source={image.uri} 
              style={styles.slideImage} 
              resizeMode="cover"
            />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.8)']}
              style={styles.slideOverlay}
            >
              <Text style={styles.slideTitle}>{image.title}</Text>
              <Text style={styles.slideSubtitle}>{image.subtitle}</Text>
            </LinearGradient>
          </View>
        ))}
      </ScrollView>
      <View style={styles.pagination}>
        {images.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              { backgroundColor: index === currentIndex ? '#fff' : 'rgba(255,255,255,0.5)' }
            ]}
          />
        ))}
      </View>
    </View>
  );
};

export default function NutritionGuide() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  const nutritionImages = [
    { 
      uri: getImageByCategory('nutrition'), 
      title: 'Balanced Nutrition',
      subtitle: 'Fuel your body with the right nutrients'
    },
    { 
      uri: getImageByCategory('nutrition'), 
      title: 'Healthy Eating',
      subtitle: 'Make smart food choices every day'
    },
    { 
      uri: getImageByCategory('nutrition'), 
      title: 'Natural Foods',
      subtitle: 'Choose whole, unprocessed foods'
    },
    { 
      uri: getImageByCategory('nutrition'), 
      title: 'Protein Rich',
      subtitle: 'Build and repair muscles effectively'
    },
  ];

  const nutritionVideos = [
    {
      title: 'Nutrition Basics',
      duration: '5:30',
      thumbnail: getImageByCategory('nutrition'),
      videoUrl: 'https://example.com/nutrition-basics.mp4'
    },
    {
      title: 'Meal Planning',
      duration: '8:15',
      thumbnail: getImageByCategory('nutrition'),
      videoUrl: 'https://example.com/meal-planning.mp4'
    },
    {
      title: 'Healthy Recipes',
      duration: '12:45',
      thumbnail: getImageByCategory('nutrition'),
      videoUrl: 'https://example.com/healthy-recipes.mp4'
    }
  ];

  const nutritionTips = [
    {
      title: 'Stay Hydrated',
      description: 'Drink at least 8 glasses of water daily for optimal health.',
      icon: 'water',
      color: '#4ECDC4'
    },
    {
      title: 'Eat Protein',
      description: 'Include lean protein in every meal for muscle recovery.',
      icon: 'nutrition',
      color: '#FF6B6B'
    },
    {
      title: 'Fruits & Vegetables',
      description: 'Aim for 5 servings of colorful fruits and vegetables daily.',
      icon: 'leaf',
      color: '#96CEB4'
    },
    {
      title: 'Plan Your Meals',
      description: 'Prepare healthy meals in advance to avoid unhealthy choices.',
      icon: 'calendar',
      color: '#FFD166'
    }
  ];

  const mealPlans = [
    {
      title: 'Weight Loss Plan',
      calories: '1500-1800',
      focus: 'Calorie Deficit',
      meals: ['High Protein Breakfast', 'Lean Lunch', 'Light Dinner'],
      image: { uri: getImageByCategory('nutrition') }
    },
    {
      title: 'Muscle Building',
      calories: '2200-2500',
      focus: 'Protein Rich',
      meals: ['Protein Breakfast', 'Complex Carbs Lunch', 'Protein Dinner'],
      image: { uri: getImageByCategory('nutrition') }
    },
    {
      title: 'Maintenance Plan',
      calories: '2000-2200',
      focus: 'Balanced Nutrition',
      meals: ['Balanced Breakfast', 'Nutritious Lunch', 'Healthy Dinner'],
      image: { uri: getImageByCategory('nutrition') }
    }
  ];

  const macroNutrients = [
    { name: 'Protein', percentage: 25, color: '#FF6B6B', description: 'Builds and repairs muscles' },
    { name: 'Carbohydrates', percentage: 45, color: '#4ECDC4', description: 'Primary energy source' },
    { name: 'Fats', percentage: 30, color: '#FFD166', description: 'Essential for hormone production' },
  ];

  if (!fontsLoaded) return null;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />
      <LinearGradient
        colors={['#FFD166', '#F7931E', '#FF6B6B']}
        style={StyleSheet.absoluteFill}
      />
      
      {/* Background Particles */}
      {[...Array(15)].map((_, i) => (
        <AdvancedParticle 
          key={i} 
          style={[
            styles.particle, 
            { 
              left: `${Math.random() * 100}%`, 
              width: Math.random() * 4 + 2, 
              height: Math.random() * 4 + 2 
            }
          ]} 
          delay={Math.random() * 3000}
          speed={Math.random() * 2 + 0.5}
        />
      ))}
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Nutrition Guide</Text>
        <TouchableOpacity style={styles.shareButton}>
          <Ionicons name="share-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Image Slider */}
      <ImageSlider images={nutritionImages} />

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {['overview', 'plans', 'tips', 'videos'].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[styles.tab, activeTab === tab && styles.activeTab]}
            onPress={() => setActiveTab(tab)}
          >
            <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'overview' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Fuel Your Fitness</Text>
            <Text style={styles.description}>
              Proper nutrition is the foundation of any fitness journey. What you eat directly impacts 
              your energy levels, performance, and recovery. Learn to make smart food choices that 
              support your goals.
            </Text>
            
            <View style={styles.benefitsContainer}>
              <View style={styles.benefitCard}>
                <Ionicons name="trending-up" size={24} color="#4ECDC4" />
                <Text style={styles.benefitTitle}>Energy Boost</Text>
                <Text style={styles.benefitText}>Sustain energy throughout your day</Text>
              </View>
              <View style={styles.benefitCard}>
                <Ionicons name="fitness" size={24} color="#FF6B6B" />
                <Text style={styles.benefitTitle}>Muscle Growth</Text>
                <Text style={styles.benefitText}>Support muscle development and recovery</Text>
              </View>
              <View style={styles.benefitCard}>
                <Ionicons name="heart" size={24} color="#96CEB4" />
                <Text style={styles.benefitTitle}>Health Benefits</Text>
                <Text style={styles.benefitText}>Improve overall health and wellness</Text>
              </View>
            </View>
          </View>
        )}

        {activeTab === 'plans' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Meal Plans</Text>
            <Text style={styles.description}>
              Choose a meal plan that aligns with your fitness goals and lifestyle.
            </Text>
            
            {mealPlans.map((plan, index) => (
              <TouchableOpacity key={index} style={styles.planCard}>
                <LinearGradient
                  colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
                  style={styles.planCardGradient}
                >
                  <SafeImage 
                    source={plan.image} 
                    style={styles.planImage}
                    resizeMode="cover"
                  />
                  <View style={styles.planInfo}>
                    <Text style={styles.planTitle}>{plan.title}</Text>
                    <Text style={styles.planCalories}>{plan.calories} calories</Text>
                    <Text style={styles.planFocus}>Focus: {plan.focus}</Text>
                    <View style={styles.planMeals}>
                      {plan.meals.map((meal, mealIndex) => (
                        <Text key={mealIndex} style={styles.planMeal}>• {meal}</Text>
                      ))}
                    </View>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {activeTab === 'tips' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Nutrition Tips</Text>
            <Text style={styles.description}>
              Follow these essential tips for better nutrition and health.
            </Text>
            
            {nutritionTips.map((tip, index) => (
              <View key={index} style={styles.tipCard}>
                <View style={[styles.tipIcon, { backgroundColor: tip.color }]}>
                  <Ionicons name={tip.icon} size={24} color="#fff" />
                </View>
                <View style={styles.tipContent}>
                  <Text style={styles.tipTitle}>{tip.title}</Text>
                  <Text style={styles.tipDescription}>{tip.description}</Text>
                </View>
              </View>
            ))}
          </View>
        )}

        {activeTab === 'videos' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Nutrition Videos</Text>
            <Text style={styles.description}>
              Watch expert nutrition videos to learn more about healthy eating.
            </Text>
            
            {nutritionVideos.map((video, index) => (
              <VideoPlayer
                key={index}
                videoUrl={video.videoUrl}
                title={video.title}
                duration={video.duration}
                style={styles.videoItem}
              />
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(4),
    paddingTop: hp(2),
    paddingBottom: hp(1),
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: wp(2),
    borderRadius: wp(2),
  },
  headerTitle: {
    fontSize: wp(7),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  shareButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: wp(2),
    borderRadius: wp(2),
  },
  sliderContainer: {
    height: hp(30),
    position: 'relative',
  },
  slide: {
    width: width,
    height: hp(30),
    position: 'relative',
  },
  slideImage: {
    width: '100%',
    height: '100%',
  },
  slideOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: hp(10),
    justifyContent: 'flex-end',
    paddingBottom: hp(2),
    paddingHorizontal: wp(4),
  },
  slideTitle: {
    fontSize: wp(6),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  slideSubtitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
    marginTop: hp(0.5),
  },
  pagination: {
    position: 'absolute',
    bottom: hp(1),
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    width: wp(2),
    height: wp(2),
    borderRadius: wp(1),
    marginHorizontal: wp(1),
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,0.1)',
    marginHorizontal: wp(4),
    borderRadius: wp(3),
    padding: wp(1),
  },
  tab: {
    flex: 1,
    paddingVertical: hp(1.5),
    alignItems: 'center',
    borderRadius: wp(2),
  },
  activeTab: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  tabText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255,255,255,0.7)',
  },
  activeTabText: {
    color: '#fff',
  },
  content: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: wp(10),
    borderTopRightRadius: wp(10),
    marginTop: hp(2),
  },
  section: {
    padding: wp(5),
  },
  sectionTitle: {
    fontSize: wp(6),
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: hp(1),
  },
  description: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    lineHeight: hp(2.5),
    marginBottom: hp(2),
  },
  benefitsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  benefitCard: {
    width: wp(40),
    backgroundColor: '#fff',
    borderRadius: wp(5),
    padding: wp(4),
    marginVertical: hp(0.5),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  benefitTitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginTop: hp(1),
    textAlign: 'center',
  },
  benefitText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: hp(0.5),
  },
  planCard: {
    marginBottom: hp(1.5),
  },
  planCardGradient: {
    borderRadius: wp(8),
    overflow: 'hidden',
  },
  planImage: {
    width: '100%',
    height: hp(15),
  },
  planInfo: {
    padding: wp(4),
  },
  planTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: hp(0.5),
  },
  planCalories: {
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: '#FFD166',
    marginBottom: hp(0.5),
  },
  planFocus: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#FF6B6B',
    marginBottom: hp(0.5),
  },
  planMeals: {
    marginTop: hp(0.5),
  },
  planMeal: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
    marginBottom: hp(0.3),
  },
  tipCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: wp(5),
    padding: wp(4),
    marginBottom: hp(1),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  tipIcon: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(4),
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginBottom: hp(0.3),
  },
  tipDescription: {
    fontSize: wp(3.8),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    lineHeight: hp(2.2),
  },
  videoCard: {
    width: '100%',
    height: hp(25),
    borderRadius: wp(5),
    overflow: 'hidden',
    marginBottom: hp(2),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  videoTouchable: {
    flex: 1,
  },
  videoGradient: {
    flex: 1,
    borderRadius: wp(5),
    overflow: 'hidden',
  },
  videoThumbnail: {
    width: '100%',
    height: '100%',
  },
  videoOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: hp(10),
    justifyContent: 'space-between',
    padding: wp(4),
    paddingBottom: hp(2),
  },
  videoInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  videoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  videoIconContainer: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: wp(1.5),
    borderRadius: wp(3),
    marginRight: wp(2),
  },
  videoMeta: {
    flexDirection: 'column',
  },
  videoTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  videoDuration: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
    marginTop: hp(0.2),
  },
  videoControls: {
    alignItems: 'flex-end',
  },
  playButton: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButtonGradient: {
    flex: 1,
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: wp(4),
  },
  loadingGradient: {
    flexDirection: 'column',
    alignItems: 'center',
    padding: wp(4),
  },
  loadingText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    marginTop: hp(1),
  },
  videoItem: {
    marginBottom: hp(1.5),
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 100,
  },
}); 