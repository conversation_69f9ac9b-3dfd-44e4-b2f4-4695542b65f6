import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  ScrollView,
  Switch,
  Dimensions,
  Animated
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';

const { width, height } = Dimensions.get('window');

// Advanced Particle component
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animSpeed = 4000 * speed + Math.random() * 2000;
    const animDelay = Math.random() * 3000 + delay;
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, { toValue: 1, duration: animSpeed, useNativeDriver: true }),
        Animated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = Animated.sequence([
      Animated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 1, duration: animSpeed - 1000, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);
    
    const timeoutId = setTimeout(() => {
      animation.start();
      Animated.loop(opacitySequence).start();
    }, animDelay);

    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, [delay, speed]);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <Animated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

const FloatingShape = ({ style, delay = 0, rotationSpeed = 1 }) => {
  const floatAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const floatAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
        Animated.timing(floatAnim, {
          toValue: 0,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
      ])
    );
    
    const rotateAnimation = Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 15000 * rotationSpeed,
        useNativeDriver: true,
      })
    );
    
    const timeoutId = setTimeout(() => {
      floatAnimation.start();
      rotateAnimation.start();
    }, delay);

    return () => {
      clearTimeout(timeoutId);
      floatAnimation.stop();
      rotateAnimation.stop();
    };
  }, [delay, rotationSpeed]);

  const translateY = floatAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });

  const rotate = rotationAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{ translateY }, { rotate }],
        },
      ]}
    />
  );
};

export default function NotificationsManagement() {
  const router = useRouter();
  const [notifications, setNotifications] = useState([]);
  const [filteredNotifications, setFilteredNotifications] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [modalVisible, setModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState(null);
  const [loading, setLoading] = useState(true);

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  const notificationTypes = ['all', 'push', 'email', 'sms', 'in-app'];
  const priorities = ['low', 'medium', 'high', 'urgent'];
  const audiences = ['all_users', 'active_users', 'inactive_users', 'premium_users', 'new_users'];

  useEffect(() => {
    fetchNotifications();
  }, []);

  useEffect(() => {
    filterNotifications();
  }, [searchQuery, selectedType, notifications]);

  const fetchNotifications = async () => {
    try {
      const notificationsData = await AsyncStorage.getItem('@notifications');
      if (notificationsData) {
        const parsedNotifications = JSON.parse(notificationsData);
        setNotifications(parsedNotifications);
      } else {
        // Initialize with sample notifications
        const sampleNotifications = [
          {
            id: '1',
            title: 'Welcome to Fitness App!',
            message: 'Start your fitness journey today with our personalized plans.',
            type: 'push',
            priority: 'medium',
            audience: 'new_users',
            scheduled: false,
            sent: true,
            sentAt: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            active: true
          },
          {
            id: '2',
            title: 'Weekly Progress Update',
            message: 'Check out your amazing progress this week!',
            type: 'email',
            priority: 'low',
            audience: 'active_users',
            scheduled: true,
            scheduledFor: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            sent: false,
            createdAt: new Date().toISOString(),
            active: true
          }
        ];
        await AsyncStorage.setItem('@notifications', JSON.stringify(sampleNotifications));
        setNotifications(sampleNotifications);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      Alert.alert('Error', 'Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  const filterNotifications = () => {
    let filtered = notifications;

    // Filter by type
    if (selectedType !== 'all') {
      filtered = filtered.filter(notification => notification.type === selectedType);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(notification =>
        notification.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        notification.message?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort by creation date (newest first)
    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    setFilteredNotifications(filtered);
  };

  const handleNotificationAction = (notification, action) => {
    setSelectedNotification(notification);
    switch (action) {
      case 'view':
        setModalVisible(true);
        break;
      case 'edit':
        setEditModalVisible(true);
        break;
      case 'send':
        handleSendNotification(notification);
        break;
      case 'toggle':
        handleToggleNotification(notification);
        break;
      case 'delete':
        handleDeleteNotification(notification);
        break;
    }
  };

  const handleSendNotification = (notification) => {
    Alert.alert(
      'Send Notification',
      `Are you sure you want to send "${notification.title}" to ${notification.audience}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send',
          onPress: async () => {
            try {
              const updatedNotifications = notifications.map(n =>
                n.id === notification.id 
                  ? { ...n, sent: true, sentAt: new Date().toISOString() }
                  : n
              );
              await AsyncStorage.setItem('@notifications', JSON.stringify(updatedNotifications));
              setNotifications(updatedNotifications);
              Alert.alert('Success', 'Notification sent successfully!');
            } catch (error) {
              Alert.alert('Error', 'Failed to send notification');
            }
          }
        }
      ]
    );
  };

  const handleToggleNotification = async (notification) => {
    try {
      const updatedNotifications = notifications.map(n =>
        n.id === notification.id ? { ...n, active: !n.active } : n
      );
      await AsyncStorage.setItem('@notifications', JSON.stringify(updatedNotifications));
      setNotifications(updatedNotifications);
      Alert.alert('Success', `Notification ${notification.active ? 'deactivated' : 'activated'}`);
    } catch (error) {
      Alert.alert('Error', 'Failed to update notification status');
    }
  };

  const handleDeleteNotification = (notification) => {
    Alert.alert(
      'Delete Notification',
      `Are you sure you want to delete "${notification.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const updatedNotifications = notifications.filter(n => n.id !== notification.id);
              await AsyncStorage.setItem('@notifications', JSON.stringify(updatedNotifications));
              setNotifications(updatedNotifications);
              Alert.alert('Success', 'Notification deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete notification');
            }
          }
        }
      ]
    );
  };

  const handleSaveNotification = async (notificationData, isNew = false) => {
    try {
      let updatedNotifications;
      
      if (isNew) {
        const newNotification = {
          ...notificationData,
          id: Date.now().toString(),
          createdAt: new Date().toISOString(),
          sent: false
        };
        updatedNotifications = [...notifications, newNotification];
      } else {
        updatedNotifications = notifications.map(n =>
          n.id === notificationData.id 
            ? { ...notificationData, updatedAt: new Date().toISOString() } 
            : n
        );
      }

      await AsyncStorage.setItem('@notifications', JSON.stringify(updatedNotifications));
      setNotifications(updatedNotifications);
      setEditModalVisible(false);
      Alert.alert('Success', `Notification ${isNew ? 'created' : 'updated'} successfully`);
    } catch (error) {
      Alert.alert('Error', `Failed to ${isNew ? 'create' : 'update'} notification`);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (notification) => {
    if (!notification.active) return '#95a5a6';
    if (notification.sent) return '#4ECDC4';
    if (notification.scheduled) return '#FECA57';
    return '#45B7D1';
  };

  const getStatusText = (notification) => {
    if (!notification.active) return 'Inactive';
    if (notification.sent) return 'Sent';
    if (notification.scheduled) return 'Scheduled';
    return 'Draft';
  };

  const renderNotificationItem = ({ item }) => (
    <View style={styles.notificationCard}>
      <View style={styles.notificationHeader}>
        <View style={styles.notificationInfo}>
          <Text style={styles.notificationTitle}>{item.title}</Text>
          <Text style={styles.notificationType}>
            {item.type.toUpperCase()} • {item.priority.toUpperCase()} • {item.audience.replace('_', ' ').toUpperCase()}
          </Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item) }]}>
          <Text style={styles.statusText}>{getStatusText(item)}</Text>
        </View>
      </View>

      <Text style={styles.notificationMessage} numberOfLines={2}>
        {item.message}
      </Text>

      <View style={styles.notificationMeta}>
        <Text style={styles.metaText}>
          Created: {formatDate(item.createdAt)}
        </Text>
        {item.scheduled && (
          <Text style={styles.metaText}>
            Scheduled: {formatDate(item.scheduledFor)}
          </Text>
        )}
        {item.sent && (
          <Text style={styles.metaText}>
            Sent: {formatDate(item.sentAt)}
          </Text>
        )}
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#4ECDC4' }]}
          onPress={() => handleNotificationAction(item, 'view')}
        >
          <Ionicons name="eye" size={16} color="#fff" />
          <Text style={styles.actionButtonText}>View</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#45B7D1' }]}
          onPress={() => handleNotificationAction(item, 'edit')}
        >
          <Ionicons name="create" size={16} color="#fff" />
          <Text style={styles.actionButtonText}>Edit</Text>
        </TouchableOpacity>

        {!item.sent && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#96CEB4' }]}
            onPress={() => handleNotificationAction(item, 'send')}
          >
            <Ionicons name="send" size={16} color="#fff" />
            <Text style={styles.actionButtonText}>Send</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: item.active ? '#FECA57' : '#4ECDC4' }]}
          onPress={() => handleNotificationAction(item, 'toggle')}
        >
          <Ionicons name={item.active ? "pause" : "play"} size={16} color="#fff" />
          <Text style={styles.actionButtonText}>{item.active ? 'Pause' : 'Activate'}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#FF6B6B' }]}
          onPress={() => handleNotificationAction(item, 'delete')}
        >
          <Ionicons name="trash" size={16} color="#fff" />
          <Text style={styles.actionButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (!fontsLoaded) return null;

  return (
    <>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={{ flex: 1 }}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Example floating shape/particle */}
        <Animated.View style={{ position: 'absolute', top: 40, left: 30, width: 60, height: 60, borderRadius: 30, backgroundColor: 'rgba(255,255,255,0.08)' }} />
        {/* Main content below, unchanged logic */}
        <View style={{ flex: 1 }}>
          <View style={styles.outerContainer}>
            {/* Animated Particles and Floating Shapes */}
            <AdvancedParticle style={styles.particle1} delay={0} speed={1.2} />
            <AdvancedParticle style={styles.particle2} delay={500} speed={0.8} />
            <AdvancedParticle style={styles.particle3} delay={1000} speed={1.5} />
            <FloatingShape style={styles.floatingShape1} delay={0} rotationSpeed={0.5} />
            <FloatingShape style={styles.floatingShape2} delay={1000} rotationSpeed={1.2} />
            <View style={[styles.glowCircle, { top: 60, right: 30 }]} />
            <View style={[styles.glowCircle, { bottom: 80, left: 20 }]} />
            {/* Main Content */}
            <LinearGradient
              colors={['#1E90FF', '#00C9A7', '#21D4FD']}
              style={styles.container}
            >
              {/* Header */}
              <View style={styles.header}>
                <TouchableOpacity
                  style={styles.backButton}
                  onPress={() => router.back()}
                >
                  <Ionicons name="arrow-back" size={24} color="#fff" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Notifications</Text>
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={() => setEditModalVisible(true)}
                >
                  <Ionicons name="add" size={24} color="#fff" />
                </TouchableOpacity>
              </View>

              {/* Search and Filter */}
              <View style={styles.searchContainer}>
                <View style={styles.searchInputContainer}>
                  <Ionicons name="search" size={20} color="#7f8c8d" style={styles.searchIcon} />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search notifications..."
                    placeholderTextColor="#7f8c8d"
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                  />
                </View>
                
                <View style={styles.filterContainer}>
                  <Picker
                    selectedValue={selectedType}
                    style={styles.typePicker}
                    onValueChange={setSelectedType}
                  >
                    {notificationTypes.map((type) => (
                      <Picker.Item 
                        key={type} 
                        label={type === 'all' ? 'All Types' : type.toUpperCase()} 
                        value={type} 
                      />
                    ))}
                  </Picker>
                </View>
              </View>

              {/* Notifications List */}
              <FlatList
                data={filteredNotifications}
                renderItem={renderNotificationItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
              />

              {/* Notification Detail Modal */}
              <Modal
                animationType="slide"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
              >
                <NotificationDetailModal
                  notification={selectedNotification}
                  onClose={() => setModalVisible(false)}
                  formatDate={formatDate}
                />
              </Modal>

              {/* Edit/Create Notification Modal */}
              <Modal
                animationType="slide"
                transparent={true}
                visible={editModalVisible}
                onRequestClose={() => setEditModalVisible(false)}
              >
                <NotificationFormModal
                  notification={selectedNotification}
                  onSave={(data) => handleSaveNotification(data, !selectedNotification)}
                  onClose={() => {
                    setEditModalVisible(false);
                    setSelectedNotification(null);
                  }}
                  title={selectedNotification ? 'Edit Notification' : 'Create Notification'}
                  priorities={priorities}
                  audiences={audiences}
                  notificationTypes={notificationTypes.filter(t => t !== 'all')}
                />
              </Modal>
            </LinearGradient>
          </View>
        </View>
      </LinearGradient>
    </>
  );
}

// Notification Detail Modal Component
const NotificationDetailModal = ({ notification, onClose, formatDate }) => {
  if (!notification) return null;

  return (
    <View style={styles.modalOverlay}>
      <View style={styles.modalContent}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Notification Details</Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#2c3e50" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalBody}>
          <Text style={styles.modalLabel}>Title:</Text>
          <Text style={styles.modalValue}>{notification.title}</Text>

          <Text style={styles.modalLabel}>Message:</Text>
          <Text style={styles.modalValue}>{notification.message}</Text>

          <Text style={styles.modalLabel}>Type:</Text>
          <Text style={styles.modalValue}>{notification.type.toUpperCase()}</Text>

          <Text style={styles.modalLabel}>Priority:</Text>
          <Text style={styles.modalValue}>{notification.priority.toUpperCase()}</Text>

          <Text style={styles.modalLabel}>Audience:</Text>
          <Text style={styles.modalValue}>{notification.audience.replace('_', ' ').toUpperCase()}</Text>

          <Text style={styles.modalLabel}>Status:</Text>
          <Text style={styles.modalValue}>
            {notification.active ? 'Active' : 'Inactive'} • {notification.sent ? 'Sent' : 'Not Sent'}
          </Text>

          <Text style={styles.modalLabel}>Created:</Text>
          <Text style={styles.modalValue}>{formatDate(notification.createdAt)}</Text>

          {notification.scheduled && (
            <>
              <Text style={styles.modalLabel}>Scheduled For:</Text>
              <Text style={styles.modalValue}>{formatDate(notification.scheduledFor)}</Text>
            </>
          )}

          {notification.sent && (
            <>
              <Text style={styles.modalLabel}>Sent At:</Text>
              <Text style={styles.modalValue}>{formatDate(notification.sentAt)}</Text>
            </>
          )}
        </ScrollView>
      </View>
    </View>
  );
};

// Notification Form Modal Component
const NotificationFormModal = ({ notification, onSave, onClose, title, priorities, audiences, notificationTypes }) => {
  const [formData, setFormData] = useState(notification || {
    title: '',
    message: '',
    type: 'push',
    priority: 'medium',
    audience: 'all_users',
    scheduled: false,
    scheduledFor: '',
    active: true
  });

  const handleSave = () => {
    if (!formData.title || !formData.message) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    onSave(formData);
  };

  return (
    <View style={styles.modalOverlay}>
      <View style={styles.modalContent}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>{title}</Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#2c3e50" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalBody}>
          <Text style={styles.inputLabel}>Title *</Text>
          <TextInput
            style={styles.textInput}
            value={formData.title}
            onChangeText={(text) => setFormData({...formData, title: text})}
            placeholder="Enter notification title"
          />

          <Text style={styles.inputLabel}>Message *</Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            value={formData.message}
            onChangeText={(text) => setFormData({...formData, message: text})}
            placeholder="Enter notification message"
            multiline
            numberOfLines={4}
          />

          <Text style={styles.inputLabel}>Type</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={formData.type}
              style={styles.picker}
              onValueChange={(value) => setFormData({...formData, type: value})}
            >
              {notificationTypes.map((type) => (
                <Picker.Item key={type} label={type.toUpperCase()} value={type} />
              ))}
            </Picker>
          </View>

          <Text style={styles.inputLabel}>Priority</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={formData.priority}
              style={styles.picker}
              onValueChange={(value) => setFormData({...formData, priority: value})}
            >
              {priorities.map((priority) => (
                <Picker.Item key={priority} label={priority.toUpperCase()} value={priority} />
              ))}
            </Picker>
          </View>

          <Text style={styles.inputLabel}>Audience</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={formData.audience}
              style={styles.picker}
              onValueChange={(value) => setFormData({...formData, audience: value})}
            >
              {audiences.map((audience) => (
                <Picker.Item 
                  key={audience} 
                  label={audience.replace('_', ' ').toUpperCase()} 
                  value={audience} 
                />
              ))}
            </Picker>
          </View>

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Schedule for later</Text>
            <Switch
              value={formData.scheduled}
              onValueChange={(value) => setFormData({...formData, scheduled: value})}
              trackColor={{ false: '#ecf0f1', true: '#4ECDC4' }}
              thumbColor={formData.scheduled ? '#fff' : '#f4f3f4'}
            />
          </View>

          {formData.scheduled && (
            <>
              <Text style={styles.inputLabel}>Scheduled Date & Time</Text>
              <TextInput
                style={styles.textInput}
                value={formData.scheduledFor}
                onChangeText={(text) => setFormData({...formData, scheduledFor: text})}
                placeholder="YYYY-MM-DD HH:MM"
              />
            </>
          )}

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Active</Text>
            <Switch
              value={formData.active}
              onValueChange={(value) => setFormData({...formData, active: value})}
              trackColor={{ false: '#ecf0f1', true: '#4ECDC4' }}
              thumbColor={formData.active ? '#fff' : '#f4f3f4'}
            />
          </View>

          <View style={styles.modalActions}>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  particle1: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderRadius: 12,
    left: width * 0.1,
    top: 120,
    zIndex: 0,
  },
  particle2: {
    position: 'absolute',
    width: 18,
    height: 18,
    borderRadius: 9,
    left: width * 0.8,
    top: 200,
    zIndex: 0,
  },
  particle3: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    left: width * 0.2,
    top: 500,
    zIndex: 0,
  },
  particleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  floatingShape1: {
    position: 'absolute',
    width: 32,
    height: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    left: width * 0.85,
    top: 100,
    zIndex: 0,
  },
  floatingShape2: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    left: width * 0.05,
    top: 600,
    zIndex: 0,
  },
  glowCircle: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    zIndex: 0,
  },
  container: {
    flex: 1,
    paddingTop: 50,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  addButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 15,
    marginBottom: 10,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
  },
  filterContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
  },
  typePicker: {
    height: 50,
    color: '#2c3e50',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  notificationCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  notificationInfo: {
    flex: 1,
    marginRight: 10,
  },
  notificationTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  notificationType: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  notificationMessage: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    marginBottom: 12,
    lineHeight: 20,
  },
  notificationMeta: {
    marginBottom: 16,
  },
  metaText: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#95a5a6',
    marginBottom: 2,
  },
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 8,
    marginBottom: 5,
    minWidth: '18%',
    justifyContent: 'center',
  },
  actionButtonText: {
    fontSize: 10,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginLeft: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
  },
  modalBody: {
    maxHeight: 400,
  },
  modalLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginTop: 12,
    marginBottom: 4,
  },
  modalValue: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    marginBottom: 8,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginTop: 12,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ecf0f1',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
    marginBottom: 12,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ecf0f1',
    borderRadius: 8,
    marginBottom: 12,
  },
  picker: {
    height: 50,
    color: '#2c3e50',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    marginBottom: 12,
  },
  switchLabel: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    backgroundColor: '#95a5a6',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    marginRight: 10,
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: '#4ECDC4',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    marginLeft: 10,
  },
  saveButtonText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    textAlign: 'center',
  },
});