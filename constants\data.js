// Demo exercises data for the fitness app
export const demoExercises = [
  {
    id: 1,
    name: "Push-ups",
    bodyPart: "chest",
    equipment: "body weight",
    gifUrl: "https://v2.exercisedb.io/image/QmYjRQYhvdHdFUExTTVFZQmNEY3JOdz09",
    target: "pectorals",
    secondaryMuscles: ["triceps", "anterior deltoid"],
    instructions: [
      "Start in a plank position with your hands placed slightly wider than shoulder-width apart.",
      "Lower your body until your chest nearly touches the floor.",
      "Push yourself back up to the starting position.",
      "Keep your body in a straight line throughout the movement."
    ]
  },
  {
    id: 2,
    name: "Squats",
    bodyPart: "legs",
    equipment: "body weight",
    gifUrl: "https://v2.exercisedb.io/image/QmYjRQYhvdHdFUExTTVFZQmNEY3JOdz09",
    target: "quadriceps",
    secondaryMuscles: ["glutes", "hamstrings", "calves"],
    instructions: [
      "Stand with feet shoulder-width apart.",
      "Lower your body as if sitting back into a chair.",
      "Keep your chest up and knees behind your toes.",
      "Return to standing position."
    ]
  },
  {
    id: 3,
    name: "Plank",
    bodyPart: "core",
    equipment: "body weight",
    gifUrl: "https://v2.exercisedb.io/image/QmYjRQYhvdHdFUExTTVFZQmNEY3JOdz09",
    target: "abs",
    secondaryMuscles: ["shoulders", "back"],
    instructions: [
      "Start in a push-up position.",
      "Lower onto your forearms.",
      "Keep your body in a straight line.",
      "Hold the position for the desired time."
    ]
  }
];

// Video tutorial functionality
export const isVideoTutorialEnabled = (exerciseName) => {
  // For demo purposes, enable video tutorials for all exercises
  return true;
};

export const getVideoUrl = (exerciseName) => {
  // Demo video URLs - replace with actual video URLs
  const videoUrls = {
    "Push-ups": "https://www.youtube.com/watch?v=IODxDxX7oi4",
    "Squats": "https://www.youtube.com/watch?v=aclHkVaku9U",
    "Plank": "https://www.youtube.com/watch?v=ASdvN_XEl_c"
  };
  
  return videoUrls[exerciseName] || "https://www.youtube.com/watch?v=dQw4w9WgXcQ";
};

export const handleVideoTutorial = (exerciseName) => {
  const videoUrl = getVideoUrl(exerciseName);
  // This function can be used to handle video tutorial opening
  return videoUrl;
};

// Additional exercise categories
export const bodyParts = [
  "back",
  "cardio", 
  "chest",
  "lower arms",
  "lower legs",
  "neck",
  "shoulders",
  "upper arms",
  "upper legs",
  "waist"
];

export const equipmentTypes = [
  "assisted",
  "band",
  "barbell",
  "body weight",
  "bosu ball",
  "cable",
  "dumbbell",
  "elliptical machine",
  "ez barbell",
  "hammer",
  "kettlebell",
  "leverage machine",
  "medicine ball",
  "olympic barbell",
  "resistance band",
  "roller",
  "rope",
  "skierg machine",
  "sled machine",
  "smith machine",
  "stability ball",
  "stationary bike",
  "stepmill machine",
  "tire",
  "trap bar",
  "upper body ergometer",
  "weighted",
  "wheel roller"
];
