import React, { useRef, useEffect, useCallback } from 'react';
import {
  Animated,
  Dimensions,
  Image,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
} from 'react-native';
// import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
// import { useFonts, Poppins_400Regular, Poppins_700Bold, Poppins_600SemiBold } from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from '../utils/responsive';
import { StatusBar } from 'expo-status-bar';
import SafeImage from '../components/SafeImage';
import { getImageByCategory } from '../constants/remoteImages';

const { width, height } = Dimensions.get('window');

// Advanced Particle component
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.parallel([
          Animated.timing(particleAnim, {
            toValue: 1,
            duration: (4000 + Math.random() * 3000) * speed,
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 40,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(opacityAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      ])
    );
    
    Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 8000 + Math.random() * 4000,
        useNativeDriver: true,
      })
    ).start();
    
    setTimeout(() => animation.start(), delay);
  }, []);

  return (
    <Animated.View
      style={[
        style,
        {
          opacity: opacityAnim,
          transform: [
            {
              translateY: particleAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -height * 0.5],
              }),
            },
            {
              scale: scaleAnim,
            },
            {
              rotate: rotationAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '360deg'],
              }),
            },
          ],
        },
      ]}
    >
      <View style={[styles.particleGradient, { backgroundColor: 'rgba(102, 126, 234, 0.8)' }]} />
    </Animated.View>
  );
};

// Helper to get robust image source for role cards
const getRoleImage = (role) => {
  const url = getImageByCategory(role);
  return { uri: url || getImageByCategory('default') };
};

export default function Role() {
  const router = useRouter();
  // const [fontsLoaded] = useFonts({
  //   Poppins_400Regular,
  //   Poppins_700Bold,
  //   Poppins_600SemiBold,
  // });
  const fontsLoaded = true;

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const logoScale = useRef(new Animated.Value(0.8)).current;
  const buttonScale = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;
  const scaleAdmin = useRef(new Animated.Value(1)).current;
  const scaleUser = useRef(new Animated.Value(1)).current;
  const backScale = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Main entrance animations
    Animated.stagger(200, [
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 1200,
        useNativeDriver: true,
      }),
      Animated.spring(logoScale, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.spring(buttonScale, {
        toValue: 1,
        friction: 6,
        tension: 50,
        delay: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Continuous animations
    Animated.loop(
      Animated.sequence([
        Animated.timing(rotationAnim, {
          toValue: 1,
          duration: 25000,
          useNativeDriver: true,
        }),
        Animated.timing(rotationAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    ).start();

    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.08,
          duration: 2500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2500,
          useNativeDriver: true,
        }),
      ])
    ).start();

    Animated.loop(
      Animated.sequence([
        Animated.timing(glowAnim, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnim, {
          toValue: 0,
          duration: 3000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const animateButton = useCallback((scaleAnim, route) => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 3,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start(() => {
      scaleAnim.setValue(1);
      router.replace(route);
    });
  }, [router]);

  const handleBackPress = useCallback(() => {
    animateButton(backScale, '/');
  }, [animateButton, backScale]);

  const handleAdminLogin = useCallback(() => {
    animateButton(scaleAdmin, '/admin/login');
  }, [animateButton, scaleAdmin]);

  const handleUserLogin = useCallback(() => {
    animateButton(scaleUser, '/user/login');
  }, [animateButton, scaleUser]);

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <View style={[StyleSheet.absoluteFill, { backgroundColor: '#1a1a2e' }]} />
        <ActivityIndicator size="large" color="#667eea" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* Professional Animated Background */}
      <Animated.View style={[styles.backgroundContainer, { opacity: fadeAnim }]}>
        <View style={[styles.background, { backgroundColor: '#1a1a2e' }]} />
        
        <Animated.View
          style={[
            styles.gradientOverlay,
            {
              opacity: glowAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.4, 0.8],
              }),
            },
          ]}
        >
          <LinearGradient
            colors={['rgba(102, 126, 234, 0.3)', 'rgba(118, 75, 162, 0.2)', 'rgba(240, 147, 251, 0.1)', 'transparent']}
            style={styles.overlayGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </Animated.View>

        {/* Advanced Particles */}
        <AdvancedParticle
          style={styles.particle1}
          delay={0}
          speed={1.2}
        />
        <AdvancedParticle
          style={styles.particle2}
          delay={500}
          speed={0.8}
        />
        <AdvancedParticle
          style={styles.particle3}
          delay={1000}
          speed={1.5}
        />

        {/* Animated background circles */}
        <Animated.View
          style={[
            styles.circle1,
            {
              transform: [
                {
                  rotate: rotationAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0deg', '360deg'],
                  }),
                },
              ],
              shadowOpacity: glowAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.1, 0.3],
              }),
            },
          ]}
        />
        <Animated.View
          style={[
            styles.circle2,
            {
              transform: [
                {
                  rotate: rotationAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['360deg', '0deg'],
                  }),
                },
              ],
              shadowOpacity: glowAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.08, 0.25],
              }),
            },
          ]}
        />
        <Animated.View
          style={[
            styles.circle3,
            {
              transform: [
                {
                  rotate: rotationAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0deg', '-360deg'],
                  }),
                },
              ],
              shadowOpacity: glowAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.06, 0.2],
              }),
            },
          ]}
        />
        
        {/* Glow circles */}
        <View style={[styles.glowCircle, { top: hp(10), right: wp(10) }]} />
        <View style={[styles.glowCircle, { bottom: hp(20), left: wp(5) }]} />
      </Animated.View>

      {/* Back Button */}
      <Animated.View style={[styles.backWrapper, { transform: [{ scale: backScale }] }]}>
        <TouchableOpacity
          style={styles.backButton}
          activeOpacity={0.8}
          onPress={handleBackPress}
        >
          <LinearGradient
            colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
            style={styles.backButtonGradient}
        >
            <Ionicons name="arrow-back" size={hp(3)} color="#fff" />
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>

      {/* Main Content */}
      <View style={styles.contentContainer}>
        <Animated.View 
          style={[
            styles.content,
            { 
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          {/* Logo Section */}
          <Animated.View 
            style={[
              styles.logoContainer,
              { transform: [{ scale: logoScale }] }
            ]}
          >
            <LinearGradient
              colors={['rgba(102, 126, 234, 0.3)', 'rgba(118, 75, 162, 0.2)', 'rgba(240, 147, 251, 0.1)']}
              style={styles.logoGradient}
            >
              <Ionicons name="fitness" size={hp(8)} color="#fff" />
            </LinearGradient>
          </Animated.View>

          {/* Title Section */}
          <View style={styles.textContainer}>
            <Text style={styles.title}>Choose Your Role</Text>
            <Text style={styles.subtitle}>Select your fitness journey path</Text>
          </View>

          {/* Role Selection Cards */}
          <Animated.View 
            style={[
              styles.cardsContainer,
              { 
                transform: [
                  { scale: buttonScale },
                  { scale: pulseAnim }
                ] 
              }
            ]}
          >
            {/* Admin/Trainer Card */}
            <Animated.View style={{ transform: [{ scale: scaleAdmin }] }}>
              <TouchableOpacity
                style={[styles.roleCard, styles.trainerCard]}
                onPress={handleAdminLogin}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={['#ffffff', '#f8f8f8']}
                  style={styles.trainerCardBg}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <View style={styles.imageContainer}>
                    <LinearGradient
                      colors={['rgba(102, 126, 234, 0.2)', 'rgba(118, 75, 162, 0.2)']}
                      style={styles.imageBackground}
                    >
                      <SafeImage 
                        source={getRoleImage('admin')} 
                        fallbackSource={{ uri: getImageByCategory('default') }}
                        style={styles.roleImage} 
                        resizeMode="cover" 
                      />
                    </LinearGradient>
                  </View>
                  <View style={styles.textContent}>
                    <Text style={styles.trainerTitle}>Trainer</Text>
                    <Text style={styles.trainerDescription}>Manage workouts and guide users</Text>
                  </View>
                  <View style={styles.cardIconContainer}>
                    <Ionicons name="chevron-forward" size={24} color="rgba(102, 126, 234, 0.8)" />
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>

            {/* User Card */}
            <Animated.View style={{ transform: [{ scale: scaleUser }] }}>
              <TouchableOpacity
                style={[styles.roleCard, styles.userCard]}
                onPress={handleUserLogin}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={['#667eea', '#764ba2']}
                  style={styles.userCardBg}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                >
                  <View style={styles.imageContainer}>
                    <LinearGradient
                      colors={['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.1)']}
                      style={styles.imageBackground}
                    >
                      <SafeImage 
                        source={getRoleImage('user')} 
                        fallbackSource={{ uri: getImageByCategory('default') }}
                        style={styles.roleImage} 
                        resizeMode="cover" 
                      />
                    </LinearGradient>
                  </View>
                  <View style={styles.textContent}>
                    <Text style={styles.userTitle}>User</Text>
                    <Text style={styles.userDescription}>Track your fitness journey</Text>
                  </View>
                  <View style={styles.cardIconContainer}>
                    <Ionicons name="chevron-forward" size={24} color="#ffffff" />
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>
          </Animated.View>
        </Animated.View>
      </View>
        </View>
  );
}

const styles = StyleSheet.create({
  container: { 
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  background: {
    flex: 1,
  },
  gradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  overlayGradient: {
    flex: 1,
  },
  circle1: {
    position: 'absolute',
    top: -hp(20),
    right: -wp(20),
    width: wp(40),
    height: wp(40),
    borderRadius: wp(20),
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 0 },
    shadowRadius: 30,
    elevation: 10,
  },
  circle2: {
    position: 'absolute',
    bottom: -hp(15),
    left: -wp(15),
    width: wp(30),
    height: wp(30),
    borderRadius: wp(15),
    backgroundColor: 'rgba(240, 147, 251, 0.08)',
    shadowColor: '#f093fb',
    shadowOffset: { width: 0, height: 0 },
    shadowRadius: 25,
    elevation: 8,
  },
  circle3: {
    position: 'absolute',
    top: hp(40),
    right: -wp(8),
    width: wp(16),
    height: wp(16),
    borderRadius: wp(8),
    backgroundColor: 'rgba(118, 75, 162, 0.06)',
    shadowColor: '#764ba2',
    shadowOffset: { width: 0, height: 0 },
    shadowRadius: 20,
    elevation: 6,
  },
  backWrapper: {
    position: 'absolute',
    top: Platform.select({
      ios: hp(8),
      android: hp(6),
    }),
    left: wp(5),
    zIndex: 10,
  },
  backButton: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    overflow: 'hidden',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 8,
  },
  backButtonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(5),
    paddingVertical: hp(3),
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    maxWidth: wp(85),
    paddingTop: hp(10),
  },
  logoContainer: {
    width: wp(25),
    height: wp(25),
    borderRadius: wp(12.5),
    overflow: 'hidden',
    marginBottom: hp(4),
  },
  logoGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    alignItems: 'center',
    paddingHorizontal: wp(5),
    marginBottom: hp(6),
  },
  title: {
    fontSize: hp(4.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: hp(2),
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 3 },
    textShadowRadius: 8,
    letterSpacing: 0.5,
  },
  subtitle: {
    fontSize: hp(2),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: hp(2.5),
  },
  cardsContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: hp(4),
  },
  roleCard: {
    width: wp(80),
    height: hp(16),
    borderRadius: wp(4),
    overflow: 'hidden',
    marginBottom: hp(3),
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 10,
  },
  cardGradient: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(4),
    paddingVertical: hp(1.5),
  },
  imageContainer: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    marginRight: wp(3),
    overflow: 'hidden',
  },
  imageBackground: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: wp(6),
  },
  roleImage: {
    width: '80%',
    height: '80%',
    borderRadius: wp(6),
  },
  textContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  cardIconContainer: {
    width: wp(8),
    height: wp(8),
    justifyContent: 'center',
    alignItems: 'center',
  },
  roleTitle: {
    fontSize: hp(3),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(0.5),
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 0.5,
  },
  roleDescription: {
    fontSize: hp(1.8),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: hp(2.2),
  },
  particle1: {
    position: 'absolute',
    width: wp(5),
    height: wp(5),
    borderRadius: wp(2.5),
    left: wp(10),
    top: hp(20),
  },
  particle2: {
    position: 'absolute',
    width: wp(4),
    height: wp(4),
    borderRadius: wp(2),
    left: wp(80),
    top: hp(30),
  },
  particle3: {
    position: 'absolute',
    width: wp(3),
    height: wp(3),
    borderRadius: wp(1.5),
    left: wp(20),
    top: hp(70),
  },
  particleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: wp(2.5),
  },
  floatingShape: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
  },
  glowCircle: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 20,
    elevation: 10,
  },
  trainerCard: {
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
  trainerCardBg: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(4),
    paddingVertical: hp(1.5),
    borderRadius: wp(4),
    width: '100%',
    height: '100%',
  },
  trainerTitle: {
    fontSize: hp(2.5),
    fontFamily: 'Poppins_700Bold',
    color: '#333',
    marginBottom: hp(0.5),
    letterSpacing: 0.5,
  },
  trainerDescription: {
    fontSize: hp(1.6),
    fontFamily: 'Poppins_400Regular',
    color: '#666',
    lineHeight: hp(2),
    maxWidth: wp(45),
  },
  userCard: {
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
  userCardBg: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(4),
    paddingVertical: hp(1.5),
    borderRadius: wp(4),
    width: '100%',
    height: '100%',
  },
  userTitle: {
    fontSize: hp(2.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(0.5),
    letterSpacing: 0.5,
  },
  userDescription: {
    fontSize: hp(1.6),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.9)',
    lineHeight: hp(2),
    maxWidth: wp(45),
  },
});