import AsyncStorage from '@react-native-async-storage/async-storage';

// User-specific functions
export const saveUserData = async (data) => {
  try {
    const existingData = await AsyncStorage.getItem('userHistory');
    const history = existingData ? JSON.parse(existingData) : [];
    history.push(data);
    await AsyncStorage.setItem('userHistory', JSON.stringify(history));
  } catch (error) {
    console.error('Error saving data to AsyncStorage:', error);
  }
};

export const getUserHistory = async () => {
  try {
    const data = await AsyncStorage.getItem('userHistory');
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error('Error retrieving data from AsyncStorage:', error);
    return [];
  }
};

// Main Storage utility
const Storage = {
  async getItem(key) {
    try {
      const value = await AsyncStorage.getItem(key);
      return value != null ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Error getting data', error);
      return null;
    }
  },

  async setItem(key, value) {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Error saving data', error);
    }
  },

  async removeItem(key) {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing data', error);
    }
  },

  // Include the user-specific functions as methods
  saveUserData,
  getUserHistory
};

export default Storage;