import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  Platform,
  SafeAreaView,
  ActivityIndicator,
  FlatList,
  Animated,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { FadeInUp } from 'react-native-reanimated';
import { useColorScheme } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { StatusBar } from 'expo-status-bar';
import { LineChart } from 'react-native-chart-kit';

const { width, height } = Dimensions.get('window');

// Advanced Particle component
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animSpeed = 4000 * speed + Math.random() * 2000;
    const animDelay = Math.random() * 3000 + delay;
      const animation = Animated.loop(
        Animated.sequence([
        Animated.timing(particleAnim, { toValue: 1, duration: animSpeed, useNativeDriver: true }),
        Animated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = Animated.sequence([
      Animated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 1, duration: animSpeed - 1000, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);
    
    const timeoutId = setTimeout(() => {
      animation.start();
      Animated.loop(opacitySequence).start();
    }, animDelay);

      return () => {
      clearTimeout(timeoutId);
        animation.stop();
      };
  }, [delay, speed]);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <Animated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

const FloatingShape = ({ style, delay = 0, rotationSpeed = 1 }) => {
  const floatAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
      const floatAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(floatAnim, {
            toValue: 1,
            duration: 3000 + Math.random() * 2000,
            useNativeDriver: true,
          }),
          Animated.timing(floatAnim, {
            toValue: 0,
            duration: 3000 + Math.random() * 2000,
            useNativeDriver: true,
          }),
        ])
      );
      
      const rotateAnimation = Animated.loop(
        Animated.timing(rotationAnim, {
          toValue: 1,
          duration: 15000 * rotationSpeed,
          useNativeDriver: true,
        })
      );
      
    const timeoutId = setTimeout(() => {
      floatAnimation.start();
      rotateAnimation.start();
    }, delay);
      
      return () => {
      clearTimeout(timeoutId);
        floatAnimation.stop();
        rotateAnimation.stop();
    };
  }, [delay, rotationSpeed]);

  const translateY = floatAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });

  const rotate = rotationAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{ translateY }, { rotate }],
        },
      ]}
    >
      <LinearGradient
        colors={['rgba(102, 126, 234, 0.3)', 'rgba(118, 75, 162, 0.2)']}
        style={styles.floatingShapeGradient}
      />
    </Animated.View>
  );
};

// Utility function to sanitize chart data
function sanitizeChartData(data) {
  return Array.isArray(data) ? data.map(d => (isFinite(d) && !isNaN(d) ? d : 0)) : [];
}

export default function UserAnalytics() {
  // Always call useFonts first to maintain hook order
  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  const router = useRouter();
  const [analytics, setAnalytics] = useState({
    userGrowth: {
      totalUsers: 0,
      newUsersToday: 0,
      newUsersThisWeek: 0,
      newUsersThisMonth: 0,
      growthRate: 0
    },
    userActivity: {
      dailyActiveUsers: 0,
      weeklyActiveUsers: 0,
      monthlyActiveUsers: 0,
      averageSessionTime: 0,
      retentionRate: 0
    },
    demographics: {
      genderDistribution: { male: 0, female: 0, other: 0 },
      ageGroups: { '18-25': 0, '26-35': 0, '36-45': 0, '46+': 0 },
      fitnessLevels: { beginner: 0, intermediate: 0, advanced: 0 },
      activityLevels: { sedentary: 0, light: 0, moderate: 0, active: 0, veryActive: 0 }
    },
    engagement: {
      profileCompletionRate: 0,
      planEnrollmentRate: 0,
      averagePlansPerUser: 0,
      mostPopularTargetAreas: []
    },
    topUsers: []
  });
  const [timeRange, setTimeRange] = useState('week'); // week, month, year
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showMetrics, setShowMetrics] = useState({
    totalUsers: true,
    newUsersToday: true,
    newUsersThisWeek: true,
    newUsersThisMonth: true,
    growthRate: true,
    dailyActiveUsers: true,
    weeklyActiveUsers: true,
    monthlyActiveUsers: true,
    retentionRate: true,
  });
  const [metricOrder, setMetricOrder] = useState([
    'totalUsers',
    'newUsersToday',
    'newUsersThisWeek',
    'newUsersThisMonth',
    'growthRate',
    'dailyActiveUsers',
    'weeklyActiveUsers',
    'monthlyActiveUsers',
    'retentionRate',
  ]);
  const [darkMode, setDarkMode] = useState(false);
  const motivationalQuotes = [
    "Empower your users with insights!",
    "Every metric tells a story!",
    "Data-driven decisions win!",
    "Analytics is the admin's compass!",
    "Great leaders measure progress!",
    "Your dashboard, your rules!",
    "Visualize, analyze, optimize!",
    "Numbers never lie!"
  ];
  const todayQuote = motivationalQuotes[new Date().getDate() % motivationalQuotes.length];
  const adminName = 'Admin'; // Replace with dynamic admin name if available

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      const [usersData, historyData] = await Promise.all([
        AsyncStorage.getItem('@FitnessApp:users'),
        AsyncStorage.getItem('userHistory')
      ]);

      let users = [];
      let history = [];

      try { users = usersData ? JSON.parse(usersData) : []; } catch (e) { console.error('Error parsing users:', e); }
      try { history = historyData ? JSON.parse(historyData) : []; } catch (e) { console.error('Error parsing history:', e); }

      // Calculate time periods
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      // User Growth Analytics
      const totalUsers = users.length;
      const newUsersToday = users.filter(user => {
        const signupTime = user.signupTime ? new Date(user.signupTime) : null;
        return signupTime && signupTime > oneDayAgo;
      }).length;

      const newUsersThisWeek = users.filter(user => {
        const signupTime = user.signupTime ? new Date(user.signupTime) : null;
        return signupTime && signupTime > oneWeekAgo;
      }).length;

      const newUsersThisMonth = users.filter(user => {
        const signupTime = user.signupTime ? new Date(user.signupTime) : null;
        return signupTime && signupTime > oneMonthAgo;
      }).length;

      // Calculate growth rate
      const previousMonthUsers = users.filter(user => {
        const signupTime = user.signupTime ? new Date(user.signupTime) : null;
        const twoMonthsAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);
        return signupTime && signupTime > twoMonthsAgo && signupTime <= oneMonthAgo;
      }).length;

      const growthRate = previousMonthUsers > 0 ? 
        ((newUsersThisMonth - previousMonthUsers) / previousMonthUsers) * 100 : 0;

      // User Activity Analytics
      const dailyActiveUsers = users.filter(user => {
        const lastLogin = user.lastLogin ? new Date(user.lastLogin) : null;
        return lastLogin && lastLogin > oneDayAgo;
      }).length;

      const weeklyActiveUsers = users.filter(user => {
        const lastLogin = user.lastLogin ? new Date(user.lastLogin) : null;
        return lastLogin && lastLogin > oneWeekAgo;
      }).length;

      const monthlyActiveUsers = users.filter(user => {
        const lastLogin = user.lastLogin ? new Date(user.lastLogin) : null;
        return lastLogin && lastLogin > oneMonthAgo;
      }).length;

      // Demographics Analytics
      const genderDistribution = users.reduce((acc, user) => {
        try {
          const gender = user.gender || 'other';
          acc[gender] = (acc[gender] || 0) + 1;
        } catch (error) {
          console.error('Error processing user gender:', error);
        }
        return acc;
      }, { male: 0, female: 0, other: 0 });

      const fitnessLevels = users.reduce((acc, user) => {
        try {
          const level = user.fitnessLevel || 'beginner';
          acc[level] = (acc[level] || 0) + 1;
        } catch (error) {
          console.error('Error processing user fitness level:', error);
        }
        return acc;
      }, { beginner: 0, intermediate: 0, advanced: 0 });

      const activityLevels = users.reduce((acc, user) => {
        try {
          const level = user.activityLevel || 'sedentary';
          acc[level] = (acc[level] || 0) + 1;
        } catch (error) {
          console.error('Error processing user activity level:', error);
        }
        return acc;
      }, { sedentary: 0, light: 0, moderate: 0, active: 0, veryActive: 0 });

      // Engagement Analytics
      const completedProfiles = users.filter(user => {
        try {
          return user.isProfileComplete;
        } catch (error) {
          console.error('Error checking profile completion:', error);
          return false;
        }
      }).length;
      const profileCompletionRate = totalUsers > 0 ? (completedProfiles / totalUsers) * 100 : 0;

      const usersWithPlans = new Set(history.map(h => {
        try {
          return h.userEmail || h.email;
        } catch (error) {
          console.error('Error processing history entry:', error);
          return null;
        }
      }).filter(Boolean)).size;
      const planEnrollmentRate = totalUsers > 0 ? (usersWithPlans / totalUsers) * 100 : 0;

      const averagePlansPerUser = totalUsers > 0 ? history.length / totalUsers : 0;

      // Most popular target areas
      const targetAreasCount = users.reduce((acc, user) => {
        try {
          if (user.targetAreas && Array.isArray(user.targetAreas)) {
            user.targetAreas.forEach(area => {
              acc[area] = (acc[area] || 0) + 1;
            });
          }
        } catch (error) {
          console.error('Error processing target areas:', error);
        }
        return acc;
      }, {});

      const mostPopularTargetAreas = Object.entries(targetAreasCount)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([area, count]) => ({ area, count }));

      // Top Users (by plan completions)
      const userPlanCounts = history.reduce((acc, h) => {
        try {
          const userEmail = h.userEmail || h.email;
          if (userEmail) {
            if (!acc[userEmail]) {
              acc[userEmail] = { email: userEmail, completions: 0, inProgress: 0 };
            }
            if (h.status === 'completed') {
              acc[userEmail].completions++;
            } else if (h.status === 'active' || h.status === 'in_progress') {
              acc[userEmail].inProgress++;
            }
          }
        } catch (error) {
          console.error('Error processing user plan count:', error);
        }
        return acc;
      }, {});

      const topUsers = Object.values(userPlanCounts)
        .sort((a, b) => b.completions - a.completions)
        .slice(0, 10)
        .map(userStat => {
          try {
            const user = users.find(u => u.email === userStat.email);
            return {
              ...userStat,
              name: user?.name || 'Unknown User',
              joinDate: user?.signupTime
            };
          } catch (error) {
            console.error('Error processing top user:', error);
            return {
              ...userStat,
              name: 'Unknown User',
              joinDate: null
            };
          }
        });

      // Retention rate (users who logged in within last week)
      const retentionRate = totalUsers > 0 ? (weeklyActiveUsers / totalUsers) * 100 : 0;

      setAnalytics({
        userGrowth: {
          totalUsers,
          newUsersToday,
          newUsersThisWeek,
          newUsersThisMonth,
          growthRate
        },
        userActivity: {
          dailyActiveUsers,
          weeklyActiveUsers,
          monthlyActiveUsers,
          averageSessionTime: 0,
          retentionRate
        },
        demographics: {
          genderDistribution: genderDistribution || { male: 0, female: 0, other: 0 },
          ageGroups: { '18-25': 0, '26-35': 0, '36-45': 0, '46+': 0 }, // Would need age data
          fitnessLevels: fitnessLevels || { beginner: 0, intermediate: 0, advanced: 0 },
          activityLevels: activityLevels || { sedentary: 0, light: 0, moderate: 0, active: 0, veryActive: 0 }
        },
        engagement: {
          profileCompletionRate,
          planEnrollmentRate,
          averagePlansPerUser,
          mostPopularTargetAreas: mostPopularTargetAreas || []
        },
        topUsers: topUsers || []
      });

    } catch (error) {
      console.error('Error fetching analytics:', error);
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  const renderMetricCard = (title, value, subtitle, icon, color, trend = null) => (
    <View style={[styles.metricCard, { borderLeftColor: color }]}>
      <View style={styles.metricHeader}>
        <Ionicons name={icon} size={24} color={color} />
        <Text style={styles.metricValue}>{value || 0}</Text>
      </View>
      <Text style={styles.metricTitle}>{title}</Text>
      {subtitle ? <Text style={styles.metricSubtitle}>{subtitle}</Text> : null}
      {trend && typeof trend === 'number' && !isNaN(trend) && (
        <View style={styles.trendContainer}>
          <Ionicons 
            name={trend > 0 ? "trending-up" : "trending-down"} 
            size={16} 
            color={trend > 0 ? "#4ECDC4" : "#FF6B6B"} 
          />
          <Text style={[styles.trendText, { color: trend > 0 ? "#4ECDC4" : "#FF6B6B" }]}>
            {Math.abs(trend).toFixed(1)}%
          </Text>
        </View>
      )}
    </View>
  );

  const renderDemographicChart = (title, data, total) => (
    <View style={styles.chartContainer}>
      <Text style={styles.chartTitle}>{title}</Text>
      {Object.entries(data || {}).map(([key, value]) => {
        const percentage = total > 0 ? (value / total) * 100 : 0;
        return (
          <View key={key} style={styles.chartItem}>
            <Text style={styles.chartLabel}>{key.charAt(0).toUpperCase() + key.slice(1)}</Text>
            <View style={styles.chartBarContainer}>
              <View style={styles.chartBar}>
                <View 
                  style={[
                    styles.chartBarFill,
                    { width: `${Math.max(0, Math.min(100, percentage))}%` }
                  ]} 
                />
              </View>
              <Text style={styles.chartPercentage}>{percentage.toFixed(1)}%</Text>
            </View>
            <Text style={styles.chartValue}>{value || 0}</Text>
          </View>
        );
      })}
    </View>
  );

  const renderTopUser = ({ item, index }) => (
    <View style={styles.topUserCard}>
      <View style={styles.topUserRank}>
        <Text style={styles.rankNumber}>#{index + 1}</Text>
      </View>
      <View style={styles.topUserInfo}>
        <Text style={styles.topUserName}>{item?.name || 'Unknown User'}</Text>
        <Text style={styles.topUserEmail}>{item?.email || 'No email'}</Text>
        <Text style={styles.topUserJoinDate}>
          Joined: {item?.joinDate ? new Date(item.joinDate).toLocaleDateString() : 'Unknown'}
        </Text>
      </View>
      <View style={styles.topUserStats}>
        <Text style={styles.topUserCompletions}>{item?.completions || 0}</Text>
        <Text style={styles.topUserLabel}>Completed</Text>
        <Text style={styles.topUserInProgress}>{item?.inProgress || 0}</Text>
        <Text style={styles.topUserLabel}>In Progress</Text>
      </View>
    </View>
  );

  if (!fontsLoaded) {
    return null;
  }

  if (loading) {
    return (
      <View style={styles.outerContainer}>
        <LinearGradient
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
          style={StyleSheet.absoluteFill}
        />
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>User Analytics</Text>
          </View>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#667eea" />
            <Text style={styles.loadingText}>Loading analytics...</Text>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.outerContainer}>
        <LinearGradient
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
          style={StyleSheet.absoluteFill}
        />
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>User Analytics</Text>
          </View>
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={64} color="#FF6B6B" />
            <Text style={styles.errorTitle}>Error Loading Data</Text>
            <Text style={styles.errorMessage}>
              {error.message || 'Failed to load analytics data. Please try again.'}
            </Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={fetchAnalytics}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  return (
    <React.Fragment>
    <View style={styles.outerContainer}>
      <LinearGradient
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
        style={StyleSheet.absoluteFill}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      {/* Animated Particles and Floating Shapes */}
      <AdvancedParticle style={styles.particle1} delay={0} speed={1.2} />
      <AdvancedParticle style={styles.particle2} delay={500} speed={0.8} />
      <AdvancedParticle style={styles.particle3} delay={1000} speed={1.5} />
      <FloatingShape style={styles.floatingShape1} delay={0} rotationSpeed={0.5} />
      <FloatingShape style={styles.floatingShape2} delay={1000} rotationSpeed={1.2} />
      <View style={[styles.glowCircle, { top: 60, right: 30 }]} />
      <View style={[styles.glowCircle, { bottom: 80, left: 20 }]} />
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>User Analytics</Text>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={fetchAnalytics}
          >
            <Ionicons name="refresh" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
        {/* Time Range Selector */}
        <View style={styles.timeRangeContainer}>
          {['week', 'month', 'year'].map((range) => (
            <TouchableOpacity
              key={range}
              style={[
                styles.timeRangeButton,
                timeRange === range && styles.activeTimeRangeButton
              ]}
              onPress={() => setTimeRange(range)}
            >
              <Text style={[
                styles.timeRangeText,
                timeRange === range && styles.activeTimeRangeText
              ]}>
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={{ position: 'relative', width: '100%' }}>
            {/* Animated Background Elements */}
            <View style={{ position: 'absolute', top: -60, left: -60, width: 120, height: 120, borderRadius: 60, backgroundColor: darkMode ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)' }} />
            <View style={{ position: 'absolute', bottom: -40, right: -40, width: 80, height: 80, borderRadius: 40, backgroundColor: darkMode ? 'rgba(255,255,255,0.07)' : 'rgba(0,0,0,0.07)' }} />
            <View style={{ position: 'absolute', top: '30%', right: -20, width: 40, height: 40, borderRadius: 20, backgroundColor: darkMode ? 'rgba(255,255,255,0.04)' : 'rgba(0,0,0,0.04)' }} />
            {/* Greeting & Quote */}
            <Text style={{ fontSize: 20, fontWeight: '700', color: darkMode ? '#fff' : '#222', marginTop: 12, marginBottom: 2, textAlign: 'center' }}>
              {`Welcome, ${adminName}!`}
            </Text>
            <Text style={{ fontSize: 14, color: darkMode ? '#fff' : '#444', marginBottom: 8, textAlign: 'center', fontStyle: 'italic' }}>
              {todayQuote}
            </Text>
            {/* Dark/Light Mode Toggle */}
            <TouchableOpacity onPress={() => setDarkMode((d) => !d)} style={{ alignSelf: 'center', marginBottom: 8 }}>
              <Ionicons name={darkMode ? 'moon' : 'sunny'} size={22} color={darkMode ? '#fff' : '#222'} />
            </TouchableOpacity>
            {/* Dashboard Customization: Show/Hide Metric Cards */}
            <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 8, flexWrap: 'wrap' }}>
              {Object.keys(showMetrics).map((metric) => (
                <TouchableOpacity
                  key={`metric-toggle-${metric}`}
                  onPress={() => setShowMetrics((prev) => ({ ...prev, [metric]: !prev[metric] }))}
                  style={{ margin: 4, padding: 4, borderRadius: 6, backgroundColor: showMetrics[metric] ? (darkMode ? '#333' : '#eee') : 'transparent' }}
                >
                  <Text style={{ color: showMetrics[metric] ? (darkMode ? '#fff' : '#222') : (darkMode ? '#aaa' : '#888'), fontSize: 12 }}>
                    {metric.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          {/* User Growth Metrics */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>User Growth</Text>
            <View style={styles.metricsGrid}>
              {metricOrder.filter(metric => showMetrics[metric]).map((item, index) => (
                <Animated.View key={`growth-${item}-${index}`} entering={FadeInUp} style={{ marginBottom: 12 }}>
                  <TouchableOpacity onLongPress={() => setMetricOrder(metricOrder.filter(m => m !== item))} activeOpacity={0.8}>
                    {renderMetricCard(
                      item.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
                      sanitizeChartData(analytics.userGrowth[item]) || sanitizeChartData(analytics.userActivity[item]) || sanitizeChartData(analytics[item]) || 0,
                      '', // subtitle
                      item === 'totalUsers' ? 'people' :
                      item === 'newUsersToday' ? 'person-add' :
                      item === 'newUsersThisWeek' ? 'calendar' :
                      item === 'newUsersThisMonth' ? 'calendar-outline' :
                      item === 'growthRate' ? 'trending-up' :
                      item === 'dailyActiveUsers' ? 'pulse' :
                      item === 'weeklyActiveUsers' ? 'pulse-outline' :
                      item === 'monthlyActiveUsers' ? 'calendar-sharp' :
                      item === 'retentionRate' ? 'refresh' : 'stats-chart',
                      item === 'totalUsers' ? '#667eea' :
                      item === 'newUsersToday' ? '#4ECDC4' :
                      item === 'newUsersThisWeek' ? '#FF6B6B' :
                      item === 'newUsersThisMonth' ? '#FFD166' :
                      item === 'growthRate' ? '#45B7D1' :
                      item === 'dailyActiveUsers' ? '#FF8E53' :
                      item === 'weeklyActiveUsers' ? '#888' :
                      item === 'monthlyActiveUsers' ? '#2196F3' :
                      item === 'retentionRate' ? '#4CAF50' : '#888',
                      null // trend
                    )}
                  </TouchableOpacity>
                </Animated.View>
              ))}
            </View>
          </View>
          {/* User Activity */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>User Activity</Text>
            <View style={styles.metricsGrid}>
              {metricOrder.filter(metric => showMetrics[metric]).map((item, index) => (
                <Animated.View key={`activity-${item}-${index}`} entering={FadeInUp} style={{ marginBottom: 12 }}>
                  <TouchableOpacity onLongPress={() => setMetricOrder(metricOrder.filter(m => m !== item))} activeOpacity={0.8}>
                    {renderMetricCard(
                      item.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
                      sanitizeChartData(analytics.userActivity[item]) || sanitizeChartData(analytics[item]) || 0,
                      '', // subtitle
                      item === 'dailyActiveUsers' ? 'pulse' :
                      item === 'weeklyActiveUsers' ? 'pulse-outline' :
                      item === 'monthlyActiveUsers' ? 'calendar-sharp' :
                      item === 'retentionRate' ? 'refresh' : 'stats-chart',
                      item === 'dailyActiveUsers' ? '#FF8E53' :
                      item === 'weeklyActiveUsers' ? '#888' :
                      item === 'monthlyActiveUsers' ? '#2196F3' :
                      item === 'retentionRate' ? '#4CAF50' : '#888',
                      null // trend
                    )}
                  </TouchableOpacity>
                </Animated.View>
              ))}
            </View>
          </View>
          {/* Demographics */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Demographics</Text>
            <View style={styles.demographicsContainer}>
              {metricOrder.filter(metric => showMetrics[metric]).map((item, index) => (
                <Animated.View key={`demographics-${item}-${index}`} entering={FadeInUp} style={{ marginBottom: 12 }}>
                  <TouchableOpacity onLongPress={() => setMetricOrder(metricOrder.filter(m => m !== item))} activeOpacity={0.8}>
                    {renderDemographicChart(
                      item.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
                      analytics.demographics[item],
                      analytics.userGrowth.totalUsers
                    )}
                  </TouchableOpacity>
                </Animated.View>
              ))}
            </View>
          </View>
          {/* Engagement Metrics */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>User Engagement</Text>
            <View style={styles.engagementContainer}>
              {metricOrder.filter(metric => showMetrics[metric]).map((item, index) => (
                <Animated.View key={`engagement-${item}-${index}`} entering={FadeInUp} style={{ marginBottom: 12 }}>
                  <TouchableOpacity onLongPress={() => setMetricOrder(metricOrder.filter(m => m !== item))} activeOpacity={0.8}>
                    {renderMetricCard(
                      item.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
                      sanitizeChartData(analytics.engagement[item]) || 0,
                      '', // subtitle
                      item === 'profileCompletionRate' ? 'checkmark-circle' :
                      item === 'planEnrollmentRate' ? 'fitness' :
                      item === 'averagePlansPerUser' ? 'list' : 'stats-chart',
                      item === 'profileCompletionRate' ? '#4ECDC4' :
                      item === 'planEnrollmentRate' ? '#45B7D1' :
                      item === 'averagePlansPerUser' ? '#96CEB4' : '#888',
                      null // trend
                    )}
                  </TouchableOpacity>
                </Animated.View>
              ))}
            </View>
          </View>
          {/* Popular Target Areas */}
          {analytics.engagement.mostPopularTargetAreas.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Popular Target Areas</Text>
              <View style={styles.targetAreasContainer}>
                {analytics.engagement.mostPopularTargetAreas.map((item, index) => (
                  <View key={item.area} style={styles.targetAreaItem}>
                    <Text style={styles.targetAreaRank}>#{index + 1}</Text>
                    <Text style={styles.targetAreaName}>{item.area}</Text>
                    <Text style={styles.targetAreaCount}>{item.count} users</Text>
                  </View>
                ))}
              </View>
            </View>
          )}
          {/* Top Users */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Top Users</Text>
            <FlatList
              data={analytics.topUsers}
              renderItem={renderTopUser}
              keyExtractor={(item) => item.email}
              scrollEnabled={false}
            />
          </View>
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </SafeAreaView>
    </View>
    </React.Fragment>
  );
}

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  safeArea: {
    flex: 1,
    paddingTop: 40,
  },
  particle1: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderRadius: 12,
    left: width * 0.1,
    top: 120,
    zIndex: 0,
  },
  particle2: {
    position: 'absolute',
    width: 18,
    height: 18,
    borderRadius: 9,
    left: width * 0.8,
    top: 200,
    zIndex: 0,
  },
  particle3: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    left: width * 0.2,
    top: 500,
    zIndex: 0,
  },
  particleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  floatingShape1: {
    position: 'absolute',
    width: 32,
    height: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    left: width * 0.85,
    top: 100,
    zIndex: 0,
  },
  floatingShape2: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    left: width * 0.05,
    top: 600,
    zIndex: 0,
  },
  glowCircle: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 20,
    elevation: 10,
    zIndex: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  refreshButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  timeRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  timeRangeButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginHorizontal: 5,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  activeTimeRangeButton: {
    backgroundColor: '#fff',
  },
  timeRangeText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  activeTimeRangeText: {
    color: '#1E90FF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 15,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 15,
    width: (width - 60) / 2,
    marginBottom: 15,
    borderLeftWidth: 4,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  metricValue: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  metricTitle: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: 4,
  },
  metricSubtitle: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.5)',
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  trendText: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    marginLeft: 4,
  },
  demographicsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  chartContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 15,
    width: '100%',
    marginBottom: 15,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
  },
  chartTitle: {
    fontSize: 14,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 15,
    textAlign: 'center',
  },
  chartItem: {
    marginBottom: 10,
  },
  chartLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: 4,
  },
  chartBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  chartBar: {
    width: 150,
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  chartBarFill: {
    height: '100%',
    backgroundColor: '#4ECDC4',
    borderRadius: 4,
  },
  chartPercentage: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.9)',
    marginLeft: 10,
  },
  chartValue: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    marginLeft: 'auto',
  },
  engagementContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  engagementCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 15,
    width: (width - 80) / 3,
    alignItems: 'center',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
  },
  engagementValue: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 5,
  },
  engagementLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    marginBottom: 8,
  },
  targetAreasContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 15,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
  },
  targetAreaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  targetAreaRank: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginRight: 15,
  },
  targetAreaName: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.9)',
  },
  targetAreaCount: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.6)',
    marginLeft: 'auto',
  },
  topUserCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
  },
  topUserRank: {
    marginRight: 15,
    backgroundColor: '#4ECDC4',
    width: 30,
    height: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rankNumber: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  topUserInfo: {
    flex: 1,
  },
  topUserName: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 4,
  },
  topUserEmail: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  topUserJoinDate: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.5)',
    marginTop: 4,
  },
  topUserStats: {
    alignItems: 'flex-end',
  },
  topUserCompletions: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#4ECDC4',
  },
  topUserInProgress: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#FECA57',
    marginTop: 4,
  },
  topUserLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  bottomSpacing: {
    height: 50,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginTop: 10,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: 5,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#667eea',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
});