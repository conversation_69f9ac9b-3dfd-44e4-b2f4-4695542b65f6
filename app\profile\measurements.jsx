import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  SafeAreaView, 
  TouchableOpacity,
  ScrollView,
  Platform,
  TextInput,
  Alert
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import {
  useFonts,
  Poppins_700Bold,
  Poppins_400Regular,
  Poppins_600SemiBold,
} from '@expo-google-fonts/poppins';
import { useUser } from '../../context/UserContext';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function Measurements() {
  const router = useRouter();
  const { 
    height, 
    setHeight, 
    weight, 
    setWeight, 
    targetWeight, 
    setTargetWeight,
    targetHeight,
    setTargetHeight,
    currentUser,
    updateCurrentUser,
    setUserHistory
  } = useUser();
  
  const userEmail = currentUser?.email;
  
  let [fontsLoaded] = useFonts({
    Poppins_700Bold,
    Poppins_400Regular,
    Poppins_600SemiBold,
  });

  // Add Age field
  const [age, setAge] = React.useState('');

  const handleBack = () => {
    router.back();
  };

  const handleNext = async () => {
    if (!height || !weight || !age) {
      Alert.alert('Missing Information', 'Please enter your height, age, and weight to continue.');
      return;
    }
    try {
      if (!userEmail) {
        Alert.alert('Error', 'User email not found. Please log in again.');
        return;
      }
      
      // Save to user-specific profile
      const profileKey = `@user_profile:${userEmail}`;
      const profileData = await AsyncStorage.getItem(profileKey);
      const profile = profileData ? JSON.parse(profileData) : {};
      profile.measurements = { height, targetHeight, weight, targetWeight, age };
      await AsyncStorage.setItem(profileKey, JSON.stringify(profile));
      
      // Also save to user data
      const userDataKey = `@user_data:${userEmail}`;
      const userDataString = await AsyncStorage.getItem(userDataKey);
      const userData = userDataString ? JSON.parse(userDataString) : {};
      userData.height = height;
      userData.weight = weight;
      userData.targetWeight = targetWeight || weight;
      userData.targetHeight = targetHeight || height;
      userData.age = age;
      await AsyncStorage.setItem(userDataKey, JSON.stringify(userData));
      
      // Create history entry
      const entry = {
        action: 'Measurements Updated',
        userEmail: userEmail,
        userName: currentUser?.name,
        timestamp: new Date().toISOString(),
        details: `Height: ${height}, Weight: ${weight}, Age: ${age}`
      };
      
      // Save to user-specific history
      const userHistoryKey = `@userHistory:${userEmail}`;
      const existingHistory = await AsyncStorage.getItem(userHistoryKey);
      const history = existingHistory ? JSON.parse(existingHistory) : [];
      await AsyncStorage.setItem(userHistoryKey, JSON.stringify([...history, entry]));
      
      // Update user context
      await updateCurrentUser({ 
        height, 
        weight, 
        targetWeight: targetWeight || weight, 
        targetHeight: targetHeight || height, 
        age 
      });
      
      router.push('/profile/gender');
    } catch (error) {
      console.error('Error saving measurements:', error);
      Alert.alert('Error', 'Failed to save your measurements.');
    }
  };

  // Load measurements for current user only
  React.useEffect(() => {
    if (!userEmail) return;
    (async () => {
      try {
        // Always start with empty state for new users
        if (currentUser && currentUser.isNewUser) {
          setHeight('');
          setTargetHeight('');
          setWeight('');
          setTargetWeight('');
          setAge('');
          return;
        }
        // First try to get from user-specific data
        const userDataKey = `@user_data:${userEmail}`;
        const userDataString = await AsyncStorage.getItem(userDataKey);
        if (userDataString) {
          const userData = JSON.parse(userDataString);
          if (userData.height || userData.weight || userData.targetWeight || userData.targetHeight || userData.age) {
            setHeight(userData.height || '');
            setTargetHeight(userData.targetHeight || '');
            setWeight(userData.weight || '');
            setTargetWeight(userData.targetWeight || '');
            setAge(userData.age || '');
            return;
          }
        }
        // Then try to get from user-specific profile
        const profileKey = `@user_profile:${userEmail}`;
        const profileData = await AsyncStorage.getItem(profileKey);
        if (profileData) {
          const profile = JSON.parse(profileData);
          if (profile.measurements) {
            setHeight(profile.measurements.height || '');
            setTargetHeight(profile.measurements.targetHeight || '');
            setWeight(profile.measurements.weight || '');
            setTargetWeight(profile.measurements.targetWeight || '');
            setAge(profile.measurements.age || '');
            return;
          }
        }
        // Finally try to get from user context
        if (currentUser) {
          setHeight(currentUser.height || '');
          setTargetHeight(currentUser.targetHeight || '');
          setWeight(currentUser.weight || '');
          setTargetWeight(currentUser.targetWeight || '');
          setAge(currentUser.age || '');
          return;
        }
        // Default to empty
        setHeight('');
        setTargetHeight('');
        setWeight('');
        setTargetWeight('');
        setAge('');
      } catch (error) {
        console.error('Error loading measurements:', error);
        setHeight('');
        setTargetHeight('');
        setWeight('');
        setTargetWeight('');
        setAge('');
      }
    })();
  }, [userEmail, currentUser]);

  if (!fontsLoaded) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      <LinearGradient 
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} 
        style={styles.background}
      >
        {/* Back Button */}
        <TouchableOpacity 
          onPress={handleBack} 
          style={styles.backButton}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
            style={styles.backButtonGradient}
          >
            <Ionicons name="arrow-back" size={24} color="#ffffff" />
          </LinearGradient>
        </TouchableOpacity>

        <ScrollView style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Measurements</Text>
            <Text style={styles.subtitle}>Enter your body measurements</Text>
          </View>

          {/* Progress Indicator */}
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: '50%' }]} />
            </View>
            <Text style={styles.progressText}>Step 2 of 4</Text>
          </View>

          {/* Measurements Form */}
          <View style={styles.formContainer}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Height (cm)</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your height"
                placeholderTextColor="rgba(255, 255, 255, 0.5)"
                keyboardType="numeric"
                value={height}
                onChangeText={setHeight}
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Age</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your age"
                placeholderTextColor="rgba(255, 255, 255, 0.5)"
                keyboardType="numeric"
                value={age}
                onChangeText={setAge}
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Target Height (cm)</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your target height (optional)"
                placeholderTextColor="rgba(255, 255, 255, 0.5)"
                keyboardType="numeric"
                value={targetHeight}
                onChangeText={setTargetHeight}
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Current Weight (kg)</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your weight"
                placeholderTextColor="rgba(255, 255, 255, 0.5)"
                keyboardType="numeric"
                value={weight}
                onChangeText={setWeight}
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Target Weight (kg)</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your target weight"
                placeholderTextColor="rgba(255, 255, 255, 0.5)"
                keyboardType="numeric"
                value={targetWeight}
                onChangeText={setTargetWeight}
              />
            </View>
          </View>

          {/* Next Button */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              onPress={handleNext} 
              activeOpacity={0.8}
              style={styles.nextButton}
            >
              <LinearGradient 
                colors={['#667eea', '#764ba2']} 
                style={styles.buttonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={styles.buttonText}>
                  Continue
                </Text>
                <Ionicons 
                  name="arrow-forward" 
                  size={20} 
                  color="#fff" 
                  style={styles.buttonIcon}
                />
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  background: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'android' ? 40 : 20,
  },
  header: {
    marginTop: 60,
    marginBottom: 20,
  },
  title: {
    fontFamily: 'Poppins_700Bold',
    fontSize: 28,
    color: '#ffffff',
    marginBottom: 5,
  },
  subtitle: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  progressContainer: {
    marginBottom: 30,
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#667eea',
    borderRadius: 4,
  },
  progressText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  formContainer: {
    marginBottom: 40,
    padding: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 15,
    color: '#ffffff',
    fontFamily: 'Poppins_400Regular',
    fontSize: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  buttonContainer: {
    marginTop: 20,
    marginBottom: 30,
  },
  nextButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  buttonText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#ffffff',
    marginRight: 8,
  },
  buttonIcon: {
    marginLeft: 4,
  },
  backButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 30,
    left: 20,
    zIndex: 10,
  },
  backButtonGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});