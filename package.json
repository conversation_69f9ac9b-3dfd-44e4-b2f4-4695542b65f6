{"name": "my-fitness", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.3", "@react-native-async-storage/async-storage": "^1.23.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "expo": "~52.0.10", "expo-av": "~15.0.3", "expo-blur": "~14.0.4", "expo-constants": "~17.0.10", "expo-font": "~13.0.5", "expo-haptics": "~14.0.2", "expo-linking": "~7.0.6", "expo-router": "~4.0.21", "expo-sensors": "~14.0.2", "expo-splash-screen": "~0.29.25", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.10", "expo-web-browser": "~14.0.3", "lodash": "^4.17.21", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.10", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "react-native-youtube-iframe": "^2.4.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.10", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}