// Polyfills for React Native Firebase compatibility
// This file should be imported at the top of your app entry point

// Mock idb (IndexedDB) for React Native
const idbMock = {
  open: () => Promise.resolve({}),
  deleteDatabase: () => Promise.resolve(),
  databases: () => Promise.resolve([]),
};

// Mock indexeddb for React Native
const indexedDBMock = {
  open: () => ({ result: {} }),
  deleteDatabase: () => Promise.resolve(),
  databases: () => Promise.resolve([]),
};

// Mock localforage for React Native
const localforageMock = {
  getItem: () => Promise.resolve(null),
  setItem: () => Promise.resolve(),
  removeItem: () => Promise.resolve(),
  clear: () => Promise.resolve(),
  length: () => Promise.resolve(0),
  key: () => Promise.resolve(null),
  keys: () => Promise.resolve([]),
  iterate: () => Promise.resolve(),
};

// Set global variables
global.idb = idbMock;
global.indexedDB = indexedDBMock;
global.localforage = localforageMock;

// Export modules for Metro resolver
module.exports = idbMock;
module.exports.default = idbMock;

// Mock other browser APIs that Firebase might try to use
global.navigator = global.navigator || {};
global.navigator.serviceWorker = undefined;
global.navigator.onLine = true;

// Mock window.location for Firebase
global.window = global.window || {};
global.window.location = {
  href: 'http://localhost',
  origin: 'http://localhost',
  protocol: 'http:',
  host: 'localhost',
  hostname: 'localhost',
  port: '',
  pathname: '/',
  search: '',
  hash: '',
};

// Mock document for Firebase
global.document = {
  createElement: () => ({}),
  getElementsByTagName: () => [],
  addEventListener: () => {},
  removeEventListener: () => {},
};

// Mock XMLHttpRequest if not available
if (!global.XMLHttpRequest) {
  global.XMLHttpRequest = class XMLHttpRequest {
    constructor() {
      this.readyState = 0;
      this.status = 0;
      this.responseText = '';
      this.responseURL = '';
      this.responseType = '';
      this.response = null;
      this.statusText = '';
      this.timeout = 0;
      this.withCredentials = false;
    }
    
    open() {}
    send() {}
    setRequestHeader() {}
    getResponseHeader() { return null; }
    getAllResponseHeaders() { return ''; }
    abort() {}
  };
}

// Mock fetch if not available
if (!global.fetch) {
  global.fetch = () => Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
  });
}

// Mock crypto for Firebase
if (!global.crypto) {
  global.crypto = {
    getRandomValues: (array) => {
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
      return array;
    },
    subtle: {
      generateKey: () => Promise.resolve({}),
      sign: () => Promise.resolve(new ArrayBuffer(0)),
      verify: () => Promise.resolve(true),
      encrypt: () => Promise.resolve(new ArrayBuffer(0)),
      decrypt: () => Promise.resolve(new ArrayBuffer(0)),
    },
  };
}

console.log('✅ Firebase polyfills loaded successfully'); 