﻿import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

export default function HomepageScreen() {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All', icon: 'grid-outline' },
    { id: 'strength', name: 'Strength', icon: 'barbell-outline' },
    { id: 'cardio', name: 'Card<PERSON>', icon: 'heart-outline' },
    { id: 'flexibility', name: 'Flexibility', icon: 'body-outline' },
  ];

  const workouts = [
    {
      id: 1,
      title: 'Full Body Workout',
      duration: '45 min',
      difficulty: 'Intermediate',
      category: 'strength',
      image: 'https://via.placeholder.com/300x200',
    },
  ];

  return (
    <ThemedView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <ThemedText type="title" style={styles.welcomeText}>
            Welcome to Fitness
          </ThemedText>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
  },
  welcomeText: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
});
