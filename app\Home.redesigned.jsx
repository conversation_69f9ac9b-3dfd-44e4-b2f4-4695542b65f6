// This file is used in the project. Do NOT delete.
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StatusBar,
  Image,
  ScrollView,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Platform,
  Alert,
  Modal,
  ActivityIndicator,
  Animated,
  Animated as RNAnimated,
  TextInput,
  FlatList,
  Keyboard,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useRouter } from 'expo-router';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from '../utils/responsive';
// import {
//   useFonts,
//   Poppins_400Regular,
//   Poppins_700Bold,
//   Poppins_600SemiBold,
// } from '@expo-google-fonts/poppins';
// Temporarily disable Firebase imports
// import { getDatabase, ref, get } from 'firebase/database';
// import app from '../firebaseConfig';

// Mock Firebase functions
const getDatabase = () => ({});
const ref = () => ({});
const get = () => Promise.resolve({});
const app = {};
import { useUser } from '../context/UserContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { sliderImages, bodyParts, demoExercises } from '../constants/data';
import SafeImage from '../components/SafeImage';
import { getImageByCategory } from '../constants/remoteImages';

const { width, height } = Dimensions.get('window');

const AdvancedParticle = ({ style }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const speed = 4000 + Math.random() * 2000;
    const delay = Math.random() * 3000;

    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, { toValue: 1, duration: speed, useNativeDriver: true }),
        Animated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = Animated.sequence([
      Animated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 1, duration: speed - 1000, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);

    setTimeout(() => {
      animation.start();
      Animated.loop(opacitySequence).start();
    }, delay);

  }, []);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <Animated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

export default function Home() {
  const router = useRouter();
  const { logoutUser } = useUser ? useUser() : { logoutUser: null };
  
  // Tracker states
  const [trackerData, setTrackerData] = useState({
    steps: 0,
    calories: 0,
    water: 0, // in glasses
    sleep: 0, // in hours
  });
  
  const [dailyGoals] = useState({
    steps: 10000,
    calories: 2000,
    water: 8, // glasses
    sleep: 8, // hours
  });

  const [showProgressModal, setShowProgressModal] = useState(false);
  const [aiCoachActive, setAiCoachActive] = useState(false);
  const [formGuidanceActive, setFormGuidanceActive] = useState(false);
  const [activityRecognition, setActivityRecognition] = useState('idle');
  const [aiWorkoutSuggestion, setAiWorkoutSuggestion] = useState(null);
  const [loadingAI, setLoadingAI] = useState(false);
  const [showWorkoutTracker, setShowWorkoutTracker] = useState(false);
  const [currentWorkout, setCurrentWorkout] = useState(null);
  const [userEmail, setUserEmail] = useState(null);
  const [videoPlayerVisible, setVideoPlayerVisible] = useState(false);
  const [selectedExercise, setSelectedExercise] = useState(null);
  const [activeUsers, setActiveUsers] = useState(0);

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_700Bold,
    Poppins_600SemiBold,
  });

  // Load tracker data on component mount
  useEffect(() => {
    loadTrackerData();
    loadUserEmail();
    fetchActiveUsers();
  }, []);

  const loadUserEmail = async () => {
    try {
      const email = await AsyncStorage.getItem('@current_user');
      setUserEmail(email);
    } catch (error) {
      console.error('Error loading user email:', error);
    }
  };
  
  const fetchActiveUsers = async () => {
    try {
      // Get all users from AsyncStorage
      const usersData = await AsyncStorage.getItem('@users');
      if (usersData) {
        const users = JSON.parse(usersData);
        // Count users who logged in within the last 24 hours
        const now = new Date();
        const activeCount = users.filter(user => {
          if (!user.lastLogin) return false;
          const lastLogin = new Date(user.lastLogin);
          const hoursDiff = (now - lastLogin) / (1000 * 60 * 60);
          return hoursDiff < 24;
        }).length;
        
        setActiveUsers(activeCount);
      } else {
        setActiveUsers(0);
      }
    } catch (error) {
      console.error('Error fetching active users:', error);
      setActiveUsers(0);
    }
  };

  // AI activity recognition simulation
  useEffect(() => {
    if (aiCoachActive) {
      const interval = setInterval(() => {
        recognizeActivity();
      }, 3000); // Check activity every 3 seconds

      return () => clearInterval(interval);
    }
  }, [aiCoachActive]);

  const loadTrackerData = async () => {
    try {
      const today = new Date().toDateString();
      const savedData = await AsyncStorage.getItem(`tracker_${today}`);
      if (savedData) {
        setTrackerData(JSON.parse(savedData));
      }
    } catch (error) {
      console.error('Error loading tracker data:', error);
    }
  };

  const saveTrackerData = async (newData) => {
    try {
      const today = new Date().toDateString();
      await AsyncStorage.setItem(`tracker_${today}`, JSON.stringify(newData));
      setTrackerData(newData);
    } catch (error) {
      console.error('Error saving tracker data:', error);
    }
  };

  const updateTracker = (type, increment = true, customAmount = null) => {
    const newData = { ...trackerData };
    
    switch (type) {
      case 'steps':
        const stepAmount = customAmount || 100;
        newData.steps = increment ? newData.steps + stepAmount : Math.max(0, newData.steps - stepAmount);
        break;
      case 'calories':
        const calorieAmount = customAmount || 50;
        newData.calories = increment ? newData.calories + calorieAmount : Math.max(0, newData.calories - calorieAmount);
        break;
      case 'water':
        newData.water = increment ? newData.water + 1 : Math.max(0, newData.water - 1);
        break;
      case 'sleep':
        newData.sleep = increment ? Math.min(12, newData.sleep + 0.5) : Math.max(0, newData.sleep - 0.5);
        break;
    }
    
    saveTrackerData(newData);
  };

  const getProgressPercentage = (current, goal) => {
    return Math.min(100, (current / goal) * 100);
  };

  // AI-powered workout generation
  const generateWorkout = async () => {
    setLoadingAI(true);
    try {
      // Simulate AI API call - replace with actual API
      const workoutTypes = ['HIIT', 'Strength', 'Cardio', 'Yoga', 'Pilates'];
      const intensities = ['Beginner', 'Intermediate', 'Advanced'];
      const durations = [15, 20, 30, 45, 60];
      
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API delay
      
      const suggestion = {
        type: workoutTypes[Math.floor(Math.random() * workoutTypes.length)],
        intensity: intensities[Math.floor(Math.random() * intensities.length)],
        duration: durations[Math.floor(Math.random() * durations.length)],
        exercises: [
          'Push-ups', 'Squats', 'Lunges', 'Plank', 'Burpees'
        ].slice(0, 3 + Math.floor(Math.random() * 3)),
        aiTip: 'Focus on proper form over speed. Quality movements lead to better results!'
      };
      
      setAiWorkoutSuggestion(suggestion);
      Alert.alert(
        '🤖 AI Coach Recommendation',
        `I suggest a ${suggestion.duration}-minute ${suggestion.type} workout at ${suggestion.intensity} level.\n\nExercises: ${suggestion.exercises.join(', ')}\n\n💡 ${suggestion.aiTip}`,
        [
          { text: 'Maybe Later', style: 'cancel' },
          { text: 'Start Workout', onPress: () => startAIWorkout(suggestion) }
        ]
      );
    } catch (error) {
      console.error('AI workout generation error:', error);
      Alert.alert('AI Coach Offline', 'Unable to generate workout. Please try again later.');
    } finally {
      setLoadingAI(false);
    }
  };

  const startAIWorkout = (workout) => {
    setAiCoachActive(true);
    setFormGuidanceActive(true);
    Alert.alert(
      '🚀 AI Workout Started!',
      `Your ${workout.type} workout is now active. AI form guidance is enabled.\n\n📱 Features Active:\n• Real-time form correction\n• Activity recognition\n• Progress tracking\n• Voice guidance`,
      [{ text: 'Let\'s Go!', style: 'default' }]
    );
  };

  // CNN-based activity recognition simulation
  const recognizeActivity = () => {
    const activities = ['walking', 'running', 'cycling', 'jumping', 'squatting', 'idle'];
    const newActivity = activities[Math.floor(Math.random() * activities.length)];
    setActivityRecognition(newActivity);
    
    if (newActivity !== 'idle') {
      updateTracker('steps', true, Math.floor(Math.random() * 50) + 10);
      updateTracker('calories', true, Math.floor(Math.random() * 20) + 5);
    }
  };

  const getFormTips = (exercise) => {
    const tips = {
      'push-ups': 'Keep your body in a straight line, engage your core',
      'squats': 'Keep your knees behind your toes, chest up',
      'lunges': 'Step forward with control, keep your back straight',
      'plank': 'Hold your body straight, engage your abs',
      'burpees': 'Land softly, maintain good form throughout'
    };
    return tips[exercise.toLowerCase()] || 'Focus on proper form and breathing';
  };

  const getExerciseReps = (exercise) => {
    const reps = {
      'push-ups': '10-15 reps',
      'squats': '15-20 reps',
      'lunges': '10-12 reps each leg',
      'plank': '30-60 seconds',
      'burpees': '8-12 reps'
    };
    return reps[exercise.toLowerCase()] || '10-15 reps';
  };

  const getActivityFeedback = (activity) => {
    const feedback = {
      'walking': 'Great pace! Keep it up!',
      'running': 'Excellent form! You\'re doing great!',
      'cycling': 'Perfect cadence! Maintain this rhythm!',
      'jumping': 'Good height! Land softly!',
      'squatting': 'Perfect form! Keep your back straight!',
      'idle': 'Ready for your next move!'
    };
    return feedback[activity] || 'Keep moving!';
  };

  useEffect(() => {
    async function fetchActiveUsers() {
      const db = getDatabase(app);
      const statsSnap = await get(ref(db, 'userStats'));
      if (!statsSnap.exists()) return;
      const allStats = statsSnap.val();
      const today = new Date();
      const days = [...Array(7)].map((_, i) => {
        const d = new Date(today);
        d.setDate(today.getDate() - (6 - i));
        return d.toISOString().split('T')[0];
      });
      let count = 0;
      Object.values(allStats).forEach(user => {
        if (days.some(date => user[date] && user[date].workouts && user[date].workouts > 0)) {
          count++;
        }
      });
      setActiveUsers(count);
    }
    fetchActiveUsers();
  }, []);

  // Add logout handler
  const handleLogout = async () => {
    try {
      if (logoutUser) await logoutUser();
      await AsyncStorage.removeItem('@current_user');
      await AsyncStorage.removeItem('@FitnessApp:currentUser');
      router.replace('/role');
    } catch (error) {
      Alert.alert('Logout Error', 'Failed to log out. Please try again.');
    }
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <View style={[StyleSheet.absoluteFill, { backgroundColor: '#1a1a2e' }]} />
        <ActivityIndicator size="large" color="#fff" />
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <View style={[StyleSheet.absoluteFill, { backgroundColor: '#1a1a2e' }]} />
      {[...Array(20)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      <View style={[styles.glowCircle, { top: '10%', right: -50 }]} />
      <View style={[styles.glowCircle, { bottom: '15%', left: -50 }]} />

      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" />
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: hp(12) }}
        >
          {/* Header */}
          <View style={styles.header}>
            <View>
              <Text style={styles.greetingText}>Hello, {userEmail ? userEmail.split('@')[0] : 'Fitness Enthusiast'}</Text>
              <Text style={styles.welcomeText}>Welcome to Your Fitness Hub</Text>
            </View>
            <View style={styles.headerRightContainer}>
              <TouchableOpacity onPress={() => router.push('/profile/user-profile')} style={styles.avatarContainer}>
                <SafeImage 
                  source={{ uri: getImageByCategory('user') }} 
                  style={styles.avatar} 
                />
              </TouchableOpacity>
              <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
                <Ionicons name="log-out-outline" size={20} color="#fff" style={styles.logoutIcon} />
                <Text style={styles.logoutText}>Logout</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Image Slider */}
          <View style={styles.sliderPlaceholder}>
            <View style={[styles.sliderGradient, { backgroundColor: 'rgba(102, 126, 234, 0.3)' }]}>
              <Ionicons name="images-outline" size={wp(10)} color="#fff" style={styles.sliderIcon} />
              <Text style={styles.sliderText}>
                Featured Workouts
              </Text>
              <Text style={styles.sliderSubtext}>
                Personalized workout recommendations will appear here
              </Text>
            </View>
          </View>
          
          {/* Community Stats */}
          <View style={styles.communitySection}>
            <Text style={styles.sectionTitle}>Community</Text>
            <View style={styles.communityCard}>
              <View style={styles.communityStatItem}>
                <Text style={styles.communityStatValue}>{activeUsers}</Text>
                <Text style={styles.communityStatLabel}>Active Users</Text>
              </View>
              <View style={styles.communityStatItem}>
                <Text style={styles.communityStatValue}>24</Text>
                <Text style={styles.communityStatLabel}>Challenges</Text>
              </View>
              <View style={styles.communityStatItem}>
                <Text style={styles.communityStatValue}>5.2K</Text>
                <Text style={styles.communityStatLabel}>Workouts Today</Text>
              </View>
            </View>
          </View>
          
          {/* Daily Tracker */}
          <View style={styles.trackerSection}>
            <Text style={styles.sectionTitle}>Daily Progress</Text>
            <View style={styles.trackerGrid}>
              {Object.entries(trackerData).map(([key, value]) => (
                <View key={key} style={styles.trackerCard}>
                  <View style={styles.trackerIconContainer}>
                    <Ionicons name={
                      key === 'steps' ? 'walk' :
                      key === 'calories' ? 'flame' :
                      key === 'water' ? 'water' : 'moon'
                    } size={wp(6)} color="#fff" />
                  </View>
                  <Text style={styles.trackerValue}>
                    {key === 'sleep' ? value.toFixed(1) : value}
                    <Text style={styles.trackerUnit}>
                      {key === 'steps' ? ' steps' :
                       key === 'calories' ? ' kcal' :
                       key === 'water' ? ' glasses' : ' hrs'}
                    </Text>
                  </Text>
                  <Text style={styles.trackerLabel}>{key.charAt(0).toUpperCase() + key.slice(1)}</Text>
                  <View style={styles.progressBar}>
                    <View style={[styles.progressFill, { width: `${getProgressPercentage(value, dailyGoals[key])}%` }]} />
                  </View>
                  <Text style={styles.progressText}>{Math.round(getProgressPercentage(value, dailyGoals[key]))}% of goal</Text>
                  <View style={styles.trackerButtonsContainer}>
                    <TouchableOpacity 
                      style={styles.trackerButton} 
                      onPress={() => updateTracker(key, false)}
                    >
                      <Ionicons name="remove" size={wp(4)} color="#fff" />
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={styles.trackerButton} 
                      onPress={() => updateTracker(key, true)}
                    >
                      <Ionicons name="add" size={wp(4)} color="#fff" />
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>
          </View>
          
          {/* AI Coach Section */}
          <View style={styles.aiCoachSection}>
            <Text style={styles.sectionTitle}>AI Coach</Text>
            <View style={[styles.aiCoachCard, { backgroundColor: 'rgba(102, 126, 234, 0.3)' }]}>
              <View style={styles.aiCoachHeader}>
                <Ionicons name="sparkles" size={wp(8)} color="#fff" style={styles.aiCoachIcon} />
                <View style={styles.aiCoachTitleContainer}>
                  <Text style={styles.aiCoachTitle}>Personalized Guidance</Text>
                  <Text style={styles.aiCoachSubtitle}>Get real-time feedback and workout suggestions.</Text>
                </View>
              </View>
              <TouchableOpacity style={styles.aiCoachButton} onPress={generateWorkout} disabled={loadingAI}>
                {loadingAI ? <ActivityIndicator color="#fff" /> : <Text style={styles.aiCoachButtonText}>Get Suggestion</Text>}
              </TouchableOpacity>
              {aiCoachActive && (
                <View style={styles.aiStatus}>
                  <Text style={styles.aiStatusText}>Activity: {activityRecognition}</Text>
                  <Text style={styles.aiStatusFeedback}>{getActivityFeedback(activityRecognition)}</Text>
                </View>
              )}
            </View>
          </View>

          {/* Quick Actions */}
          <View style={styles.quickActionsSection}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false} 
              contentContainerStyle={styles.quickActionsContainer}
            >
              <TouchableOpacity style={styles.actionCard} onPress={() => router.push('/Exercises')}>
                <Ionicons name="barbell" size={wp(8)} color="#fff" />
                <Text style={styles.actionText}>Exercise Library</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionCard} onPress={() => router.push('/plans')}>
                <Ionicons name="reader" size={wp(8)} color="#fff" />
                <Text style={styles.actionText}>Workout Plans</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionCard} onPress={() => setShowWorkoutTracker(true)}>
                <Ionicons name="stopwatch" size={wp(8)} color="#fff" />
                <Text style={styles.actionText}>Start Workout</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionCard} onPress={() => router.push('/virtual-classes')}>
                <Ionicons name="videocam" size={wp(8)} color="#fff" />
                <Text style={styles.actionText}>Virtual Classes</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionCard} onPress={() => router.push('/nft-achievements')}>
                <Ionicons name="trophy" size={wp(8)} color="#fff" />
                <Text style={styles.actionText}>Achievements</Text>
              </TouchableOpacity>
            </ScrollView>
          </View>
          
          {/* Featured Content */}
          <View style={styles.featuredSection}>
            <Text style={styles.sectionTitle}>Featured Content</Text>
            <TouchableOpacity 
              style={styles.featuredCard}
              onPress={() => Alert.alert('Coming Soon', 'This feature will be available in the next update!')}
            >
              <View style={[styles.featuredGradient, { backgroundColor: 'rgba(102, 126, 234, 0.3)' }]}>
                <View style={styles.featuredContent}>
                  <View style={styles.featuredTextContainer}>
                    <Text style={styles.featuredTitle}>Nutrition Tracking</Text>
                    <Text style={styles.featuredDescription}>
                      Track your meals, count calories, and get personalized nutrition advice.
                    </Text>
                    <View style={styles.featuredBadge}>
                      <Text style={styles.featuredBadgeText}>Coming Soon</Text>
                    </View>
                  </View>
                  <Ionicons name="nutrition" size={wp(15)} color="#fff" style={styles.featuredIcon} />
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 5,
    zIndex: 0,
  },
  glowCircle: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    zIndex: 0,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: wp(5),
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight + 10 : 20,
    paddingBottom: 20,
  },
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    marginRight: wp(3),
  },
  greetingText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(4),
    color: 'rgba(255,255,255,0.7)',
  },
  welcomeText: {
    fontFamily: 'Poppins_700Bold',
    fontSize: wp(5),
    color: '#fff',
  },
  avatar: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    borderWidth: 2,
    borderColor: '#667eea',
  },
  logoutButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.8)',
    borderRadius: wp(5),
    paddingVertical: hp(1),
    paddingHorizontal: wp(3),
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  logoutIcon: {
    marginRight: wp(1),
  },
  logoutText: {
    color: '#fff',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: wp(3.5),
  },
  sliderPlaceholder: {
    marginHorizontal: wp(5),
    marginBottom: hp(3),
    borderRadius: 16,
    overflow: 'hidden',
  },
  sliderGradient: {
    padding: wp(5),
    alignItems: 'center',
    justifyContent: 'center',
    height: hp(20),
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
    borderRadius: 16,
  },
  sliderIcon: {
    marginBottom: hp(1),
  },
  sliderText: {
    fontFamily: 'Poppins_700Bold',
    fontSize: wp(5),
    color: '#fff',
    textAlign: 'center',
  },
  sliderSubtext: {
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(3.5),
    color: 'rgba(255,255,255,0.7)',
    textAlign: 'center',
    marginTop: hp(0.5),
  },
  communitySection: {
    marginBottom: hp(3),
  },
  communityCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: wp(5),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: wp(4),
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  communityStatItem: {
    alignItems: 'center',
    flex: 1,
  },
  communityStatValue: {
    fontFamily: 'Poppins_700Bold',
    fontSize: wp(5),
    color: '#fff',
  },
  communityStatLabel: {
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(3),
    color: 'rgba(255,255,255,0.7)',
    textAlign: 'center',
  },
  sectionTitle: {
    fontFamily: 'Poppins_700Bold',
    fontSize: wp(5.5),
    color: '#fff',
    marginLeft: wp(5),
    marginBottom: hp(1.5),
  },
  trackerSection: {
    marginBottom: hp(3),
  },
  trackerGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: wp(5),
  },
  trackerCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    width: wp(44),
    padding: wp(4),
    borderRadius: 16,
    marginBottom: hp(2),
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  trackerIconContainer: {
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  trackerValue: {
    fontFamily: 'Poppins_700Bold',
    fontSize: wp(5),
    color: '#fff',
    textAlign: 'center',
  },
  trackerUnit: {
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(3),
    color: 'rgba(255,255,255,0.7)',
  },
  trackerLabel: {
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(3.5),
    color: 'rgba(255,255,255,0.7)',
    marginBottom: hp(1),
  },
  progressBar: {
    width: '100%',
    height: hp(0.8),
    backgroundColor: 'rgba(0,0,0,0.2)',
    borderRadius: hp(0.4),
    marginBottom: hp(0.5),
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#667eea',
    borderRadius: hp(0.4),
  },
  progressText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(3),
    color: 'rgba(255,255,255,0.7)',
    marginBottom: hp(1),
  },
  trackerButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '80%',
  },
  trackerButton: {
    backgroundColor: 'rgba(102, 126, 234, 0.3)',
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  aiCoachSection: {
    marginBottom: hp(3),
  },
  aiCoachCard: {
    marginHorizontal: wp(5),
    padding: wp(4),
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  aiCoachHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  aiCoachIcon: {
    marginRight: wp(3),
  },
  aiCoachTitleContainer: {
    flex: 1,
  },
  aiCoachTitle: {
    fontFamily: 'Poppins_700Bold',
    fontSize: wp(4.5),
    color: '#fff',
  },
  aiCoachSubtitle: {
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(3.5),
    color: 'rgba(255,255,255,0.8)',
  },
  aiCoachButton: {
    backgroundColor: 'rgba(102, 126, 234, 0.5)',
    paddingVertical: hp(1.5),
    borderRadius: wp(6),
    marginTop: hp(2),
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  aiCoachButtonText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: wp(4),
    color: '#fff',
  },
  aiStatus: {
    marginTop: hp(2),
    paddingTop: hp(1),
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.2)',
  },
  aiStatusText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: wp(3.8),
    color: '#fff',
    textAlign: 'center',
  },
  aiStatusFeedback: {
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(3.5),
    color: 'rgba(255,255,255,0.7)',
    textAlign: 'center',
    marginTop: hp(0.5),
  },
  quickActionsSection: {
    marginBottom: hp(3),
  },
  quickActionsContainer: {
    paddingHorizontal: wp(5),
  },
  actionCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: wp(4),
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    width: wp(35),
    height: wp(35),
    marginRight: wp(3),
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  actionText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: wp(3.5),
    color: '#fff',
    marginTop: hp(1),
    textAlign: 'center',
  },
  featuredSection: {
    marginBottom: hp(3),
  },
  featuredCard: {
    marginHorizontal: wp(5),
    borderRadius: 16,
    overflow: 'hidden',
  },
  featuredGradient: {
    padding: wp(4),
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  featuredContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  featuredTextContainer: {
    flex: 1,
    paddingRight: wp(3),
  },
  featuredTitle: {
    fontFamily: 'Poppins_700Bold',
    fontSize: wp(4.5),
    color: '#fff',
    marginBottom: hp(0.5),
  },
  featuredDescription: {
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(3.5),
    color: 'rgba(255,255,255,0.8)',
    marginBottom: hp(1),
  },
  featuredBadge: {
    backgroundColor: 'rgba(102, 126, 234, 0.3)',
    paddingVertical: hp(0.5),
    paddingHorizontal: wp(2),
    borderRadius: wp(3),
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  featuredBadgeText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: wp(3),
    color: '#fff',
  },
  featuredIcon: {
    opacity: 0.9,
  },
});