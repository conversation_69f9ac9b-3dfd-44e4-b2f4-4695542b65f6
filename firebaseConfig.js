// Temporarily disable Firebase imports to fix bundling issue
// import { initializeApp, getApps } from 'firebase/app';
// import { getDatabase, ref, set, push, get, child } from 'firebase/database';

// Mock Firebase functions for now
const initializeApp = () => ({});
const getApps = () => [];
const getDatabase = () => ({});
const ref = () => ({});
const set = () => Promise.resolve();
const push = () => ({});
const get = () => Promise.resolve({});
const child = () => ({});

// Your Firebase config - replace placeholders with your actual config values
const firebaseConfig = {
  apiKey: "AIzaSyBXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  authDomain: "fitness-app-12345.firebaseapp.com",
  databaseURL: "https://fitness-app-12345-default-rtdb.firebaseio.com",
  projectId: "fitness-app-12345",
  storageBucket: "fitness-app-12345.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdef1234567890",
};

// Initialize Firebase app with error handling
let app;
let db;

try {
  // Check if Firebase config is properly set up
  if (firebaseConfig.apiKey === "AIzaSyBXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX") {
    console.log('Firebase not configured - using AsyncStorage fallback');
    app = null;
    db = null;
  } else {
    // Initialize Firebase app only if not already initialized
    app = !getApps().length ? initializeApp(firebaseConfig) : getApps()[0];
    
    // Export Firebase Realtime Database instance
    db = getDatabase(app);
  }
} catch (error) {
  console.error('Firebase initialization error:', error);
  // Set fallback values
  app = null;
  db = null;
}

// Add a function to check if Firebase is properly configured
export const isFirebaseConfigured = () => {
  return app !== null && db !== null;
};

// Save push token for a user (multi-device)
export async function savePushTokenToFirebase(userEmail, token) {
  if (!userEmail || !token || !db) {
    console.log('Firebase not available for push token storage');
    return;
  }
  try {
    const tokensRef = ref(db, `pushTokens/${encodeURIComponent(userEmail)}`);
    // Use push() to allow multiple tokens per user
    await push(tokensRef, token);
  } catch (error) {
    console.error('Error saving push token to Firebase:', error);
  }
}

export default app;
