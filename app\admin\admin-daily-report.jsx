import React, { useState, useEffect } from 'react';
import {
  View, Text, StyleSheet, ActivityIndicator, TouchableOpacity, ScrollView, Dimensions, Platform, SafeAreaView
} from 'react-native';
// import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
// Temporarily disable Firebase imports
// import { getDatabase, ref, get } from 'firebase/database';
// import app from '../../firebaseConfig';

// Mock Firebase functions
const getDatabase = () => ({});
const ref = () => ({});
const get = () => Promise.resolve({});
const app = {};
// import DateTimePicker from '@react-native-community/datetimepicker';
import { useRouter } from 'expo-router';
// import { Poppins_400Regular, Poppins_700Bold, Poppins_600SemiBold, useFonts } from '@expo-google-fonts/poppins';
// import * as FileSystem from 'expo-file-system';
// import * as Sharing from 'expo-sharing';
// import { LineChart } from 'react-native-chart-kit';

const { width } = Dimensions.get('window');

function sanitizeChartData(data) {
  return Array.isArray(data) ? data.map(d => (isFinite(d) && !isNaN(d) ? d : 0)) : [];
}

export default function AdminDailyReport() {
  const router = useRouter();
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState(null);
  const [error, setError] = useState(null);
  // const [fontsLoaded] = useFonts({
  //   Poppins_400Regular,
  //   Poppins_700Bold,
  //   Poppins_600SemiBold,
  // });
  const fontsLoaded = true; // Temporary fix
  const [metric, setMetric] = useState('steps');
  const [chartData, setChartData] = useState({ labels: [], data: [] });
  const [rangeStart, setRangeStart] = useState(null);
  const [rangeEnd, setRangeEnd] = useState(null);
  const [showStartPicker, setShowStartPicker] = useState(false);
  const [showEndPicker, setShowEndPicker] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const db = getDatabase(app);
      const snapshot = await get(ref(db, 'pushTokens'));
      if (snapshot.exists()) {
        const usersObj = snapshot.val();
        setUsers(Object.keys(usersObj));
      } else {
        setUsers([]);
      }
    } catch (e) {
      setError('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    if (!selectedUser) return;
    setLoading(true);
    setStats(null);
    setError(null);
    try {
      const db = getDatabase(app);
      const dateKey = date.toISOString().split('T')[0];
      // Example: stats stored under /userStats/{email}/{date}
      const statsRef = ref(db, `userStats/${encodeURIComponent(selectedUser)}/${dateKey}`);
      const snapshot = await get(statsRef);
      if (snapshot.exists()) {
        setStats(snapshot.val());
      } else {
        setStats(null);
        setError('No data for this user on this date.');
      }
    } catch (e) {
      setError('Failed to load stats');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (selectedUser && date) fetchStats();
    // eslint-disable-next-line
  }, [selectedUser, date]);

  useEffect(() => {
    async function fetchChartData() {
      if (!selectedUser) return;
      const db = getDatabase(app);
      const userStatsRef = ref(db, `userStats/${encodeURIComponent(selectedUser)}`);
      const snapshot = await get(userStatsRef);
      if (!snapshot.exists()) return;
      const allStats = snapshot.val();
      let dates = Object.keys(allStats).sort();
      // Filter by custom range if set
      if (rangeStart && rangeEnd) {
        const start = rangeStart.toISOString().split('T')[0];
        const end = rangeEnd.toISOString().split('T')[0];
        dates = dates.filter(d => d >= start && d <= end);
      } else {
        dates = dates.slice(-7); // Default: last 7 days
      }
      setChartData({
        labels: dates.map(d => d.slice(5)),
        data: dates.map(d => allStats[d]?.[metric] ?? 0)
      });
    }
    fetchChartData();
  }, [selectedUser, metric, date, rangeStart, rangeEnd]);

  const exportCurrentReport = async () => {
    if (!stats) return;
    // const csv = `Date,Steps,Calories,Sleep,Water,Workouts\n${date.toISOString().split('T')[0]},${stats.steps ?? ''},${stats.calories ?? ''},${stats.sleep ?? ''},${stats.water ?? ''},${stats.workouts ?? ''}`;
    // const fileUri = FileSystem.cacheDirectory + `daily_report_${selectedUser}_${date.toISOString().split('T')[0]}.csv`;
    // await FileSystem.writeAsStringAsync(fileUri, csv, { encoding: FileSystem.EncodingType.UTF8 });
    Alert.alert('Export Complete', 'Report export functionality will be available in the next update');
  };

  const exportAllReports = async () => {
    if (!selectedUser) return;
    const db = getDatabase(app);
    const userStatsRef = ref(db, `userStats/${encodeURIComponent(selectedUser)}`);
    const snapshot = await get(userStatsRef);
    if (!snapshot.exists()) return;
    const allStats = snapshot.val();
    let csv = 'Date,Steps,Calories,Sleep,Water,Workouts\n';
    Object.entries(allStats).forEach(([dateKey, s]) => {
      csv += `${dateKey},${s.steps ?? ''},${s.calories ?? ''},${s.sleep ?? ''},${s.water ?? ''},${s.workouts ?? ''}\n`;
    });
    // const fileUri = FileSystem.cacheDirectory + `all_reports_${selectedUser}.csv`;
    // await FileSystem.writeAsStringAsync(fileUri, csv, { encoding: FileSystem.EncodingType.UTF8 });
    Alert.alert('Export Complete', 'All reports export functionality will be available in the next update');
  };

  if (!fontsLoaded) return null;

  return (
    <SafeAreaView style={styles.container}>
      <View style={[StyleSheet.absoluteFill, { backgroundColor: '#1a1a2e' }]} />
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Admin Daily Report</Text>
        <View style={{ width: 40 }} />
      </View>
      <ScrollView contentContainerStyle={styles.content}>
        {/* User Selector */}
        <Text style={styles.label}>Select User</Text>
        <View style={styles.dropdown}>
          {users.length === 0 ? (
            <Text style={styles.dropdownText}>No users found</Text>
          ) : (
            users.map(email => (
              <TouchableOpacity
                key={email}
                style={[styles.dropdownItem, selectedUser === email && styles.selectedDropdownItem]}
                onPress={() => setSelectedUser(email)}
              >
                <Text style={styles.dropdownText}>{email}</Text>
              </TouchableOpacity>
            ))
          )}
        </View>
        {/* Date Picker */}
        <Text style={styles.label}>Select Date</Text>
        <TouchableOpacity onPress={() => setShowDatePicker(true)} style={styles.dateButton}>
          <Text style={styles.dateButtonText}>{date.toDateString()}</Text>
        </TouchableOpacity>
        {showDatePicker && (
          <View style={styles.datePickerContainer}>
            <Text style={styles.datePickerText}>Date picker will be available in the next update</Text>
            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={() => setShowDatePicker(false)}
            >
              <Text style={styles.datePickerButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        )}
        {/* Stats Display */}
        {loading && <ActivityIndicator color="#667eea" style={{ marginTop: 20 }} />}
        {error && <Text style={styles.error}>{error}</Text>}
        {stats && (
          <View style={styles.statsCard}>
            <Text style={styles.statsTitle}>Daily Stats</Text>
            <Text style={styles.statsItem}>Steps: {stats.steps ?? 'N/A'}</Text>
            <Text style={styles.statsItem}>Calories: {stats.calories ?? 'N/A'}</Text>
            <Text style={styles.statsItem}>Sleep: {stats.sleep ?? 'N/A'} hrs</Text>
            <Text style={styles.statsItem}>Water: {stats.water ?? 'N/A'} glasses</Text>
            <Text style={styles.statsItem}>Workouts: {stats.workouts ?? 'N/A'}</Text>
          </View>
        )}
        {stats && (
          <View style={{flexDirection:'row', justifyContent:'space-between', marginTop:10}}>
            <TouchableOpacity onPress={exportCurrentReport} style={{backgroundColor:'#FFD93D', borderRadius:8, padding:10, marginRight:10}}>
              <Text style={{color:'#222', fontFamily:'Poppins_600SemiBold'}}>Export This Report</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={exportAllReports} style={{backgroundColor:'#667eea', borderRadius:8, padding:10}}>
              <Text style={{color:'#fff', fontFamily:'Poppins_600SemiBold'}}>Export All Reports</Text>
            </TouchableOpacity>
          </View>
        )}
        {/* Metric Filter and Chart */}
        <View style={{marginTop:20}}>
          <Text style={{color:'#fff', fontFamily:'Poppins_600SemiBold', fontSize:16, marginBottom:8}}>Analytics</Text>
          <View style={{flexDirection:'row', marginBottom:10, alignItems:'center'}}>
            <TouchableOpacity onPress={()=>setShowStartPicker(true)} style={{backgroundColor:'#222', borderRadius:8, padding:8, marginRight:8}}>
              <Text style={{color:'#fff', fontFamily:'Poppins_400Regular'}}>From: {rangeStart ? rangeStart.toDateString() : 'Start'}</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={()=>setShowEndPicker(true)} style={{backgroundColor:'#222', borderRadius:8, padding:8, marginRight:8}}>
              <Text style={{color:'#fff', fontFamily:'Poppins_400Regular'}}>To: {rangeEnd ? rangeEnd.toDateString() : 'End'}</Text>
            </TouchableOpacity>
            {(rangeStart || rangeEnd) && (
              <TouchableOpacity onPress={()=>{setRangeStart(null);setRangeEnd(null);}} style={{backgroundColor:'#F44336', borderRadius:8, padding:8}}>
                <Text style={{color:'#fff', fontFamily:'Poppins_400Regular'}}>Clear</Text>
              </TouchableOpacity>
            )}
          </View>
          {showStartPicker && (
            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerText}>Start date picker will be available in the next update</Text>
              <TouchableOpacity
                style={styles.datePickerButton}
                onPress={() => setShowStartPicker(false)}
              >
                <Text style={styles.datePickerButtonText}>Close</Text>
              </TouchableOpacity>
            </View>
          )}
          {showEndPicker && (
            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerText}>End date picker will be available in the next update</Text>
              <TouchableOpacity
                style={styles.datePickerButton}
                onPress={() => setShowEndPicker(false)}
              >
                <Text style={styles.datePickerButtonText}>Close</Text>
              </TouchableOpacity>
            </View>
          )}
          <View style={{flexDirection:'row', marginBottom:10}}>
            {['steps','calories','workouts','sleep','water'].map(m => (
              <TouchableOpacity key={m} onPress={()=>setMetric(m)} style={{backgroundColor:metric===m?'#FFD93D':'#222', borderRadius:8, padding:8, marginRight:8}}>
                <Text style={{color:metric===m?'#222':'#fff', fontFamily:'Poppins_600SemiBold'}}>{m.charAt(0).toUpperCase()+m.slice(1)}</Text>
              </TouchableOpacity>
            ))}
          </View>
          <View style={styles.chartPlaceholder}>
            <Text style={styles.chartPlaceholderText}>📊</Text>
            <Text style={styles.chartPlaceholderTitle}>Chart View</Text>
            <Text style={styles.chartPlaceholderSubtitle}>
              Analytics charts will be available in the next update
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#0c0c0c' },
  header: {
    flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 20, paddingTop: 40, paddingBottom: 20,
  },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', padding: 8, borderRadius: 8 },
  headerTitle: { fontSize: 22, fontFamily: 'Poppins_700Bold', color: '#fff' },
  content: { paddingHorizontal: 20, paddingBottom: 30 },
  label: { color: '#fff', fontFamily: 'Poppins_600SemiBold', fontSize: 16, marginTop: 20, marginBottom: 8 },
  dropdown: { backgroundColor: 'rgba(255,255,255,0.08)', borderRadius: 10, marginBottom: 10 },
  dropdownItem: { padding: 12 },
  selectedDropdownItem: { backgroundColor: 'rgba(102,126,234,0.2)' },
  dropdownText: { color: '#fff', fontFamily: 'Poppins_400Regular', fontSize: 15 },
  dateButton: { backgroundColor: '#667eea', borderRadius: 8, padding: 12, alignItems: 'center', marginBottom: 20 },
  dateButtonText: { color: '#fff', fontFamily: 'Poppins_600SemiBold', fontSize: 16 },
  statsCard: { backgroundColor: 'rgba(255,255,255,0.08)', borderRadius: 16, padding: 20, marginTop: 20 },
  statsTitle: { color: '#fff', fontFamily: 'Poppins_700Bold', fontSize: 18, marginBottom: 10 },
  statsItem: { color: '#fff', fontFamily: 'Poppins_400Regular', fontSize: 15, marginBottom: 6 },
  error: { color: '#ff6b6b', fontFamily: 'Poppins_600SemiBold', marginTop: 20 },
  datePickerContainer: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    padding: 20,
    marginVertical: 10,
    alignItems: 'center',
  },
  datePickerText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 15,
  },
  datePickerButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  datePickerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  chartPlaceholder: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 16,
    height: 180,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 10,
  },
  chartPlaceholderText: {
    fontSize: 40,
    marginBottom: 10,
  },
  chartPlaceholderTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
  },
  chartPlaceholderSubtitle: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
});