// This file is used in the project. Do NOT delete.
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Linking,
  Modal,
  Dimensions,
  Image,
  Platform,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { Animated as RNAnimated } from 'react-native';
import Animated, { FadeInLeft, FadeInUp, FadeInRight, FadeInDown } from 'react-native-reanimated';
import { WebView } from 'react-native-webview';
import { handleVideoTutorial, isVideoTutorialEnabled, getVideoUrl } from '../constants/data';
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';
import {
  useFonts,
  Poppins_400Regular,
  Poppins_600SemiBold,
  Poppins_700Bold,
} from '@expo-google-fonts/poppins';
import PropTypes from 'prop-types';
import AdvancedParticle from '../components/AdvancedParticle';
import SafeImage from '../components/SafeImage';
import { getImageByCategory } from '../constants/remoteImages';

const { width, height } = Dimensions.get('window');

export default function FavoritesPage() {
  const router = useRouter();
  const [favoriteExercises, setFavoriteExercises] = useState([]);
  const [videoModalVisible, setVideoModalVisible] = useState(false);
  const [currentVideoUrl, setCurrentVideoUrl] = useState('');
  const [currentExercise, setCurrentExercise] = useState(null);
  const [showInstructions, setShowInstructions] = useState(false);

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  const handleBack = () => {
    router.back();
  };

  // Function to convert YouTube URL to embeddable format
  const getEmbeddableYouTubeUrl = (url) => {
    if (!url) return '';
    
    // Extract video ID from various YouTube URL formats
    let videoId = '';
    
    if (url.includes('youtube.com/watch?v=')) {
      videoId = url.split('youtube.com/watch?v=')[1].split('&')[0];
    } else if (url.includes('youtu.be/')) {
      videoId = url.split('youtu.be/')[1].split('?')[0];
    } else if (url.includes('youtube.com/embed/')) {
      videoId = url.split('youtube.com/embed/')[1].split('?')[0];
    }
    
    if (videoId) {
      return `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&showinfo=0&controls=1`;
    }
    
    return url;
  };

  // Function to open video in modal
  const openVideoModal = (exercise) => {
    console.log('🎥 Opening video modal for:', exercise.name);
    
    const videoUrl = exercise.videoUrl || handleVideoTutorial(exercise.name);
    const embeddableUrl = getEmbeddableYouTubeUrl(videoUrl);
    
    console.log('📹 Video URL:', videoUrl);
    console.log('📹 Embeddable URL:', embeddableUrl);
    
    setCurrentExercise(exercise);
    setCurrentVideoUrl(embeddableUrl);
    setVideoModalVisible(true);
  };

  // Function to close video modal
  const closeVideoModal = () => {
    console.log('❌ Closing video modal');
    setVideoModalVisible(false);
    setCurrentVideoUrl('');
    setCurrentExercise(null);
  };

  useEffect(() => {
    loadFavoriteExercises();
  }, []);

  // Exercise image database with high-quality photos for the specific exercises
  const exerciseImageDatabase = {
    'push-ups': { uri: 'https://images.pexels.com/photos/416778/pexels-photo-416778.jpeg?auto=compress&w=400&h=300&fit=crop' },
    'squats': { uri: 'https://images.pexels.com/photos/2261482/pexels-photo-2261482.jpeg?auto=compress&w=400&h=300&fit=crop' },
    'burpees': { uri: 'https://images.pexels.com/photos/1552242/pexels-photo-1552242.jpeg?auto=compress&w=400&h=300&fit=crop' },
    'plank': { uri: 'https://images.pexels.com/photos/4056723/pexels-photo-4056723.jpeg?auto=compress&w=400&h=300&fit=crop' },
    'mountain-climbers': { uri: 'https://images.pexels.com/photos/1552106/pexels-photo-1552106.jpeg?auto=compress&w=400&h=300&fit=crop' },
    'jumping-climbers': { uri: 'https://images.pexels.com/photos/1552106/pexels-photo-1552106.jpeg?auto=compress&w=400&h=300&fit=crop' },
    'jumping-jacks': { uri: 'https://images.pexels.com/photos/1552242/pexels-photo-1552242.jpeg?auto=compress&w=400&h=300&fit=crop' },
    // Fallback images using remote assets
    'push-ups-fallback': { uri: getImageByCategory('exercise') },
    'squats-fallback': { uri: getImageByCategory('exercise') },
    'burpees-fallback': { uri: getImageByCategory('exercise') },
    'plank-fallback': { uri: getImageByCategory('exercise') },
    'mountain-climbers-fallback': { uri: getImageByCategory('exercise') },
    'jumping-climbers-fallback': { uri: getImageByCategory('exercise') },
    'jumping-jacks-fallback': { uri: getImageByCategory('exercise') },
  };

  const getExerciseImage = (exerciseName) => {
    const key = exerciseName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    return exerciseImageDatabase[key] || exerciseImageDatabase[key + '-fallback'] || 
           { uri: getImageByCategory('exercise') };
  };

  const loadFavoriteExercises = () => {
    // Enhanced favorite exercises with proper images and exercise data
    const mockFavorites = [
      {
        id: 'push-ups-001',
        name: 'Push-ups',
        category: 'Strength',
        duration: '10 min',
        difficulty: 'Beginner',
        calories: 80,
        description: 'Classic upper body exercise targeting chest, shoulders, and triceps',
        videoUrl: 'https://www.youtube.com/watch?v=IODxDxX7oi4',
        equipment: 'body weight',
        muscleGroups: ['Chest', 'Shoulders', 'Triceps'],
        videoTutorialEnabled: true,
        bodyPart: 'upper body',
        target: 'pectorals',
        imageSource: getExerciseImage('push-ups'),
        instructions: [
          'Start in a plank position with hands slightly wider than shoulders',
          'Lower your body until chest nearly touches the floor',
          'Push back up to starting position',
          'Keep your core tight throughout the movement',
          'Maintain a straight line from head to heels'
        ],
        secondaryMuscles: ['triceps', 'anterior deltoid', 'core']
      },
      {
        id: 'squats-002',
        name: 'Squats',
        category: 'Strength',
        duration: '15 min',
        difficulty: 'Beginner',
        calories: 120,
        description: 'Lower body compound exercise for legs and glutes',
        videoUrl: 'https://www.youtube.com/watch?v=aclHkVaku9U',
        equipment: 'body weight',
        muscleGroups: ['Quadriceps', 'Glutes', 'Hamstrings'],
        videoTutorialEnabled: true,
        bodyPart: 'lower body',
        target: 'quadriceps',
        imageSource: getExerciseImage('squats'),
        instructions: [
          'Stand with feet shoulder-width apart',
          'Lower your body as if sitting back into a chair',
          'Keep your chest up and knees behind toes',
          'Lower until thighs are parallel to floor',
          'Push through heels to return to starting position'
        ],
        secondaryMuscles: ['glutes', 'hamstrings', 'calves']
      },
      {
        id: 'burpees-003',
        name: 'Burpees',
        category: 'Cardio',
        duration: '12 min',
        difficulty: 'Advanced',
        calories: 150,
        description: 'Full-body high-intensity exercise combining squat, plank, and jump',
        videoUrl: 'https://www.youtube.com/watch?v=TU8QYVW0gDU',
        equipment: 'body weight',
        muscleGroups: ['Full Body'],
        videoTutorialEnabled: true,
        bodyPart: 'cardio',
        target: 'cardiovascular system',
        imageSource: getExerciseImage('burpees'),
        instructions: [
          'Start standing with feet shoulder-width apart',
          'Drop into a squat and place hands on floor',
          'Jump feet back into plank position',
          'Do a push-up (optional)',
          'Jump feet back to squat position',
          'Explode up with arms overhead'
        ],
        secondaryMuscles: ['full body']
      },
      {
        id: 'plank-004',
        name: 'Plank',
        category: 'Core',
        duration: '8 min',
        difficulty: 'Beginner',
        calories: 60,
        description: 'Isometric core exercise for stability and strength',
        videoUrl: 'https://www.youtube.com/watch?v=ASdvN_XEl_c',
        equipment: 'body weight',
        muscleGroups: ['Core', 'Shoulders', 'Back'],
        videoTutorialEnabled: true,
        bodyPart: 'core',
        target: 'abdominals',
        imageSource: getExerciseImage('plank'),
        instructions: [
          'Start in a forearm plank position',
          'Keep your body in a straight line from head to heels',
          'Engage your core and glutes',
          'Hold the position without letting hips sag',
          'Breathe steadily throughout the hold'
        ],
        secondaryMuscles: ['shoulders', 'back', 'glutes']
      },
      {
        id: 'jumping-climbers-005',
        name: 'Jumping Climbers',
        category: 'Cardio',
        duration: '10 min',
        difficulty: 'Intermediate',
        calories: 100,
        description: 'Dynamic cardio exercise combining mountain climbers with jumps',
        videoUrl: 'https://www.youtube.com/watch?v=nmwgirgXLYM',
        equipment: 'body weight',
        muscleGroups: ['Core', 'Cardio', 'Legs'],
        videoTutorialEnabled: true,
        bodyPart: 'full body',
        target: 'cardiovascular system',
        imageSource: getExerciseImage('jumping-climbers'),
        instructions: [
          'Start in a plank position',
          'Drive one knee toward chest while keeping other leg extended',
          'Quickly switch legs in a running motion',
          'Add a small jump between switches for intensity',
          'Keep your core engaged throughout'
        ],
        secondaryMuscles: ['shoulders', 'core', 'legs']
      },
      {
        id: 'jumping-jacks-006',
        name: 'Jumping Jacks',
        category: 'Cardio',
        duration: '8 min',
        difficulty: 'Beginner',
        calories: 90,
        description: 'Classic cardio exercise for full body conditioning',
        videoUrl: 'https://www.youtube.com/watch?v=UpH7rm0cYbM',
        equipment: 'body weight',
        muscleGroups: ['Cardio', 'Legs', 'Shoulders'],
        videoTutorialEnabled: true,
        bodyPart: 'full body',
        target: 'cardiovascular system',
        imageSource: getExerciseImage('jumping-jacks'),
        instructions: [
          'Stand with feet together and arms at sides',
          'Jump feet apart while raising arms overhead',
          'Jump back to starting position',
          'Keep movements controlled and rhythmic',
          'Land softly to protect joints'
        ],
        secondaryMuscles: ['shoulders', 'legs', 'core']
      }
    ];

    setFavoriteExercises(mockFavorites);
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'beginner': return '#4CAF50';
      case 'intermediate': return '#FF9800';
      case 'advanced': return '#F44336';
      default: return '#667eea';
    }
  };

  const getCategoryIcon = (category) => {
    switch (category.toLowerCase()) {
      case 'strength': return 'barbell';
      case 'cardio': return 'heart';
      case 'core': return 'body';
      case 'yoga': return 'leaf';
      default: return 'fitness';
    }
  };

  const startWorkout = (exercise) => {
    Alert.alert('Start Workout', `Ready to start ${exercise.name}?`);
  };

  const removeFromFavorites = (exerciseId) => {
    Alert.alert(
      'Remove from Favorites',
      'Are you sure you want to remove this exercise from favorites?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            setFavoriteExercises(prev => prev.filter(ex => ex.id !== exerciseId));
            Alert.alert('Removed', 'Exercise removed from favorites');
          }
        }
      ]
    );
  };

  const openTutorialVideo = async (exercise) => {
    const videoUrl = getVideoUrl(exercise.name);
    if (await Linking.canOpenURL(videoUrl)) {
      await Linking.openURL(videoUrl);
    } else {
      Alert.alert('Error', 'Could not open video tutorial.');
    }
  };

  if (!fontsLoaded) {
    return null;
  }
  
  return (
    <View style={styles.outerContainer}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      {[...Array(20)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      <View style={[styles.glowCircle, { top: '10%', right: -50 }]} />
      <View style={[styles.glowCircle, { bottom: '15%', left: -50 }]} />

      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" />
        
        {/* Fixed Header - Properly positioned */}
        <View style={styles.headerContainer}>
          <View style={styles.header}>
            <TouchableOpacity onPress={handleBack} style={styles.backButton}>
              <Ionicons name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Favorite Exercises</Text>
            <View style={{ width: 40 }} />
          </View>
        </View>

        {/* Favorite Exercises List */}
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          {favoriteExercises.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="heart-dislike-outline" size={wp(20)} color="rgba(255,255,255,0.5)" />
              <Text style={styles.emptyText}>No Favorites Yet</Text>
              <Text style={styles.emptySubtext}>Add exercises to your favorites to see them here.</Text>
            </View>
          ) : (
            favoriteExercises.map((item, index) => (
              <Animated.View key={item.id} entering={FadeInDown.delay(index * 100).duration(600)}>
                <View style={styles.exerciseCard}>
                  <SafeImage source={item.imageSource} style={styles.exerciseImage} />
                  <LinearGradient
                    colors={['transparent', 'rgba(0,0,0,0.8)']}
                    style={styles.imageOverlay}
                  />
                  <View style={styles.cardHeader}>
                    <Text style={styles.exerciseName} numberOfLines={1}>{item.name}</Text>
                    <TouchableOpacity onPress={() => removeFromFavorites(item.id)}>
                      <Ionicons name="heart" size={wp(6)} color="#ff6b6b" />
                    </TouchableOpacity>
                  </View>
                  <View style={styles.cardFooter}>
                    <View style={styles.infoChip}>
                      <Ionicons name={getCategoryIcon(item.category)} size={wp(4)} color="#fff" />
                      <Text style={styles.infoText}>{item.category}</Text>
                    </View>
                    <View style={styles.infoChip}>
                      <Ionicons name="flame" size={wp(4)} color="#fff" />
                      <Text style={styles.infoText}>{item.calories} cal</Text>
                    </View>
                    <TouchableOpacity style={styles.playButton} onPress={() => openVideoModal(item)}>
                      <Ionicons name="play" size={wp(6)} color="#fff" />
                    </TouchableOpacity>
                  </View>
                </View>
              </Animated.View>
            ))
          )}
        </ScrollView>
      </SafeAreaView>

      {/* Video Player Modal */}
      {currentExercise && (
        <Modal
          animationType="slide"
          transparent={true}
          visible={videoModalVisible}
          onRequestClose={closeVideoModal}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{currentExercise.name}</Text>
                <TouchableOpacity onPress={closeVideoModal} style={styles.closeButton}>
                  <Ionicons name="close" size={28} color="#fff" />
                </TouchableOpacity>
              </View>
              <View style={styles.videoPlayer}>
                <WebView
                  source={{ uri: currentVideoUrl }}
                  style={{ flex: 1, backgroundColor: '#000' }}
                  allowsFullscreenVideo
                  javaScriptEnabled
                  domStorageEnabled
                />
              </View>
              <ScrollView style={styles.instructionsContainer}>
                <Text style={styles.instructionsTitle}>Instructions</Text>
                {currentExercise.instructions.map((instruction, index) => (
                  <Text key={index} style={styles.instructionText}>
                    {`\u2022 ${instruction}`}
                  </Text>
                ))}
              </ScrollView>
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
}

FavoritesPage.propTypes = {
  // No props, but add for future-proofing
};

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  container: {
    flex: 1,
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 5,
    zIndex: 0,
  },
  glowCircle: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    zIndex: 0,
  },
  // Fixed header container with proper positioning
  headerContainer: {
    backgroundColor: 'rgba(20,20,30,0.95)',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight + 10 : 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
    zIndex: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(5),
    paddingVertical: 10,
  },
  backButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(102,126,234,0.8)',
    borderRadius: 22,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    elevation: 3,
  },
  headerTitle: {
    fontFamily: 'Poppins_700Bold',
    fontSize: wp(5.5),
    color: '#fff',
    textAlign: 'center',
    flex: 1,
  },
  scrollContainer: {
    paddingHorizontal: wp(5),
    paddingBottom: 30,
    paddingTop: 10,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: height * 0.2,
  },
  emptyText: {
    fontFamily: 'Poppins_700Bold',
    fontSize: wp(6),
    color: 'rgba(255,255,255,0.7)',
    marginTop: 20,
  },
  emptySubtext: {
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(4),
    color: 'rgba(255,255,255,0.5)',
    marginTop: 10,
    textAlign: 'center',
    paddingHorizontal: wp(10),
  },
  exerciseCard: {
    height: height * 0.25,
    borderRadius: 25,
    overflow: 'hidden',
    marginBottom: 20,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  exerciseImage: {
    ...StyleSheet.absoluteFillObject,
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageOverlay: {
    ...StyleSheet.absoluteFillObject,
  },
  cardHeader: {
    position: 'absolute',
    top: 15,
    left: 15,
    right: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  exerciseName: {
    fontFamily: 'Poppins_700Bold',
    fontSize: wp(5),
    color: '#fff',
    flex: 1,
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
  },
  infoChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 15,
  },
  infoText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: wp(3.5),
    color: '#fff',
    marginLeft: 8,
  },
  playButton: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    backgroundColor: 'rgba(102, 126, 234, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.8)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    height: height * 0.8,
    backgroundColor: '#1e1e2d',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
  },
  modalTitle: {
    fontFamily: 'Poppins_700Bold',
    fontSize: wp(5),
    color: '#fff',
    flex: 1,
  },
  closeButton: {
    padding: 8,
  },
  videoPlayer: {
    width: '100%',
    aspectRatio: 16 / 9,
    backgroundColor: '#000',
  },
  instructionsContainer: {
    padding: 20,
  },
  instructionsTitle: {
    fontFamily: 'Poppins_700Bold',
    fontSize: wp(4.5),
    color: '#fff',
    marginBottom: 10,
  },
  instructionText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(4),
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 8,
  },
});