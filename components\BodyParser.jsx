import React from 'react';
import { View, Text, Image, ScrollView } from 'react-native';

const bodyParts = [
  {
    name: 'Back',
    image: { uri: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=200&h=200&fit=crop&crop=center&auto=format&q=80' },
  },
  {
    name: 'Cardio',
    image: { uri: 'https://images.unsplash.com/photo-1538805060514-97d9cc17730c?w=200&h=200&fit=crop&crop=center&auto=format&q=80' },
  },
  {
    name: 'Chest',
    image: { uri: 'https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?w=200&h=200&fit=crop&crop=center&auto=format&q=80' },
  },
  {
    name: 'Lower Arms',
    image: { uri: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=200&h=200&fit=crop&crop=center&auto=format&q=80' },
  },
  {
    name: 'Lower Legs',
    image: { uri: 'https://images.unsplash.com/photo-1549476464-37392f717541?w=200&h=200&fit=crop&crop=center&auto=format&q=80' },
  },
  {
    name: 'Shoulder',
    image: { uri: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=200&h=200&fit=crop&crop=center&auto=format&q=80' },
  },
];

export default function BodyParser() {
  return (
    <View style={{ marginVertical: 16 }}>
      <Text style={{ fontWeight: 'bold', fontSize: 18, marginBottom: 8 }}>Body Parts</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {bodyParts.map((part, idx) => (
          <View key={idx} style={{ alignItems: 'center', marginRight: 16 }}>
            <Image
              source={part.image}
              style={{ width: 70, height: 70, borderRadius: 35, marginBottom: 4 }}
            />
            <Text>{part.name}</Text>
          </View>
        ))}
      </ScrollView>
    </View>
  );
} 