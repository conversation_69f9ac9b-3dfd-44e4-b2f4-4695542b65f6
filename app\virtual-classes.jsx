import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  FlatList,
  Modal,
  Image,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInLeft, FadeInRight } from 'react-native-reanimated';
import { useUser } from '../context/UserContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  useFonts,
  Poppins_700Bold,
  Poppins_400Regular,
  Poppins_600SemiBold,
} from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';

const { width } = Dimensions.get('window');

export default function VirtualClassesPage() {
  const router = useRouter();
  const { currentUser: user } = useUser();
  const [liveClasses, setLiveClasses] = useState([]);
  const [upcomingClasses, setUpcomingClasses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [registrationModalVisible, setRegistrationModalVisible] = useState(false);
  const [selectedClass, setSelectedClass] = useState(null);
  const [registeredClasses, setRegisteredClasses] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [classes, setClasses] = useState([]);

  let [fontsLoaded] = useFonts({
    Poppins_700Bold,
    Poppins_400Regular,
    Poppins_600SemiBold,
  });

  useEffect(() => {
    loadVirtualClasses();
  }, []);

  const loadVirtualClasses = async () => {
    try {
      // Simulate loading live and upcoming classes
      setTimeout(() => {
        setLiveClasses([
          {
            id: 1,
            title: 'HIIT Cardio Blast',
            instructor: 'Sarah Johnson',
            participants: 234,
            duration: '30 min',
            difficulty: 'Intermediate',
            isLive: true,
            imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop&auto=format&q=80',
          },
          {
            id: 2,
            title: 'Yoga Flow & Meditation',
            instructor: 'Michael Chen',
            participants: 156,
            duration: '45 min',
            difficulty: 'Beginner',
            isLive: true,
            imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop&auto=format&q=80',
          },
        ]);

        setUpcomingClasses([
          {
            id: 3,
            title: 'Strength Training Bootcamp',
            instructor: 'David Wilson',
            startTime: '2:00 PM',
            duration: '60 min',
            difficulty: 'Advanced',
            maxParticipants: 50,
            registered: 32,
            imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop&auto=format&q=80',
          },
          {
            id: 4,
            title: 'Dance Fitness Party',
            instructor: 'Emma Rodriguez',
            startTime: '4:30 PM',
            duration: '45 min',
            difficulty: 'All Levels',
            maxParticipants: 100,
            registered: 78,
            imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop&auto=format&q=80',
          },
          {
            id: 5,
            title: 'Pilates Core Focus',
            instructor: 'Lisa Thompson',
            startTime: '6:00 PM',
            duration: '30 min',
            difficulty: 'Intermediate',
            maxParticipants: 30,
            registered: 15,
            imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop&auto=format&q=80',
          },
        ]);

        setLoading(false);
      }, 1500);
    } catch (error) {
      console.error('Error loading virtual classes:', error);
      setLoading(false);
    }
  };

  const joinLiveClass = (classItem) => {
    Alert.alert(
      '🎥 Join Live Class',
      `Join "${classItem.title}" with ${classItem.instructor}?\n\n👥 ${classItem.participants} participants currently active\n⏱️ ${classItem.duration} remaining`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Join Now', 
          onPress: () => {
            Alert.alert(
              '🚀 Joining Class...',
              'You are now connected to the live virtual class! Follow the instructor and enjoy your workout.',
              [
                { 
                  text: 'Start Workout', 
                  onPress: () => {
                    Alert.alert(
                      '🎉 Workout Started!',
                      'Your live virtual class workout has begun! Follow the instructor and give it your best effort.',
                      [
                        { 
                          text: 'Let\'s Go!', 
                          onPress: () => {
                            // Simulate starting the actual workout
                            console.log('Live class workout started:', classItem.title);
                            Alert.alert(
                              '🏃‍♂️ Workout in Progress',
                              `You're now actively participating in "${classItem.title}"!\n\n• Follow the instructor's guidance\n• Keep your camera on for form feedback\n• Stay hydrated and take breaks when needed\n• Enjoy your workout!`,
                              [{ text: 'Got it!', style: 'default' }]
                            );
                          }
                        }
                      ]
                    );
                  }
                }
              ]
            );
          }
        }
      ]
    );
  };

  const registerForClass = (classItem) => {
    setSelectedClass(classItem);
    setRegistrationModalVisible(true);
  };

  const confirmRegistration = async () => {
    try {
      if (!user?.email) {
        Alert.alert('Error', 'Please login to register for classes');
        return;
      }

      // Save registration to AsyncStorage
      const registrationKey = `@class_registrations:${user.email}`;
      const existingRegistrations = await AsyncStorage.getItem(registrationKey);
      const registrations = existingRegistrations ? JSON.parse(existingRegistrations) : [];
      
      const newRegistration = {
        classId: selectedClass.id,
        className: selectedClass.title,
        instructor: selectedClass.instructor,
        startTime: selectedClass.startTime,
        registeredAt: new Date().toISOString(),
        status: 'registered'
      };

      registrations.push(newRegistration);
      await AsyncStorage.setItem(registrationKey, JSON.stringify(registrations));

      // Update registered classes state
      setRegisteredClasses(prev => [...prev, selectedClass.id]);

      // Update the registered count
      setUpcomingClasses(prev => 
        prev.map(cls => 
          cls.id === selectedClass.id 
            ? { ...cls, registered: cls.registered + 1 }
            : cls
        )
      );

      setRegistrationModalVisible(false);
      
      Alert.alert(
        '✅ Registration Successful!',
        `You're registered for "${selectedClass.title}" at ${selectedClass.startTime}.\n\nWe'll send you a reminder 15 minutes before the class starts.`,
        [
          { 
            text: 'Great!', 
            onPress: () => {
              Alert.alert(
                '🎉 All Set!',
                'Your class registration is confirmed. Check your notifications for updates.',
                [{ text: 'Perfect!', style: 'default' }]
              );
            }
          }
        ]
      );
    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert('Error', 'Failed to register for class. Please try again.');
    }
  };

  const startWorkout = (classItem) => {
    Alert.alert(
      '🚀 Start Workout',
      `Ready to start "${classItem.title}" with ${classItem.instructor}?\n\nDuration: ${classItem.duration}\nDifficulty: ${classItem.difficulty}`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Start Now', 
          onPress: () => {
            Alert.alert(
              '🎉 Workout Started!',
              'Your virtual class workout has begun! Follow the instructor and give it your best effort.',
              [
                { 
                  text: 'Let\'s Go!', 
                  onPress: () => {
                    // Simulate starting the actual workout
                    console.log('Virtual class workout started:', classItem.title);
                    Alert.alert(
                      '🏃‍♂️ Workout in Progress',
                      `You're now actively participating in "${classItem.title}"!\n\n• Follow the instructor's guidance\n• Keep your camera on for form feedback\n• Stay hydrated and take breaks when needed\n• Enjoy your workout!`,
                      [{ text: 'Got it!', style: 'default' }]
                    );
                  }
                }
              ]
            );
          }
        }
      ]
    );
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'beginner': return '#00C853';
      case 'intermediate': return '#FFAB00';
      case 'advanced': return '#D50000';
      default: return '#1E90FF';
    }
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.container}>
        <LinearGradient 
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} 
          style={StyleSheet.absoluteFill}
        >
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#ffffff" />
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        </LinearGradient>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar style="light" />
        <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#ffffff" />
            <Text style={styles.loadingText}>Loading virtual classes...</Text>
          </View>
        </LinearGradient>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* Background */}
      <LinearGradient 
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} 
        style={styles.backgroundGradient}
      >
        <View style={styles.circle1} />
        <View style={styles.circle2} />
        <View style={styles.circle3} />
      </LinearGradient>
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>📹 Live Virtual Classes</Text>
      </View>

      {/* Styled Back Button */}
      <Animated.View entering={FadeInLeft.delay(100)} style={styles.backWrapper}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
      </Animated.View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Live Classes Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔴 Live Now</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.horizontalScroll}
            contentContainerStyle={styles.horizontalScrollContent}
          >
            {liveClasses.map((classItem) => (
              <Animated.View 
                key={classItem.id} 
                entering={FadeInRight.delay(classItem.id * 100)}
                style={styles.liveClassContainer}
              >
                <LinearGradient
                  colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.8)']}
                  style={styles.liveClassCard}
                >
                  <View style={styles.liveIndicator}>
                    <View style={styles.liveDot} />
                    <Text style={styles.liveText}>LIVE</Text>
                  </View>
                  
                  <Text style={styles.classTitle}>{classItem.title}</Text>
                  <Text style={styles.instructorName}>with {classItem.instructor}</Text>
                  
                  <View style={styles.classInfo}>
                    <Text style={styles.classInfoText}>👥 {classItem.participants} joined</Text>
                    <Text style={styles.classInfoText}>⏱️ {classItem.duration}</Text>
                    <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(classItem.difficulty) }]}>
                      <Text style={styles.difficultyText}>{classItem.difficulty}</Text>
                    </View>
                  </View>

                  <TouchableOpacity 
                    style={styles.joinButton}
                    onPress={() => joinLiveClass(classItem)}
                  >
                    <Text style={styles.joinButtonText}>Join Live Class</Text>
                  </TouchableOpacity>
                </LinearGradient>
              </Animated.View>
            ))}
          </ScrollView>
        </View>

        {/* Upcoming Classes Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📅 Upcoming Classes</Text>
          {upcomingClasses.map((classItem, index) => (
            <Animated.View 
              key={classItem.id} 
              entering={FadeInRight.delay(index * 100)}
              style={styles.upcomingClassCard}
            >
              <View style={styles.classHeader}>
                <Text style={styles.upcomingClassTitle}>{classItem.title}</Text>
                <Text style={styles.classTime}>{classItem.startTime}</Text>
              </View>
              
              <Text style={styles.upcomingInstructor}>with {classItem.instructor}</Text>
              
              <View style={styles.classDetails}>
                <Text style={styles.classDetailText}>⏱️ {classItem.duration}</Text>
                <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(classItem.difficulty) }]}>
                  <Text style={styles.difficultyText}>{classItem.difficulty}</Text>
                </View>
              </View>

              <View style={styles.registrationInfo}>
                <Text style={styles.spotsText}>
                  {classItem.registered}/{classItem.maxParticipants} spots taken
                </Text>
                <View style={styles.progressBar}>
                  <View 
                    style={[
                      styles.progressFill, 
                      { width: `${(classItem.registered / classItem.maxParticipants) * 100}%` }
                    ]} 
                  />
                </View>
              </View>

              <TouchableOpacity 
                style={styles.registerButton}
                onPress={() => registerForClass(classItem)}
              >
                <Text style={styles.registerButtonText}>Register for Class</Text>
              </TouchableOpacity>
            </Animated.View>
          ))}
        </View>

        {/* Features Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>✨ Virtual Class Features</Text>
          
          <Animated.View entering={FadeInRight.delay(100)} style={styles.featureCard}>
            <Text style={styles.featureIcon}>🎥</Text>
            <Text style={styles.featureTitle}>HD Video Streaming</Text>
            <Text style={styles.featureDescription}>
              Crystal clear video quality with multiple camera angles
            </Text>
          </Animated.View>

          <Animated.View entering={FadeInRight.delay(200)} style={styles.featureCard}>
            <Text style={styles.featureIcon}>👥</Text>
            <Text style={styles.featureTitle}>Interactive Community</Text>
            <Text style={styles.featureDescription}>
              Chat with other participants and get real-time motivation
            </Text>
          </Animated.View>

          <Animated.View entering={FadeInRight.delay(300)} style={styles.featureCard}>
            <Text style={styles.featureIcon}>📊</Text>
            <Text style={styles.featureTitle}>Real-time Tracking</Text>
            <Text style={styles.featureDescription}>
              Monitor your heart rate, calories, and performance metrics
            </Text>
          </Animated.View>

          <Animated.View entering={FadeInRight.delay(400)} style={styles.featureCard}>
            <Text style={styles.featureIcon}>🏆</Text>
            <Text style={styles.featureTitle}>Achievement System</Text>
            <Text style={styles.featureDescription}>
              Earn badges and NFTs for completing virtual classes
            </Text>
          </Animated.View>
        </View>
      </ScrollView>

      {/* Registration Modal */}
      <Modal
        visible={registrationModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <LinearGradient 
            colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} 
            style={styles.modalBackground}
          />
          
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>📅 Register for Class</Text>
            <TouchableOpacity onPress={() => setRegistrationModalVisible(false)}>
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>

          {selectedClass && (
            <ScrollView style={styles.modalContent}>
              <View style={styles.classPreviewCard}>
                <LinearGradient 
                  colors={['rgba(255, 255, 255, 0.15)', 'rgba(255, 255, 255, 0.05)']}
                  style={styles.previewCardGradient}
                >
                  <Text style={styles.previewTitle}>{selectedClass.title}</Text>
                  <Text style={styles.previewInstructor}>with {selectedClass.instructor}</Text>
                  <Text style={styles.previewTime}>{selectedClass.startTime}</Text>
                </LinearGradient>
              </View>

              <View style={styles.classDetailsCard}>
                <Text style={styles.detailsTitle}>Class Details</Text>
                
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Duration:</Text>
                  <Text style={styles.detailValue}>{selectedClass.duration}</Text>
                </View>
                
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Difficulty:</Text>
                  <Text style={styles.detailValue}>{selectedClass.difficulty}</Text>
                </View>
                
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Available Spots:</Text>
                  <Text style={styles.detailValue}>
                    {selectedClass.maxParticipants - selectedClass.registered} remaining
                  </Text>
                </View>
              </View>

              <View style={styles.benefitsCard}>
                <Text style={styles.benefitsTitle}>What You'll Get:</Text>
                <Text style={styles.benefitItem}>• Live instruction from certified trainers</Text>
                <Text style={styles.benefitItem}>• Real-time form corrections</Text>
                <Text style={styles.benefitItem}>• Interactive community chat</Text>
                <Text style={styles.benefitItem}>• Workout recording for later review</Text>
                <Text style={styles.benefitItem}>• Achievement badges and NFTs</Text>
              </View>

              <TouchableOpacity 
                style={styles.confirmButton}
                onPress={confirmRegistration}
              >
                <LinearGradient 
                  colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.8)']}
                  style={styles.confirmButtonGradient}
                >
                  <Text style={styles.confirmButtonText}>✅ Confirm Registration</Text>
                </LinearGradient>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.startWorkoutButton}
                onPress={() => {
                  setRegistrationModalVisible(false);
                  startWorkout(selectedClass);
                }}
              >
                <LinearGradient 
                  colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.8)']}
                  style={styles.startWorkoutButtonGradient}
                >
                  <Text style={styles.startWorkoutButtonText}>🚀 Start Workout Now</Text>
                </LinearGradient>
              </TouchableOpacity>
            </ScrollView>
          )}
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  // Animated Background Elements
  circle1: {
    position: 'absolute',
    top: -100,
    right: -100,
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  circle2: {
    position: 'absolute',
    bottom: -50,
    left: -50,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
  },
  circle3: {
    position: 'absolute',
    top: '40%',
    right: -30,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.06)',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(5),
    paddingVertical: hp(1.5),
    paddingTop: Platform.OS === 'ios' ? hp(6) : hp(4),
  },
  backWrapper: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? hp(6) : hp(4),
    left: wp(5),
    zIndex: 10,
  },
  backButton: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: wp(7),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  placeholder: {
    width: wp(20),
  },
  content: {
    flex: 1,
    paddingHorizontal: wp(5),
    paddingBottom: hp(3),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: hp(2),
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
  },
  section: {
    marginBottom: hp(3),
  },
  sectionTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(2),
  },
  horizontalScroll: {
    marginBottom: hp(2),
  },
  horizontalScrollContent: {
    paddingRight: wp(4),
  },
  liveClassContainer: {
    width: wp(70),
    marginRight: wp(4),
  },
  liveClassCard: {
    padding: wp(5),
    borderRadius: 16,
    minHeight: hp(25),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  liveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(1.5),
  },
  liveDot: {
    width: wp(2),
    height: wp(2),
    borderRadius: wp(1),
    backgroundColor: '#ff4444',
    marginRight: wp(1.5),
  },
  liveText: {
    fontSize: wp(3),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  classTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(1),
  },
  instructorName: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.9,
    marginBottom: hp(1.5),
  },
  classInfo: {
    marginBottom: hp(2),
  },
  classInfoText: {
    fontSize: wp(3),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.8,
    marginBottom: hp(0.5),
  },
  difficultyBadge: {
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.5),
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginTop: hp(0.5),
  },
  difficultyText: {
    fontSize: wp(2.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  joinButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: hp(1.5),
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  joinButtonText: {
    color: '#fff',
    fontSize: wp(3.5),
    fontFamily: 'Poppins_700Bold',
  },
  upcomingClassCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: wp(4),
    marginBottom: hp(2),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  classHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  upcomingClassTitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    flex: 1,
  },
  classTime: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  upcomingInstructor: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.9,
    marginBottom: hp(1.5),
  },
  classDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(1.5),
  },
  classDetailText: {
    fontSize: wp(3),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.8,
    marginRight: wp(3),
  },
  registrationInfo: {
    marginBottom: hp(2),
  },
  spotsText: {
    fontSize: wp(3),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.8,
    marginBottom: hp(0.5),
  },
  progressBar: {
    height: hp(0.5),
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: 'rgba(102, 126, 234, 0.8)',
    borderRadius: 2,
  },
  registerButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: hp(1.5),
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  registerButtonText: {
    color: '#fff',
    fontSize: wp(3.5),
    fontFamily: 'Poppins_700Bold',
  },
  featureCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: wp(4),
    marginBottom: hp(1.5),
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  featureIcon: {
    fontSize: wp(8),
    marginBottom: hp(1),
  },
  featureTitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(1),
    textAlign: 'center',
  },
  featureDescription: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.9,
    textAlign: 'center',
    lineHeight: hp(2.5),
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  modalBackground: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  modalContent: {
    flex: 1,
    padding: wp(4),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: wp(4),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
  },
  modalTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  closeButton: {
    fontSize: wp(6),
    color: '#fff',
    fontFamily: 'Poppins_700Bold',
  },
  classPreviewCard: {
    marginBottom: hp(2.5),
  },
  previewCardGradient: {
    padding: wp(5),
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  previewTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(1),
    textAlign: 'center',
  },
  previewInstructor: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.9,
    marginBottom: hp(0.5),
  },
  previewTime: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.8,
  },
  classDetailsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: wp(4),
    marginBottom: hp(2.5),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  detailsTitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(1.5),
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: hp(1),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  detailLabel: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  detailValue: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  benefitsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: wp(4),
    marginBottom: hp(2.5),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  benefitsTitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(1.5),
  },
  benefitItem: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: hp(1),
    lineHeight: hp(2.5),
  },
  confirmButton: {
    marginBottom: hp(1.5),
  },
  confirmButtonGradient: {
    paddingVertical: hp(2),
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: wp(4),
    fontFamily: 'Poppins_700Bold',
  },
  startWorkoutButton: {
    marginBottom: hp(2.5),
  },
  startWorkoutButtonGradient: {
    paddingVertical: hp(2),
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  startWorkoutButtonText: {
    color: '#fff',
    fontSize: wp(4),
    fontFamily: 'Poppins_700Bold',
  },
});