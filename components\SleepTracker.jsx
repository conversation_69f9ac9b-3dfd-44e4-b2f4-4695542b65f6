import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Modal,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import * as Progress from 'react-native-progress';
import * as Notifications from 'expo-notifications';
import Animated, { FadeInUp } from 'react-native-reanimated';

const SleepTracker = ({ userEmail, onSleepUpdate }) => {
  const [sleepData, setSleepData] = useState({
    bedtime: null,
    wakeTime: null,
    duration: 0,
    quality: 0,
    isTracking: false,
  });
  const [sleepGoal, setSleepGoal] = useState(8); // hours
  const [sleepHistory, setSleepHistory] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [smartAlarmEnabled, setSmartAlarmEnabled] = useState(false);
  const [optimalWakeTime, setOptimalWakeTime] = useState(null);
  const [showMetrics, setShowMetrics] = useState({
    duration: true,
    avg: true,
    quality: true,
  });
  const [metricsOrder, setMetricsOrder] = useState(['duration', 'avg', 'quality']);
  const [darkMode, setDarkMode] = useState(true);
  const [greetingName, setGreetingName] = useState('User');

  useEffect(() => {
    loadSleepData();
    loadSleepHistory();
    // Try to get user name from AsyncStorage
    (async () => {
      let email = userEmail;
      if (email) {
        const userDataString = await AsyncStorage.getItem(`@user_data:${email}`);
        if (userDataString) {
          const userData = JSON.parse(userDataString);
          setGreetingName(userData.name || 'User');
        }
      }
    })();
  }, []);

  const loadSleepData = async () => {
    try {
      const today = new Date().toDateString();
      const sleepKey = `@sleep:${userEmail}:${today}`;
      const savedSleep = await AsyncStorage.getItem(sleepKey);
      
      if (savedSleep) {
        const data = JSON.parse(savedSleep);
        setSleepData(data);
      }

      // Load user preferences
      const prefsKey = `@sleep_preferences:${userEmail}`;
      const savedPrefs = await AsyncStorage.getItem(prefsKey);
      
      if (savedPrefs) {
        const prefs = JSON.parse(savedPrefs);
        setSleepGoal(prefs.sleepGoal || 8);
        setSmartAlarmEnabled(prefs.smartAlarmEnabled || false);
      }
    } catch (error) {
      console.error('Error loading sleep data:', error);
    }
  };

  const loadSleepHistory = async () => {
    try {
      const historyKey = `@sleep_history:${userEmail}`;
      const savedHistory = await AsyncStorage.getItem(historyKey);
      
      if (savedHistory) {
        const history = JSON.parse(savedHistory);
        setSleepHistory(history.slice(-7)); // Last 7 days
      }
    } catch (error) {
      console.error('Error loading sleep history:', error);
    }
  };

  const startSleepTracking = async () => {
    try {
      const bedtime = new Date();
      const newSleepData = {
        ...sleepData,
        bedtime: bedtime.toISOString(),
        isTracking: true,
      };

      setSleepData(newSleepData);
      await saveSleepData(newSleepData);

      // Schedule smart alarm if enabled
      if (smartAlarmEnabled) {
        await scheduleSmartAlarm(bedtime);
      }

      Alert.alert(
        '😴 Sleep Tracking Started',
        `Good night! Sleep tracking started at ${bedtime.toLocaleTimeString()}.\n\n${smartAlarmEnabled ? '⏰ Smart alarm is set to wake you during light sleep.' : ''}`,
        [{ text: 'Sleep well!', style: 'default' }]
      );

    } catch (error) {
      console.error('Error starting sleep tracking:', error);
      Alert.alert('Error', 'Failed to start sleep tracking');
    }
  };

  const stopSleepTracking = async () => {
    try {
      if (!sleepData.bedtime) {
        Alert.alert('Error', 'No sleep session to stop');
        return;
      }

      const wakeTime = new Date();
      const bedtime = new Date(sleepData.bedtime);
      const duration = (wakeTime - bedtime) / (1000 * 60 * 60); // hours

      // Calculate sleep quality based on duration and optimal sleep time
      const quality = calculateSleepQuality(duration, sleepGoal);

      const completedSleep = {
        ...sleepData,
        wakeTime: wakeTime.toISOString(),
        duration: duration,
        quality: quality,
        isTracking: false,
      };

      setSleepData(completedSleep);
      await saveSleepData(completedSleep);
      await addToSleepHistory(completedSleep);

      // Update parent component
      if (onSleepUpdate) {
        onSleepUpdate({
          duration: duration,
          quality: quality,
          bedtime: bedtime,
          wakeTime: wakeTime,
        });
      }

      // Show sleep summary
      showSleepSummary(completedSleep);

    } catch (error) {
      console.error('Error stopping sleep tracking:', error);
      Alert.alert('Error', 'Failed to stop sleep tracking');
    }
  };

  const calculateSleepQuality = (duration, goal) => {
    // Simple quality calculation based on duration vs goal
    const optimalRange = [goal - 1, goal + 1]; // ±1 hour from goal
    
    if (duration >= optimalRange[0] && duration <= optimalRange[1]) {
      return 90 + Math.random() * 10; // 90-100% for optimal sleep
    } else if (duration >= goal - 2 && duration <= goal + 2) {
      return 70 + Math.random() * 20; // 70-90% for good sleep
    } else if (duration >= goal - 3 && duration <= goal + 3) {
      return 50 + Math.random() * 20; // 50-70% for fair sleep
    } else {
      return 20 + Math.random() * 30; // 20-50% for poor sleep
    }
  };

  const saveSleepData = async (data) => {
    try {
      const today = new Date().toDateString();
      const sleepKey = `@sleep:${userEmail}:${today}`;
      const trackerKey = `tracker_${userEmail}_${today}`;
      await AsyncStorage.setItem(sleepKey, JSON.stringify(data));
      // Also update analytics tracker key
      if (data.duration > 0) {
        const trackerSummary = {
          sleep: data.duration,
        };
        await AsyncStorage.mergeItem(trackerKey, JSON.stringify(trackerSummary));
      }
    } catch (error) {
      console.error('Error saving sleep data:', error);
    }
  };

  const addToSleepHistory = async (sleepSession) => {
    try {
      const historyKey = `@sleep_history:${userEmail}`;
      const existingHistory = await AsyncStorage.getItem(historyKey);
      const history = existingHistory ? JSON.parse(existingHistory) : [];
      
      const sessionData = {
        date: new Date().toDateString(),
        bedtime: sleepSession.bedtime,
        wakeTime: sleepSession.wakeTime,
        duration: sleepSession.duration,
        quality: sleepSession.quality,
      };

      history.push(sessionData);
      
      // Keep only last 30 days
      const last30Days = history.slice(-30);
      await AsyncStorage.setItem(historyKey, JSON.stringify(last30Days));
      
      setSleepHistory(last30Days.slice(-7)); // Update state with last 7 days

    } catch (error) {
      console.error('Error adding to sleep history:', error);
    }
  };

  const scheduleSmartAlarm = async (bedtime) => {
    try {
      // Calculate optimal wake time (assuming 90-minute sleep cycles)
      const sleepCycles = Math.floor(sleepGoal / 1.5); // 90-minute cycles
      const optimalSleepDuration = sleepCycles * 1.5;
      
      const wakeTime = new Date(bedtime);
      wakeTime.setHours(wakeTime.getHours() + optimalSleepDuration);
      
      setOptimalWakeTime(wakeTime);

      // Schedule notification for optimal wake time
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🌅 Smart Wake-Up',
          body: 'Good morning! Time to wake up during your light sleep phase.',
          sound: true,
          data: { type: 'smart_alarm' },
        },
        trigger: wakeTime,
      });

      // Schedule backup alarm 30 minutes later
      const backupTime = new Date(wakeTime);
      backupTime.setMinutes(backupTime.getMinutes() + 30);

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '⏰ Backup Alarm',
          body: 'Time to wake up! This is your backup alarm.',
          sound: true,
          data: { type: 'backup_alarm' },
        },
        trigger: backupTime,
      });

    } catch (error) {
      console.error('Error scheduling smart alarm:', error);
    }
  };

  const showSleepSummary = (sleepSession) => {
    const duration = sleepSession.duration || 0;
    const quality = sleepSession.quality || 0;
    
    let qualityText = '';
    if (quality >= 90) qualityText = 'Excellent 😴';
    else if (quality >= 70) qualityText = 'Good 😊';
    else if (quality >= 50) qualityText = 'Fair 😐';
    else qualityText = 'Poor 😴';

    let feedback = '';
    if (duration < sleepGoal - 2) {
      feedback = 'Try to get more sleep tonight for better recovery.';
    } else if (duration > sleepGoal + 2) {
      feedback = 'You might be oversleeping. Consider adjusting your bedtime.';
    } else {
      feedback = 'Great sleep duration! Keep up the good sleep habits.';
    }

    Alert.alert(
      '🌅 Sleep Summary',
      `Sleep Duration: ${(duration || 0).toFixed(1)} hours\nSleep Quality: ${qualityText} (${Math.round(quality || 0)}%)\n\n💡 ${feedback}`,
      [
        { text: 'View Details', onPress: () => setModalVisible(true) },
        { text: 'OK', style: 'default' }
      ]
    );
  };

  const getAverageSleep = () => {
    if (sleepHistory.length === 0) return 0;
    const total = sleepHistory.reduce((sum, session) => sum + (session.duration || 0), 0);
    return total / sleepHistory.length;
  };

  const getAverageQuality = () => {
    if (sleepHistory.length === 0) return 0;
    const total = sleepHistory.reduce((sum, session) => sum + (session.quality || 0), 0);
    return total / sleepHistory.length;
  };

  const getSleepTrend = () => {
    if (sleepHistory.length < 3) return 'Not enough data';
    
    const recent = sleepHistory.slice(-3);
    const earlier = sleepHistory.slice(0, 3);
    
    const recentAvg = recent.reduce((sum, s) => sum + (s.duration || 0), 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, s) => sum + (s.duration || 0), 0) / earlier.length;
    
    if (recentAvg > earlierAvg + 0.5) return '📈 Improving';
    if (recentAvg < earlierAvg - 0.5) return '📉 Declining';
    return '➡️ Stable';
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getQualityColor = (quality) => {
    if (quality >= 90) return '#4CAF50';
    if (quality >= 70) return '#FF9800';
    if (quality >= 50) return '#2196F3';
    return '#F44336';
  };

  function getMotivationalQuote() {
    const quotes = [
      "Rest is just as important as work.",
      "Good sleep fuels great days.",
      "Dream big, sleep well.",
      "Recharge tonight, conquer tomorrow.",
      "Sleep is the best meditation.",
      "A well-rested mind is unstoppable.",
      "Quality sleep, quality life.",
      "Sleep is the golden chain that ties health and our bodies together.",
      "Let your dreams be your wings.",
      "Sleep more, stress less.",
    ];
    const today = new Date().getDate();
    return quotes[today % quotes.length];
  }

  return (
    <View style={[styles.container, { backgroundColor: darkMode ? '#0c0c0c' : '#f8f9fa' }]}>
      {/* Animated Background Elements */}
      <Animated.View entering={FadeInUp.delay(100)} style={{ position: 'absolute', top: 0, left: 0, right: 0, height: 80, zIndex: 0 }}>
        <View style={{ position: 'absolute', top: -40, left: -40, width: 120, height: 120, borderRadius: 60, backgroundColor: darkMode ? 'rgba(102,126,234,0.08)' : 'rgba(102,126,234,0.12)' }} />
        <View style={{ position: 'absolute', top: 10, right: -30, width: 80, height: 80, borderRadius: 40, backgroundColor: darkMode ? 'rgba(255,255,255,0.05)' : 'rgba(102,126,234,0.08)' }} />
      </Animated.View>
      {/* Greeting and Motivation */}
      <View style={{ alignItems: 'center', marginTop: 16, marginBottom: 8 }}>
        <Text style={{ fontFamily: 'Poppins_700Bold', fontSize: 20, color: darkMode ? '#fff' : '#222', textAlign: 'center' }}>
          Hello, {greetingName}
        </Text>
        <Text style={{ fontFamily: 'Poppins_400Regular', fontSize: 14, color: darkMode ? 'rgba(255,255,255,0.7)' : '#555', textAlign: 'center', marginTop: 2 }}>
          {getMotivationalQuote()}
        </Text>
        <TouchableOpacity
          style={{ marginTop: 10, backgroundColor: darkMode ? 'rgba(102,126,234,0.15)' : 'rgba(102,126,234,0.08)', borderRadius: 16, paddingHorizontal: 14, paddingVertical: 6 }}
          onPress={() => setDarkMode((d) => !d)}
          activeOpacity={0.85}
        >
          <Text style={{ color: '#667eea', fontFamily: 'Poppins_600SemiBold', fontSize: 14 }}>{darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}</Text>
        </TouchableOpacity>
      </View>
      {/* Dashboard Customization */}
      <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 8 }}>
        {metricsOrder.map((metric) => (
          <TouchableOpacity
            key={metric}
            style={{ marginHorizontal: 6, padding: 6, borderRadius: 8, backgroundColor: showMetrics[metric] ? '#667eea' : 'rgba(102,126,234,0.15)' }}
            onPress={() => setShowMetrics((prev) => ({ ...prev, [metric]: !prev[metric] }))}
          >
            <Text style={{ color: showMetrics[metric] ? '#fff' : '#667eea', fontFamily: 'Poppins_600SemiBold', fontSize: 13 }}>{metric === 'duration' ? 'Last Sleep' : metric === 'avg' ? '7-Day Avg' : 'Avg Quality'}</Text>
          </TouchableOpacity>
        ))}
      </View>
      <LinearGradient colors={darkMode ? ['#667eea', '#764ba2'] : ['#fff', '#e3e6f3']} style={[styles.sleepCard, { backgroundColor: darkMode ? undefined : '#fff' }]}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: darkMode ? '#fff' : '#222' }]}>😴 Sleep Tracker</Text>
          <TouchableOpacity onPress={() => setModalVisible(true)}>
            <Ionicons name="analytics" size={24} color={darkMode ? '#fff' : '#667eea'} />
          </TouchableOpacity>
        </View>
        {/* Current Sleep Status */}
        <View style={styles.statusContainer}>
          {sleepData.isTracking ? (
            <View style={styles.trackingStatus}>
              <View style={styles.sleepingIndicator}>
                <View style={styles.pulseDot} />
                <Text style={[styles.trackingText, { color: darkMode ? '#fff' : '#222' }]}>Sleep tracking active</Text>
              </View>
              <Text style={[styles.bedtimeText, { color: darkMode ? 'rgba(255,255,255,0.8)' : '#555' }]}>Bedtime: {sleepData.bedtime ? formatTime(sleepData.bedtime) : 'Not set'}</Text>
              {optimalWakeTime && (
                <Text style={[styles.wakeTimeText, { color: darkMode ? 'rgba(255,255,255,0.8)' : '#555' }]}>⏰ Optimal wake time: {formatTime(optimalWakeTime.toISOString())}</Text>
              )}
            </View>
          ) : (
            <View style={styles.readyStatus}>
              <Text style={[styles.readyText, { color: darkMode ? '#fff' : '#222' }]}>Ready to track your sleep</Text>
              {(sleepData.duration || 0) > 0 && (
                <Text style={[styles.lastSleepText, { color: darkMode ? 'rgba(255,255,255,0.8)' : '#555' }]}>Last sleep: {(sleepData.duration || 0).toFixed(1)}h ({Math.round(sleepData.quality || 0)}% quality)</Text>
              )}
            </View>
          )}
        </View>
        {/* Sleep Metrics (customizable) */}
        <View style={styles.metricsContainer}>
          {showMetrics.duration && (
            <View style={styles.metric}>
              <Text style={styles.metricValue}>{(sleepData.duration || 0).toFixed(1)}h</Text>
              <Text style={styles.metricLabel}>Last Sleep</Text>
            </View>
          )}
          {showMetrics.avg && (
            <View style={styles.metric}>
              <Text style={styles.metricValue}>{getAverageSleep().toFixed(1)}h</Text>
              <Text style={styles.metricLabel}>7-Day Avg</Text>
            </View>
          )}
          {showMetrics.quality && (
            <View style={styles.metric}>
              <Text style={styles.metricValue}>{Math.round(getAverageQuality())}%</Text>
              <Text style={styles.metricLabel}>Avg Quality</Text>
            </View>
          )}
        </View>
        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <Text style={styles.progressLabel}>Sleep Goal Progress</Text>
          <Progress.Bar
            progress={Math.min((sleepData.duration || 0) / sleepGoal, 1)}
            width={null}
            height={8}
            color="#4CAF50"
            unfilledColor="rgba(255,255,255,0.3)"
            borderWidth={0}
            borderRadius={4}
          />
          <Text style={styles.progressText}>
            {(sleepData.duration || 0).toFixed(1)}h / {sleepGoal}h
          </Text>
        </View>
        {/* Control Buttons */}
        <View style={styles.controlButtons}>
          {!sleepData.isTracking ? (
            <TouchableOpacity style={styles.startButton} onPress={startSleepTracking}>
              <Ionicons name="moon" size={20} color="#fff" />
              <Text style={styles.buttonText}>Start Sleep Tracking</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity style={styles.stopButton} onPress={stopSleepTracking}>
              <Ionicons name="sunny" size={20} color="#fff" />
              <Text style={styles.buttonText}>Wake Up & Stop</Text>
            </TouchableOpacity>
          )}
        </View>
        {/* Smart Alarm Toggle */}
        <TouchableOpacity 
          style={styles.smartAlarmToggle}
          onPress={() => setSmartAlarmEnabled(!smartAlarmEnabled)}
        >
          <Ionicons 
            name={smartAlarmEnabled ? "alarm" : "alarm-outline"} 
            size={20} 
            color={smartAlarmEnabled ? "#4CAF50" : "rgba(255,255,255,0.7)"} 
          />
          <Text style={[
            styles.smartAlarmText,
            smartAlarmEnabled && styles.smartAlarmTextActive
          ]}>
            Smart Alarm {smartAlarmEnabled ? 'ON' : 'OFF'}
          </Text>
        </TouchableOpacity>
      </LinearGradient>
      {/* Sleep Analytics Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>😴 Sleep Analytics</Text>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalContent}>
            {/* Sleep Insights */}
            <View style={styles.insightsSection}>
              <Text style={styles.sectionTitle}>📊 Sleep Insights</Text>
              
              <View style={styles.insightCard}>
                <Text style={styles.insightLabel}>Sleep Trend:</Text>
                <Text style={styles.insightValue}>{getSleepTrend()}</Text>
              </View>

              <View style={styles.insightCard}>
                <Text style={styles.insightLabel}>Average Bedtime:</Text>
                <Text style={styles.insightValue}>
                  {sleepHistory.length > 0 ? 
                    formatTime(sleepHistory[sleepHistory.length - 1].bedtime) : 
                    'No data'
                  }
                </Text>
              </View>

              <View style={styles.insightCard}>
                <Text style={styles.insightLabel}>Sleep Consistency:</Text>
                <Text style={styles.insightValue}>
                  {sleepHistory.length >= 7 ? 'Good' : 'Need more data'}
                </Text>
              </View>
            </View>

            {/* Recent Sleep History */}
            <View style={styles.historySection}>
              <Text style={styles.sectionTitle}>📅 Recent Sleep History</Text>
              
              {sleepHistory.map((session, index) => (
                <View key={index} style={styles.historyItem}>
                  <View style={styles.historyDate}>
                    <Text style={styles.historyDateText}>
                      {new Date(session.date).toLocaleDateString()}
                    </Text>
                  </View>
                  
                  <View style={styles.historyDetails}>
                    <Text style={styles.historyDuration}>
                      {(session.duration || 0).toFixed(1)}h
                    </Text>
                    <View style={[
                      styles.qualityIndicator,
                      { backgroundColor: getQualityColor(session.quality || 0) }
                    ]}>
                      <Text style={styles.qualityText}>
                        {Math.round(session.quality || 0)}%
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>

            {/* Sleep Tips */}
            <View style={styles.tipsSection}>
              <Text style={styles.sectionTitle}>💡 Sleep Tips</Text>
              
              <View style={styles.tipItem}>
                <Text style={styles.tipText}>
                  🌙 Maintain a consistent sleep schedule, even on weekends
                </Text>
              </View>
              
              <View style={styles.tipItem}>
                <Text style={styles.tipText}>
                  📱 Avoid screens 1 hour before bedtime
                </Text>
              </View>
              
              <View style={styles.tipItem}>
                <Text style={styles.tipText}>
                  🌡️ Keep your bedroom cool (60-67°F / 15-19°C)
                </Text>
              </View>
              
              <View style={styles.tipItem}>
                <Text style={styles.tipText}>
                  ☕ Avoid caffeine 6 hours before bedtime
                </Text>
              </View>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  sleepCard: {
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#fff',
  },
  statusContainer: {
    marginBottom: 16,
  },
  trackingStatus: {
    alignItems: 'center',
  },
  sleepingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  pulseDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginRight: 8,
    opacity: 0.8,
  },
  trackingText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  bedtimeText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
    marginBottom: 4,
  },
  wakeTimeText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
  },
  readyStatus: {
    alignItems: 'center',
  },
  readyText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  lastSleepText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  metric: {
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
  },
  metricLabel: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 2,
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressLabel: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  progressText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
  },
  controlButtons: {
    marginBottom: 12,
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.3)',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  stopButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 152, 0, 0.3)',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  smartAlarmToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
  },
  smartAlarmText: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 14,
    fontWeight: '600',
  },
  smartAlarmTextActive: {
    color: '#4CAF50',
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  insightsSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 12,
  },
  insightCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  insightLabel: {
    fontSize: 14,
    color: '#666',
  },
  insightValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  historySection: {
    marginBottom: 24,
  },
  historyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  historyDate: {
    flex: 1,
  },
  historyDateText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
  },
  historyDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  historyDuration: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  qualityIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  qualityText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  tipsSection: {
    marginBottom: 24,
  },
  tipItem: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tipText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
});

export default SleepTracker;