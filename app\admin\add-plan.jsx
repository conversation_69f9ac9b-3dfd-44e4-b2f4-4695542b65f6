import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  TextInput,
  ScrollView,
  Image,
} from 'react-native';
// import { Picker } from '@react-native-picker/picker';
// import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import { Animated } from 'react-native';

export default function AddPlan() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  
  const [planData, setPlanData] = useState({
    title: '',
    description: '',
    days: '',
    calories: '',
    category: 'Diet',
    difficulty: 'Beginner',
    imageKey: 'vegan.png',
    active: true
  });

  // const [fontsLoaded] = useFonts({
  //   Poppins_400Regular,
  //   Poppins_600SemiBold,
  //   Poppins_700Bold,
  // });
  const fontsLoaded = true; // Temporary fix

  const imageOptions = [
    { key: 'fasting.png', label: 'Intermittent Fasting', uri: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=400&h=300&fit=crop&crop=center&auto=format&q=80' },
    { key: 'keto.png', label: 'Keto Diet', uri: 'https://images.unsplash.com/photo-1498837167922-ddd27525d352?w=400&h=300&fit=crop&crop=center&auto=format&q=80' },
    { key: 'lowcarb.png', label: 'Low Carb', uri: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&crop=center&auto=format&q=80' },
    { key: 'vegan.png', label: 'Vegan Diet', uri: 'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=400&h=300&fit=crop&crop=center&auto=format&q=80' },
  ];

  const categories = ['Diet', 'Workout', 'Nutrition', 'Lifestyle', 'Challenge'];
  const difficulties = ['Beginner', 'Intermediate', 'Advanced', 'Expert'];

  const validateForm = () => {
    if (!planData.title.trim()) {
      Alert.alert('Error', 'Please enter a plan title');
      return false;
    }
    if (!planData.description.trim()) {
      Alert.alert('Error', 'Please enter a plan description');
      return false;
    }
    if (!planData.days || parseInt(planData.days) <= 0) {
      Alert.alert('Error', 'Please enter a valid duration in days');
      return false;
    }
    if (!planData.calories || parseInt(planData.calories) <= 0) {
      Alert.alert('Error', 'Please enter valid daily calories');
      return false;
    }
    return true;
  };

  const handleSavePlan = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Get existing plans
      const existingPlansData = await AsyncStorage.getItem('@diet_plans');
      const existingPlans = existingPlansData ? JSON.parse(existingPlansData) : [];

      // Create new plan with unique ID
      const newPlan = {
        ...planData,
        id: Date.now().toString(),
        days: parseInt(planData.days),
        calories: parseInt(planData.calories),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Add to plans array
      const updatedPlans = [...existingPlans, newPlan];

      // Save to storage
      await AsyncStorage.setItem('@diet_plans', JSON.stringify(updatedPlans));

      // Log activity
      const activityLog = {
        action: 'plan_created',
        planTitle: newPlan.title,
        planId: newPlan.id,
        adminAction: true,
        timestamp: new Date().toISOString()
      };

      const existingHistory = await AsyncStorage.getItem('userHistory');
      const history = existingHistory ? JSON.parse(existingHistory) : [];
      history.push(activityLog);
      await AsyncStorage.setItem('userHistory', JSON.stringify(history));

      Alert.alert(
        'Success',
        'Plan created successfully!',
        [
          {
            text: 'Create Another',
            onPress: () => {
              setPlanData({
                title: '',
                description: '',
                days: '',
                calories: '',
                category: 'Diet',
                difficulty: 'Beginner',
                imageKey: 'vegan.png',
                active: true
              });
            }
          },
          {
            text: 'Go Back',
            onPress: () => router.back()
          }
        ]
      );
    } catch (error) {
      console.error('Error saving plan:', error);
      Alert.alert('Error', 'Failed to create plan. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getSelectedImage = () => {
    const selected = imageOptions.find(img => img.key === planData.imageKey);
    return selected ? { uri: selected.uri } : { uri: imageOptions[0].uri };
  };

  if (!fontsLoaded) return null;

  return (
    <>
      <View style={[StyleSheet.absoluteFill, { backgroundColor: '#1a1a2e' }]} />
      <View style={{ flex: 1 }}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Add New Plan</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.form}>
            {/* Plan Image Preview */}
            <View style={styles.imageSection}>
              <Text style={styles.sectionTitle}>Plan Image</Text>
              <Image source={getSelectedImage()} style={styles.previewImage} />
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imageSelector}>
                {imageOptions.map((image) => (
                  <TouchableOpacity
                    key={image.key}
                    style={[
                      styles.imageOption,
                      planData.imageKey === image.key && styles.selectedImageOption
                    ]}
                    onPress={() => setPlanData({...planData, imageKey: image.key})}
                  >
                    <Image source={{ uri: image.uri }} style={styles.optionImage} />
                    <Text style={styles.imageLabel}>{image.label}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Basic Information */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Basic Information</Text>
              
              <Text style={styles.inputLabel}>Plan Title *</Text>
              <TextInput
                style={styles.textInput}
                value={planData.title}
                onChangeText={(text) => setPlanData({...planData, title: text})}
                placeholder="Enter plan title (e.g., 30-Day Keto Challenge)"
                placeholderTextColor="#7f8c8d"
              />

              <Text style={styles.inputLabel}>Description *</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={planData.description}
                onChangeText={(text) => setPlanData({...planData, description: text})}
                placeholder="Describe the plan, its benefits, and what users can expect..."
                placeholderTextColor="#7f8c8d"
                multiline
                numberOfLines={4}
              />
            </View>

            {/* Plan Details */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Plan Details</Text>
              
              <View style={styles.row}>
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Duration (Days) *</Text>
                  <TextInput
                    style={styles.textInput}
                    value={planData.days}
                    onChangeText={(text) => setPlanData({...planData, days: text})}
                    placeholder="30"
                    placeholderTextColor="#7f8c8d"
                    keyboardType="numeric"
                  />
                </View>
                
                <View style={styles.halfWidth}>
                  <Text style={styles.inputLabel}>Daily Calories *</Text>
                  <TextInput
                    style={styles.textInput}
                    value={planData.calories}
                    onChangeText={(text) => setPlanData({...planData, calories: text})}
                    placeholder="1800"
                    placeholderTextColor="#7f8c8d"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <Text style={styles.inputLabel}>Category</Text>
              <TouchableOpacity
                style={styles.pickerContainer}
                onPress={() => Alert.alert('Category', 'Category selection will be available in the next update')}
              >
                <View style={styles.picker}>
                  <Text style={styles.pickerText}>{planData.category || 'Select Category'}</Text>
                  <Ionicons name="chevron-down" size={20} color="#666" />
                </View>
              </TouchableOpacity>

              <Text style={styles.inputLabel}>Difficulty Level</Text>
              <TouchableOpacity
                style={styles.pickerContainer}
                onPress={() => Alert.alert('Difficulty', 'Difficulty selection will be available in the next update')}
              >
                <View style={styles.picker}>
                  <Text style={styles.pickerText}>{planData.difficulty || 'Select Difficulty'}</Text>
                  <Ionicons name="chevron-down" size={20} color="#666" />
                </View>
              </TouchableOpacity>
            </View>

            {/* Plan Status */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Plan Status</Text>
              <View style={styles.switchContainer}>
                <Text style={styles.switchLabel}>Make plan active immediately</Text>
                <TouchableOpacity
                  style={[styles.switch, planData.active && styles.switchActive]}
                  onPress={() => setPlanData({...planData, active: !planData.active})}
                >
                  <View style={[styles.switchThumb, planData.active && styles.switchThumbActive]} />
                </TouchableOpacity>
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => router.back()}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.saveButton, loading && styles.disabledButton]}
                onPress={handleSavePlan}
                disabled={loading}
              >
                <Text style={styles.saveButtonText}>
                  {loading ? 'Creating...' : 'Create Plan'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 50,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  form: {
    padding: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  imageSection: {
    marginBottom: 30,
  },
  previewImage: {
    width: '100%',
    height: 150,
    borderRadius: 12,
    marginBottom: 15,
  },
  imageSelector: {
    marginBottom: 10,
  },
  imageOption: {
    marginRight: 15,
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedImageOption: {
    borderColor: '#4ECDC4',
  },
  optionImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginBottom: 5,
  },
  imageLabel: {
    fontSize: 10,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    textAlign: 'center',
    width: 60,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ecf0f1',
    borderRadius: 12,
    padding: 15,
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
    marginBottom: 15,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfWidth: {
    width: '48%',
  },
  pickerContainer: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ecf0f1',
    borderRadius: 12,
    marginBottom: 15,
  },
  picker: {
    height: 50,
    color: '#2c3e50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
  },
  pickerText: {
    fontSize: 16,
    color: '#2c3e50',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 12,
  },
  switchLabel: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
  },
  switch: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#ecf0f1',
    justifyContent: 'center',
    padding: 2,
  },
  switchActive: {
    backgroundColor: '#4ECDC4',
  },
  switchThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: '#fff',
  },
  switchThumbActive: {
    transform: [{ translateX: 20 }],
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 30,
    marginBottom: 50,
  },
  cancelButton: {
    backgroundColor: '#95a5a6',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 12,
    flex: 1,
    marginRight: 10,
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: '#4ECDC4',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 12,
    flex: 1,
    marginLeft: 10,
  },
  saveButtonText: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    textAlign: 'center',
  },
  disabledButton: {
    backgroundColor: '#bdc3c7',
  },
});