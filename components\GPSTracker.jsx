import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { useColorScheme } from 'react-native';

const { width } = Dimensions.get('window');

const GPSTracker = ({ userEmail, onTrackingUpdate, userName = 'User' }) => {
  const [isTracking, setIsTracking] = useState(false);
  const [location, setLocation] = useState(null);
  const [distance, setDistance] = useState(0);
  const [duration, setDuration] = useState(0);
  const [speed, setSpeed] = useState(0);
  const [calories, setCalories] = useState(0);
  const [route, setRoute] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activityType, setActivityType] = useState('walking'); // walking, running, cycling
  const [showMetrics, setShowMetrics] = useState({
    distance: true,
    duration: true,
    speed: true,
    calories: true,
  });
  const [darkMode, setDarkMode] = useState(false);
  const motivationalQuotes = [
    "Every step is progress!",
    "Push your limits today!",
    "Consistency beats intensity!",
    "You are your only limit!",
    "Stay strong, stay healthy!",
    "Great things never come from comfort zones!",
    "Sweat now, shine later!",
    "Believe in yourself!"
  ];
  const todayQuote = motivationalQuotes[new Date().getDate() % motivationalQuotes.length];

  const locationSubscription = useRef(null);
  const startTime = useRef(null);
  const lastLocation = useRef(null);
  const durationInterval = useRef(null);

  useEffect(() => {
    requestLocationPermission();
    return () => {
      stopTracking();
    };
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Location Permission Required',
          'Please enable location services to track your outdoor activities.',
          [{ text: 'OK' }]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return false;
    }
  };

  const startTracking = async () => {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) return;

    try {
      setLoading(true);
      
      // Get initial location
      const initialLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      setLocation(initialLocation);
      setIsTracking(true);
      setDistance(0);
      setDuration(0);
      setSpeed(0);
      setCalories(0);
      setRoute([initialLocation]);
      
      startTime.current = Date.now();
      lastLocation.current = initialLocation;

      // Start location tracking
      locationSubscription.current = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 1000, // Update every second
          distanceInterval: 1, // Update every meter
        },
        (newLocation) => {
          updateTrackingData(newLocation);
        }
      );

      // Start duration timer
      durationInterval.current = setInterval(() => {
        if (startTime.current) {
          const newDuration = Math.floor((Date.now() - startTime.current) / 1000);
          setDuration(newDuration);
        }
      }, 1000);

      Alert.alert('GPS Tracking Started', `${activityType.charAt(0).toUpperCase() + activityType.slice(1)} tracking is now active!`);
      
    } catch (error) {
      console.error('Error starting GPS tracking:', error);
      Alert.alert('Error', 'Failed to start GPS tracking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const updateTrackingData = (newLocation) => {
    if (!lastLocation.current) return;

    const lastLoc = lastLocation.current;
    const newDistance = calculateDistance(
      lastLoc.coords.latitude,
      lastLoc.coords.longitude,
      newLocation.coords.latitude,
      newLocation.coords.longitude
    );

    // Update distance (convert to kilometers)
    setDistance(prev => prev + newDistance);
    
    // Update route
    setRoute(prev => [...prev, newLocation]);
    
    // Calculate speed (km/h)
    const timeDiff = (newLocation.timestamp - lastLoc.timestamp) / 1000; // seconds
    const currentSpeed = timeDiff > 0 ? (newDistance / timeDiff) * 3.6 : 0; // km/h
    setSpeed(currentSpeed);

    // Calculate calories based on activity type and user weight (assuming 70kg)
    const userWeight = 70; // kg - should be fetched from user profile
    const metValues = {
      walking: 3.8,
      running: 8.0,
      cycling: 6.8,
    };
    
    const met = metValues[activityType] || 3.8;
    const caloriesPerSecond = (met * userWeight * 3.5) / (200 * 60);
    setCalories(prev => prev + caloriesPerSecond);

    // Update location
    setLocation(newLocation);
    lastLocation.current = newLocation;

    // Notify parent component
    if (onTrackingUpdate) {
      onTrackingUpdate({
        distance: distance + newDistance,
        duration,
        speed: currentSpeed,
        calories: calories + caloriesPerSecond,
        location: newLocation,
      });
    }
  };

  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = toRadians(lat2 - lat1);
    const dLon = toRadians(lon2 - lon1);
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in kilometers
  };

  const toRadians = (degrees) => {
    return degrees * (Math.PI / 180);
  };

  const stopTracking = async () => {
    try {
      // Stop location tracking
      if (locationSubscription.current) {
        locationSubscription.current.remove();
        locationSubscription.current = null;
      }

      // Stop duration timer
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
        durationInterval.current = null;
      }

      // Save workout data
      if (isTracking && distance > 0) {
        await saveWorkoutData();
      }

      // Reset state
      setIsTracking(false);
      setSpeed(0);
      startTime.current = null;
      lastLocation.current = null;

      Alert.alert(
        'Workout Complete! 🎉',
        `Great job! Here's your ${activityType} summary:\n\n` +
        `📏 Distance: ${distance.toFixed(2)} km\n` +
        `⏱️ Duration: ${formatDuration(duration)}\n` +
        `🔥 Calories: ${Math.round(calories)}\n` +
        `⚡ Avg Speed: ${(distance / (duration / 3600)).toFixed(1)} km/h`,
        [{ text: 'Awesome!', style: 'default' }]
      );

    } catch (error) {
      console.error('Error stopping GPS tracking:', error);
      Alert.alert('Error', 'Failed to save workout data.');
    }
  };

  const saveWorkoutData = async () => {
    try {
      const today = new Date().toDateString();
      const workoutKey = `workouts_${userEmail}_${today}`;
      const trackerKey = `tracker_${userEmail}_${today}`;
      const workoutData = {
        distance,
        duration,
        speed,
        calories,
        activityType,
        route,
        timestamp: Date.now(),
      };
      // Save workout session
      let existingWorkouts = await AsyncStorage.getItem(workoutKey);
      existingWorkouts = existingWorkouts ? JSON.parse(existingWorkouts) : [];
      existingWorkouts.push(workoutData);
      await AsyncStorage.setItem(workoutKey, JSON.stringify(existingWorkouts));
      // Also update analytics tracker key
      const trackerSummary = {
        distance,
        calories,
      };
      await AsyncStorage.mergeItem(trackerKey, JSON.stringify(trackerSummary));
    } catch (error) {
      console.error('Error saving workout data:', error);
    }
  };

  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatSpeed = (speed) => {
    return speed.toFixed(1);
  };

  const getActivityIcon = (activity) => {
    const icons = {
      walking: 'walk',
      running: 'fitness',
      cycling: 'bicycle',
    };
    return icons[activity] || 'walk';
  };

  const getActivityColor = (activity) => {
    const colors = {
      walking: ['#4ECDC4', '#44A08D'],
      running: ['#FF6B6B', '#EE5A52'],
      cycling: ['#45B7D1', '#2196F3'],
    };
    return colors[activity] || colors.walking;
  };

  return (
    <View style={styles.container}>
      <LinearGradient colors={getActivityColor(activityType)} style={styles.trackerCard}>
        <View style={styles.header}>
          <Text style={styles.title}>🗺️ GPS Activity Tracker</Text>
          <View style={[styles.statusIndicator, isTracking && styles.statusActive]}>
            <Text style={styles.statusText}>
              {isTracking ? '🟢 Tracking' : '⚪ Ready'}
            </Text>
          </View>
        </View>

        {/* Activity Type Selector */}
        <View style={styles.activitySelector}>
          {['walking', 'running', 'cycling'].map((activity) => (
            <TouchableOpacity
              key={activity}
              style={[
                styles.activityButton,
                activityType === activity && styles.activityButtonActive,
              ]}
              onPress={() => !isTracking && setActivityType(activity)}
              disabled={isTracking}
            >
              <Ionicons 
                name={getActivityIcon(activity)} 
                size={20} 
                color={activityType === activity ? '#fff' : 'rgba(255,255,255,0.7)'} 
              />
              <Text style={[
                styles.activityButtonText,
                activityType === activity && styles.activityButtonTextActive,
              ]}>
                {activity.charAt(0).toUpperCase() + activity.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Tracking Stats */}
        <View style={{ position: 'relative', width: '100%' }}>
          {/* Animated Background Elements */}
          <View style={{ position: 'absolute', top: -60, left: -60, width: 120, height: 120, borderRadius: 60, backgroundColor: darkMode ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)' }} />
          <View style={{ position: 'absolute', bottom: -40, right: -40, width: 80, height: 80, borderRadius: 40, backgroundColor: darkMode ? 'rgba(255,255,255,0.07)' : 'rgba(0,0,0,0.07)' }} />
          <View style={{ position: 'absolute', top: '30%', right: -20, width: 40, height: 40, borderRadius: 20, backgroundColor: darkMode ? 'rgba(255,255,255,0.04)' : 'rgba(0,0,0,0.04)' }} />
          {/* Greeting & Quote */}
          <Text style={{ fontSize: 20, fontWeight: '700', color: darkMode ? '#fff' : '#222', marginTop: 12, marginBottom: 2, textAlign: 'center' }}>
            {`Hi, ${userName}!`}
          </Text>
          <Text style={{ fontSize: 14, color: darkMode ? '#fff' : '#444', marginBottom: 8, textAlign: 'center', fontStyle: 'italic' }}>
            {todayQuote}
          </Text>
          {/* Dark/Light Mode Toggle */}
          <TouchableOpacity onPress={() => setDarkMode((d) => !d)} style={{ alignSelf: 'center', marginBottom: 8 }}>
            <Ionicons name={darkMode ? 'moon' : 'sunny'} size={22} color={darkMode ? '#fff' : '#222'} />
          </TouchableOpacity>
          {/* Dashboard Customization: Show/Hide Metrics */}
          <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 8 }}>
            {Object.keys(showMetrics).map((metric) => (
              <TouchableOpacity
                key={metric}
                onPress={() => setShowMetrics((prev) => ({ ...prev, [metric]: !prev[metric] }))}
                style={{ marginHorizontal: 6, padding: 4, borderRadius: 6, backgroundColor: showMetrics[metric] ? (darkMode ? '#333' : '#eee') : 'transparent' }}
              >
                <Text style={{ color: showMetrics[metric] ? (darkMode ? '#fff' : '#222') : (darkMode ? '#aaa' : '#888'), fontSize: 12 }}>
                  {metric.charAt(0).toUpperCase() + metric.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Tracking Stats */}
        <View style={styles.statsContainer}>
          {showMetrics.distance && (
            <Animated.View entering={FadeInUp} style={styles.statItem}>
              <Text style={styles.statValue}>{distance.toFixed(2)}</Text>
              <Text style={styles.statLabel}>km</Text>
            </Animated.View>
          )}
          {showMetrics.duration && (
            <Animated.View entering={FadeInUp} style={styles.statItem}>
              <Text style={styles.statValue}>{formatDuration(duration)}</Text>
              <Text style={styles.statLabel}>time</Text>
            </Animated.View>
          )}
          {showMetrics.speed && (
            <Animated.View entering={FadeInUp} style={styles.statItem}>
              <Text style={styles.statValue}>{formatSpeed(speed)}</Text>
              <Text style={styles.statLabel}>km/h</Text>
            </Animated.View>
          )}
          {showMetrics.calories && (
            <Animated.View entering={FadeInUp} style={styles.statItem}>
              <Text style={styles.statValue}>{Math.round(calories)}</Text>
              <Text style={styles.statLabel}>cal</Text>
            </Animated.View>
          )}
        </View>

        {/* Location Info */}
        {location && (
          <View style={styles.locationInfo}>
            <Ionicons name="location" size={16} color="rgba(255,255,255,0.8)" />
            <Text style={styles.locationText}>
              {location.coords.latitude.toFixed(6)}, {location.coords.longitude.toFixed(6)}
            </Text>
          </View>
        )}

        {/* Control Buttons */}
        <View style={styles.controlButtons}>
          {!isTracking ? (
            <TouchableOpacity
              style={[styles.controlButton, loading && styles.controlButtonDisabled]}
              onPress={startTracking}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <Ionicons name="play" size={20} color="#fff" />
                  <Text style={styles.controlButtonText}>Start Tracking</Text>
                </>
              )}
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[styles.controlButton, styles.stopButton]}
              onPress={stopTracking}
            >
              <Ionicons name="stop" size={20} color="#fff" />
              <Text style={styles.controlButtonText}>Stop & Save</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Real-time Feedback */}
        {isTracking && (
          <View style={styles.feedbackContainer}>
            <Text style={styles.feedbackText}>
              {speed > 0 ? 
                `Great pace! You're ${activityType === 'running' ? 'running' : activityType === 'cycling' ? 'cycling' : 'walking'} at ${formatSpeed(speed)} km/h` :
                'Keep moving! GPS is tracking your activity.'
              }
            </Text>
          </View>
        )}
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  trackerCard: {
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#fff',
  },
  statusIndicator: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusActive: {
    backgroundColor: 'rgba(76, 175, 80, 0.3)',
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  activitySelector: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  activityButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(255,255,255,0.2)',
    gap: 4,
  },
  activityButtonActive: {
    backgroundColor: 'rgba(255,255,255,0.3)',
  },
  activityButtonText: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.7)',
    fontWeight: '600',
  },
  activityButtonTextActive: {
    color: '#fff',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 2,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  locationText: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    fontFamily: 'monospace',
  },
  controlButtons: {
    marginBottom: 8,
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  controlButtonDisabled: {
    opacity: 0.6,
  },
  stopButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.3)',
  },
  controlButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  feedbackContainer: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    padding: 12,
    borderRadius: 8,
  },
  feedbackText: {
    color: '#fff',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default GPSTracker;