import { View, Text, ScrollView, StatusBar, Image, TouchableOpacity, SafeAreaView, Dimensions } from 'react'
import React, { useEffect, useState } from 'react'
import { useLocalSearchParams, useRouter } from 'expo-router'
// import { LinearGradient } from 'expo-linear-gradient';
// import { useFonts, Poppins_400Regular, Poppins_700Bold, Poppins_600SemiBold } from '@expo-google-fonts/poppins';

import Ionicons from '@expo/vector-icons/Ionicons';
import { demoExercises } from '../constants/data';
const { width, height } = Dimensions.get('window');

const Exercises = () => {
    const router = useRouter();
    const [exercises, setExercises] = useState(demoExercises);
    const item = useLocalSearchParams();

    // const [fontsLoaded] = useFonts({
    //     Poppins_400Regular,
    //     Poppins_700Bold,
    //     Poppins_600SemiBold,
    // });

    // if (!fontsLoaded) return null;

    return (
        <View style={{ flex: 1, backgroundColor: '#1a1a2e' }}>
            <StatusBar style="light" />
            <SafeAreaView style={{ flex: 1 }}>
                <ScrollView showsVerticalScrollIndicator={false}>
                    <View style={{ position: 'relative' }}>
                        <Image
                            source={item.image}
                            style={{ width: width * (100 / 100), height: height * (45 / 100) }}
                            className="rounded-b-3xl"
                        />
                        <TouchableOpacity
                            style={{
                                position: 'absolute',
                                top: height * (7 / 100),
                                left: 20,
                                height: height * (5.5 / 100),
                                width: height * (5.5 / 100),
                                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                borderRadius: height * (2.75 / 100),
                                justifyContent: 'center',
                                alignItems: 'center',
                            }}
                            onPress={() => router.back()}
                        >
                            <Ionicons name="arrow-back" size={height * (4 / 100)} color="white" />
                        </TouchableOpacity>
                    </View>

                    <View style={{ paddingHorizontal: 20, paddingTop: 20 }}>
                        <Text style={{
                            fontSize: height * (3 / 100),
                            fontFamily: 'Poppins_700Bold',
                            color: '#fff',
                            marginBottom: 15,
                        }}>
                            {item.name} exercises
                        </Text>
                        <View style={{ marginBottom: 50 }}>
                            <View style={{flex:1, justifyContent:'center', alignItems:'center'}}>
                                <Text style={{color:'#fff', fontFamily:'Poppins_600SemiBold', fontSize:18}}>
                                    Exercise list is currently unavailable.
                                </Text>
                            </View>
                        </View>
                    </View>
                </ScrollView>
            </SafeAreaView>
        </View>
    );
}

export default Exercises;