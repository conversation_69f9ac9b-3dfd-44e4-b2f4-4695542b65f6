import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  SafeAreaView,
  StatusBar,
  Animated as RNAnimated,
  Dimensions,
  TextInput,
  Modal,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { Poppins_400Regular, Poppins_700Bold, Poppins_600SemiBold, useFonts } from '@expo-google-fonts/poppins';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useUser } from '../context/UserContext';

const { width, height } = Dimensions.get('window');

const AdvancedParticle = ({ style }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const speed = 4000 + Math.random() * 2000;
    const delay = Math.random() * 3000;

    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(particleAnim, { toValue: 1, duration: speed, useNativeDriver: true }),
        RNAnimated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = RNAnimated.sequence([
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: speed - 1000, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);

    const timeoutId = setTimeout(() => {
      animation.start();
      RNAnimated.loop(opacitySequence).start();
    }, delay);
    
    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };

  }, []);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <RNAnimated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

export default function NutritionTracker() {
  // Always call useFonts first to maintain hook order
  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_700Bold,
    Poppins_600SemiBold,
  });

  const router = useRouter();
  const { currentUser } = useUser();
  
  // All useState hooks must be called in the same order every time
  const [nutritionData, setNutritionData] = useState({
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
    sodium: 0,
    water: 0
  });
  
  const [dailyGoals] = useState({
    calories: 2000,
    protein: 150,
    carbs: 250,
    fat: 65,
    fiber: 25,
    sugar: 50,
    sodium: 2300,
    water: 8
  });

  const [showAddFoodModal, setShowAddFoodModal] = useState(false);
  const [selectedFood, setSelectedFood] = useState('');
  const [foodAmount, setFoodAmount] = useState('');
  const [todayDate] = useState(new Date().toISOString().split('T')[0]);

  // New features - all useState hooks
  const [showMealPlanModal, setShowMealPlanModal] = useState(false);
  const [showBarcodeModal, setShowBarcodeModal] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [mealPlan, setMealPlan] = useState({
    breakfast: [],
    lunch: [],
    dinner: [],
    snacks: []
  });
  const [selectedMeal, setSelectedMeal] = useState('breakfast');
  const [waterIntake, setWaterIntake] = useState(0);
  const [showWaterModal, setShowWaterModal] = useState(false);
  const [waterAmount, setWaterAmount] = useState('');

  // All useEffect hooks must be called in the same order
  useEffect(() => {
    if (fontsLoaded) {
      loadNutritionData();
      loadMealPlan();
      loadWaterIntake();
    }
  }, [fontsLoaded]);

  if (!fontsLoaded) {
    return null;
  }

  const handleBack = () => {
    router.back();
  };

  const loadNutritionData = async () => {
    try {
      if (!currentUser?.email) return;
      
      const key = `@nutrition_data:${currentUser.email}:${todayDate}`;
      const data = await AsyncStorage.getItem(key);
      if (data) {
        setNutritionData(JSON.parse(data));
      }
    } catch (error) {
      console.error('Error loading nutrition data:', error);
    }
  };

  const saveNutritionData = async (data) => {
    try {
      if (!currentUser?.email) return;
      
      const key = `@nutrition_data:${currentUser.email}:${todayDate}`;
      await AsyncStorage.setItem(key, JSON.stringify(data));
      setNutritionData(data);
    } catch (error) {
      console.error('Error saving nutrition data:', error);
    }
  };

  const loadMealPlan = async () => {
    try {
      if (!currentUser?.email) return;
      
      const key = `@meal_plan:${currentUser.email}:${todayDate}`;
      const data = await AsyncStorage.getItem(key);
      if (data) {
        setMealPlan(JSON.parse(data));
      }
    } catch (error) {
      console.error('Error loading meal plan:', error);
    }
  };

  const saveMealPlan = async (plan) => {
    try {
      if (!currentUser?.email) return;
      
      const key = `@meal_plan:${currentUser.email}:${todayDate}`;
      await AsyncStorage.setItem(key, JSON.stringify(plan));
      setMealPlan(plan);
    } catch (error) {
      console.error('Error saving meal plan:', error);
    }
  };

  const loadWaterIntake = async () => {
    try {
      if (!currentUser?.email) return;
      
      const key = `@water_intake:${currentUser.email}:${todayDate}`;
      const data = await AsyncStorage.getItem(key);
      if (data) {
        setWaterIntake(JSON.parse(data));
      }
    } catch (error) {
      console.error('Error loading water intake:', error);
    }
  };

  const saveWaterIntake = async (amount) => {
    try {
      if (!currentUser?.email) return;
      
      const key = `@water_intake:${currentUser.email}:${todayDate}`;
      await AsyncStorage.setItem(key, JSON.stringify(amount));
      setWaterIntake(amount);
    } catch (error) {
      console.error('Error saving water intake:', error);
    }
  };

  const addWater = () => {
    const amount = parseFloat(waterAmount);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid water amount');
      return;
    }
    
    const newWaterIntake = waterIntake + amount;
    saveWaterIntake(newWaterIntake);
    setWaterAmount('');
    setShowWaterModal(false);
    Alert.alert('Success', `Added ${amount} glasses of water!`);
  };

  const addFoodToMeal = () => {
    if (!selectedFood || !foodAmount) {
      Alert.alert('Error', 'Please enter both food name and amount');
      return;
    }

    const amount = parseFloat(foodAmount);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    const nutritionValues = getNutritionValues(selectedFood, amount);
    const foodItem = {
      name: selectedFood,
      amount: amount,
      nutrition: nutritionValues,
      timestamp: new Date().toISOString()
    };

    const newMealPlan = {
      ...mealPlan,
      [selectedMeal]: [...mealPlan[selectedMeal], foodItem]
    };

    saveMealPlan(newMealPlan);
    setShowMealPlanModal(false);
    setSelectedFood('');
    setFoodAmount('');
    Alert.alert('Success', `✅ ${selectedFood} added to ${selectedMeal}!`);
  };

  const removeFoodFromMeal = (mealType, index) => {
    const newMealPlan = { ...mealPlan };
    newMealPlan[mealType].splice(index, 1);
    saveMealPlan(newMealPlan);
    Alert.alert('Success', 'Food removed from meal plan!');
  };

  const getMealIcon = (mealType) => {
    switch (mealType) {
      case 'breakfast': return 'sunny-outline';
      case 'lunch': return 'restaurant-outline';
      case 'dinner': return 'moon-outline';
      case 'snacks': return 'cafe-outline';
      default: return 'nutrition-outline';
    }
  };

  const getMealTitle = (mealType) => {
    switch (mealType) {
      case 'breakfast': return 'Breakfast';
      case 'lunch': return 'Lunch';
      case 'dinner': return 'Dinner';
      case 'snacks': return 'Snacks';
      default: return mealType;
    }
  };

  const simulateBarcodeScan = () => {
    const barcodeFoods = [
      { name: 'Chobani Greek Yogurt', calories: 130, protein: 15, carbs: 9, fat: 0, fiber: 0, sugar: 6, sodium: 65 },
      { name: 'Quaker Oats', calories: 150, protein: 6, carbs: 27, fat: 3, fiber: 4, sugar: 1, sodium: 0 },
      { name: 'Banana', calories: 89, protein: 1.1, carbs: 23, fat: 0.3, fiber: 2.6, sugar: 12, sodium: 1 },
      { name: 'Chicken Breast', calories: 165, protein: 31, carbs: 0, fat: 3.6, fiber: 0, sugar: 0, sodium: 74 },
      { name: 'Brown Rice', calories: 216, protein: 5, carbs: 45, fat: 1.8, fiber: 3.5, sugar: 0.4, sodium: 10 },
      { name: 'Salmon Fillet', calories: 208, protein: 25, carbs: 0, fat: 12, fiber: 0, sugar: 0, sodium: 59 },
      { name: 'Broccoli Crown', calories: 34, protein: 2.8, carbs: 7, fat: 0.4, fiber: 2.6, sugar: 1.5, sodium: 33 },
      { name: 'Sweet Potato', calories: 103, protein: 2, carbs: 24, fat: 0.2, fiber: 3.8, sugar: 5.4, sodium: 41 }
    ];

    const randomFood = barcodeFoods[Math.floor(Math.random() * barcodeFoods.length)];
    
    Alert.alert(
      '📱 Barcode Scanned Successfully!',
      `🍽️ Food: ${randomFood.name}\n🔥 Calories: ${randomFood.calories}\n💪 Protein: ${randomFood.protein}g\n🌾 Carbs: ${randomFood.carbs}g\n🥑 Fat: ${randomFood.fat}g\n🌿 Fiber: ${randomFood.fiber}g\n🍯 Sugar: ${randomFood.sugar}g\n🧂 Sodium: ${randomFood.sodium}mg`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: '✅ Add to Tracker', onPress: () => {
          const nutritionValues = {
            calories: randomFood.calories,
            protein: randomFood.protein,
            carbs: randomFood.carbs,
            fat: randomFood.fat,
            fiber: randomFood.fiber,
            sugar: randomFood.sugar,
            sodium: randomFood.sodium
          };

          const newData = {
            ...nutritionData,
            calories: nutritionData.calories + nutritionValues.calories,
            protein: nutritionData.protein + nutritionValues.protein,
            carbs: nutritionData.carbs + nutritionValues.carbs,
            fat: nutritionData.fat + nutritionValues.fat,
            fiber: nutritionData.fiber + nutritionValues.fiber,
            sugar: nutritionData.sugar + nutritionValues.sugar,
            sodium: nutritionData.sodium + nutritionValues.sodium,
          };

          saveNutritionData(newData);
          setShowBarcodeModal(false);
          Alert.alert('Success', `✅ ${randomFood.name} added to your nutrition tracker!`);
        }}
      ]
    );
  };

  const searchFood = () => {
    if (!searchQuery.trim()) {
      Alert.alert('Error', 'Please enter a food name to search');
      return;
    }

    // Enhanced food search with better results
    const foodDatabase = {
      'apple': { name: 'Apple (1 medium)', calories: 95, protein: 0.5, carbs: 25, fat: 0.3, fiber: 4.4, sugar: 19, sodium: 2 },
      'banana': { name: 'Banana (1 medium)', calories: 105, protein: 1.3, carbs: 27, fat: 0.4, fiber: 3.1, sugar: 14, sodium: 1 },
      'chicken': { name: 'Chicken Breast (100g)', calories: 165, protein: 31, carbs: 0, fat: 3.6, fiber: 0, sugar: 0, sodium: 74 },
      'rice': { name: 'Brown Rice (1 cup)', calories: 216, protein: 5, carbs: 45, fat: 1.8, fiber: 3.5, sugar: 0.4, sodium: 10 },
      'salmon': { name: 'Salmon (100g)', calories: 208, protein: 25, carbs: 0, fat: 12, fiber: 0, sugar: 0, sodium: 59 },
      'broccoli': { name: 'Broccoli (1 cup)', calories: 55, protein: 3.7, carbs: 11.2, fat: 0.6, fiber: 5.2, sugar: 2.6, sodium: 33 },
      'oatmeal': { name: 'Oatmeal (1 cup)', calories: 307, protein: 13, carbs: 55, fat: 5, fiber: 8, sugar: 1, sodium: 49 },
      'milk': { name: 'Milk (1 cup)', calories: 103, protein: 8, carbs: 12, fat: 2.4, fiber: 0, sugar: 12, sodium: 105 },
      'egg': { name: 'Egg (1 large)', calories: 74, protein: 6.3, carbs: 0.6, fat: 5, fiber: 0, sugar: 0.4, sodium: 62 },
      'bread': { name: 'Whole Wheat Bread (1 slice)', calories: 69, protein: 3.6, carbs: 12, fat: 1.1, fiber: 1.9, sugar: 1.5, sodium: 118 }
    };

    const searchTerm = searchQuery.toLowerCase();
    const searchResults = Object.entries(foodDatabase)
      .filter(([key, food]) => key.includes(searchTerm) || food.name.toLowerCase().includes(searchTerm))
      .map(([key, food]) => food);

    if (searchResults.length === 0) {
      Alert.alert('No Results', `No foods found matching "${searchQuery}". Try a different search term.`);
      return;
    }

    Alert.alert(
      '🔍 Search Results',
      `Found ${searchResults.length} results for "${searchQuery}"`,
      searchResults.map((food, index) => ({
        text: `${food.name} - ${food.calories} cal`,
        onPress: () => {
          const nutritionValues = {
            calories: food.calories,
            protein: food.protein,
            carbs: food.carbs,
            fat: food.fat,
            fiber: food.fiber,
            sugar: food.sugar,
            sodium: food.sodium
          };

          const newData = {
            ...nutritionData,
            calories: nutritionData.calories + nutritionValues.calories,
            protein: nutritionData.protein + nutritionValues.protein,
            carbs: nutritionData.carbs + nutritionValues.carbs,
            fat: nutritionData.fat + nutritionValues.fat,
            fiber: nutritionData.fiber + nutritionValues.fiber,
            sugar: nutritionData.sugar + nutritionValues.sugar,
            sodium: nutritionData.sodium + nutritionValues.sodium,
          };

          saveNutritionData(newData);
          setShowSearchModal(false);
          setSearchQuery('');
          Alert.alert('Success', `✅ ${food.name} added to your nutrition tracker!`);
        }
      }))
    );
  };

  const addFood = () => {
    if (!selectedFood || !foodAmount) {
      Alert.alert('Error', 'Please enter both food name and amount');
      return;
    }

    const amount = parseFloat(foodAmount);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    // Simulate nutrition values based on food type
    const nutritionValues = getNutritionValues(selectedFood, amount);
    
    const newData = {
      ...nutritionData,
      calories: nutritionData.calories + nutritionValues.calories,
      protein: nutritionData.protein + nutritionValues.protein,
      carbs: nutritionData.carbs + nutritionValues.carbs,
      fat: nutritionData.fat + nutritionValues.fat,
      fiber: nutritionData.fiber + nutritionValues.fiber,
      sugar: nutritionData.sugar + nutritionValues.sugar,
      sodium: nutritionData.sodium + nutritionValues.sodium,
    };

    saveNutritionData(newData);
    setShowAddFoodModal(false);
    setSelectedFood('');
    setFoodAmount('');
    
    Alert.alert('Success', 'Food added to your nutrition tracker!');
  };

  const getNutritionValues = (food, amount) => {
    // Simplified nutrition calculation
    const foodDatabase = {
      'apple': { calories: 52, protein: 0.3, carbs: 14, fat: 0.2, fiber: 2.4, sugar: 10, sodium: 1 },
      'banana': { calories: 89, protein: 1.1, carbs: 23, fat: 0.3, fiber: 2.6, sugar: 12, sodium: 1 },
      'chicken breast': { calories: 165, protein: 31, carbs: 0, fat: 3.6, fiber: 0, sugar: 0, sodium: 74 },
      'rice': { calories: 130, protein: 2.7, carbs: 28, fat: 0.3, fiber: 0.4, sugar: 0.1, sodium: 1 },
      'salmon': { calories: 208, protein: 25, carbs: 0, fat: 12, fiber: 0, sugar: 0, sodium: 59 },
      'broccoli': { calories: 34, protein: 2.8, carbs: 7, fat: 0.4, fiber: 2.6, sugar: 1.5, sodium: 33 },
      'oatmeal': { calories: 307, protein: 13, carbs: 55, fat: 5, fiber: 8, sugar: 1, sodium: 49 },
      'milk': { calories: 42, protein: 3.4, carbs: 5, fat: 1, fiber: 0, sugar: 5, sodium: 44 },
      'egg': { calories: 74, protein: 6.3, carbs: 0.6, fat: 5, fiber: 0, sugar: 0.4, sodium: 62 },
      'bread': { calories: 265, protein: 9, carbs: 49, fat: 3.2, fiber: 2.7, sugar: 5, sodium: 490 }
    };

    const foodKey = food.toLowerCase();
    const baseValues = foodDatabase[foodKey] || foodDatabase['apple'];
    
    return {
      calories: Math.round((baseValues.calories * amount) / 100),
      protein: Math.round((baseValues.protein * amount) / 100 * 10) / 10,
      carbs: Math.round((baseValues.carbs * amount) / 100 * 10) / 10,
      fat: Math.round((baseValues.fat * amount) / 100 * 10) / 10,
      fiber: Math.round((baseValues.fiber * amount) / 100 * 10) / 10,
      sugar: Math.round((baseValues.sugar * amount) / 100 * 10) / 10,
      sodium: Math.round((baseValues.sodium * amount) / 100)
    };
  };

  const getProgressPercentage = (current, goal) => {
    return Math.min((current / goal) * 100, 100);
  };

  const getProgressColor = (percentage) => {
    if (percentage >= 100) return '#FF6B6B';
    if (percentage >= 80) return '#4CAF50';
    if (percentage >= 60) return '#FF9800';
    return '#2196F3';
  };

  const renderNutritionCard = (title, current, goal, unit, icon, iconColor) => {
    const percentage = getProgressPercentage(current, goal);
    const progressColor = getProgressColor(percentage);
    
    return (
      <Animated.View entering={FadeInUp.delay(200)} style={styles.nutritionCard}>
        <View style={styles.cardHeader}>
          <Ionicons name={icon} size={24} color={iconColor} />
          <Text style={styles.cardTitle}>{title}</Text>
        </View>
        
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${percentage}%`, backgroundColor: progressColor }]} />
          </View>
          <Text style={styles.progressText}>{percentage.toFixed(0)}%</Text>
        </View>
        
        <View style={styles.nutritionValues}>
          <Text style={styles.currentValue}>{current}{unit}</Text>
          <Text style={styles.goalValue}>/ {goal}{unit}</Text>
        </View>
      </Animated.View>
    );
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      {[...Array(15)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Nutrition Tracker</Text>
          <TouchableOpacity onPress={() => setShowAddFoodModal(true)} style={styles.addButton}>
            <Ionicons name="add" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header Section */}
          <View style={styles.headerSection}>
            <Text style={styles.headerTitle}>Nutrition Tracker</Text>
            <Text style={styles.headerSubtitle}>Track your daily nutrition and stay healthy</Text>
          </View>

          {/* Quick Stats */}
          <View style={styles.statsSection}>
            <View style={styles.statCard}>
              <Ionicons name="flame-outline" size={24} color="#FF9800" />
              <Text style={styles.statValue}>{nutritionData.calories}</Text>
              <Text style={styles.statLabel}>Calories</Text>
            </View>
            <View style={styles.statCard}>
              <Ionicons name="water-outline" size={24} color="#2196F3" />
              <Text style={styles.statValue}>{waterIntake}</Text>
              <Text style={styles.statLabel}>Water (L)</Text>
            </View>
            <View style={styles.statCard}>
              <Ionicons name="fitness-outline" size={24} color="#4CAF50" />
              <Text style={styles.statValue}>{nutritionData.protein}g</Text>
              <Text style={styles.statLabel}>Protein</Text>
            </View>
          </View>

          {/* Water Tracking */}
          <View style={styles.waterSection}>
            <View style={styles.waterHeader}>
              <Text style={styles.sectionTitle}>Water Intake</Text>
              <TouchableOpacity style={styles.addWaterButton} onPress={() => setShowWaterModal(true)}>
                <Ionicons name="add-circle-outline" size={24} color="#2196F3" />
              </TouchableOpacity>
            </View>
            <View style={styles.waterProgress}>
              <View style={styles.waterProgressBar}>
                <View 
                  style={[
                    styles.waterProgressFill, 
                    { width: `${Math.min((waterIntake / dailyGoals.water) * 100, 100)}%` }
                  ]} 
                />
              </View>
              <Text style={styles.waterProgressText}>
                {waterIntake}L / {dailyGoals.water}L
              </Text>
            </View>
          </View>

          {/* Nutrition Summary */}
          <View style={styles.nutritionSection}>
            <Text style={styles.sectionTitle}>Today's Nutrition</Text>
            <View style={styles.nutritionGrid}>
              {renderNutritionCard('Calories', nutritionData.calories, dailyGoals.calories, 'cal', 'flame-outline', '#FF9800')}
              {renderNutritionCard('Protein', nutritionData.protein, dailyGoals.protein, 'g', 'fitness-outline', '#4CAF50')}
              {renderNutritionCard('Carbs', nutritionData.carbs, dailyGoals.carbs, 'g', 'leaf-outline', '#8BC34A')}
              {renderNutritionCard('Fat', nutritionData.fat, dailyGoals.fat, 'g', 'nutrition-outline', '#FF5722')}
              {renderNutritionCard('Fiber', nutritionData.fiber, dailyGoals.fiber, 'g', 'leaf-outline', '#795548')}
              {renderNutritionCard('Sugar', nutritionData.sugar, dailyGoals.sugar, 'g', 'cafe-outline', '#E91E63')}
            </View>
          </View>

          {/* Meal Planning */}
          <View style={styles.mealSection}>
            <View style={styles.mealHeader}>
              <Text style={styles.sectionTitle}>Meal Planning</Text>
              <TouchableOpacity style={styles.addMealButton} onPress={() => setShowMealPlanModal(true)}>
                <Ionicons name="add-circle-outline" size={24} color="#667eea" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.mealTabs}>
              {['breakfast', 'lunch', 'dinner', 'snacks'].map((meal) => (
                <TouchableOpacity
                  key={meal}
                  style={[styles.mealTab, selectedMeal === meal && styles.mealTabActive]}
                  onPress={() => setSelectedMeal(meal)}
                >
                  <Ionicons 
                    name={getMealIcon(meal)} 
                    size={20} 
                    color={selectedMeal === meal ? '#667eea' : '#aaa'} 
                  />
                  <Text style={[styles.mealTabText, selectedMeal === meal && styles.mealTabTextActive]}>
                    {getMealTitle(meal)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.mealContent}>
              {mealPlan[selectedMeal].length === 0 ? (
                <View style={styles.emptyMeal}>
                  <Ionicons name="restaurant-outline" size={48} color="rgba(255,255,255,0.3)" />
                  <Text style={styles.emptyMealText}>No foods added yet</Text>
                  <TouchableOpacity 
                    style={styles.addFoodButton}
                    onPress={() => setShowAddFoodModal(true)}
                  >
                    <Text style={styles.addFoodButtonText}>Add Food</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.mealList}>
                  {mealPlan[selectedMeal].map((food, index) => (
                    <View key={index} style={styles.mealItem}>
                      <View style={styles.mealItemInfo}>
                        <Text style={styles.mealItemName}>{food.name}</Text>
                        <Text style={styles.mealItemAmount}>{food.amount}g</Text>
                      </View>
                      <View style={styles.mealItemNutrition}>
                        <Text style={styles.mealItemCalories}>{food.calories} cal</Text>
                        <TouchableOpacity 
                          style={styles.removeFoodButton}
                          onPress={() => removeFoodFromMeal(selectedMeal, index)}
                        >
                          <Ionicons name="close-circle" size={20} color="#FF5722" />
                        </TouchableOpacity>
                      </View>
                    </View>
                  ))}
                  <TouchableOpacity 
                    style={styles.addMoreFoodButton}
                    onPress={() => setShowAddFoodModal(true)}
                  >
                    <Ionicons name="add-circle-outline" size={20} color="#667eea" />
                    <Text style={styles.addMoreFoodText}>Add More Food</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>

          {/* Quick Actions */}
          <View style={styles.quickActionsSection}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <View style={styles.quickActionsGrid}>
              <TouchableOpacity style={styles.quickActionButton} onPress={() => setShowAddFoodModal(true)}>
                <Ionicons name="add-circle-outline" size={24} color="#667eea" />
                <Text style={styles.quickActionText}>Add Food</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.quickActionButton} onPress={() => setShowBarcodeModal(true)}>
                <Ionicons name="barcode-outline" size={24} color="#4CAF50" />
                <Text style={styles.quickActionText}>Scan Barcode</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.quickActionButton} onPress={() => setShowSearchModal(true)}>
                <Ionicons name="search-outline" size={24} color="#FF9800" />
                <Text style={styles.quickActionText}>Search Food</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.quickActionButton} onPress={() => setShowMealPlanModal(true)}>
                <Ionicons name="calendar-outline" size={24} color="#9C27B0" />
                <Text style={styles.quickActionText}>Meal Plan</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>

        {/* Add Food Modal */}
        <Modal
          visible={showAddFoodModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowAddFoodModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Add Food</Text>
                <TouchableOpacity onPress={() => setShowAddFoodModal(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              
              <TextInput
                style={styles.foodInput}
                placeholder="Food name (e.g., apple, chicken breast)"
                placeholderTextColor="rgba(255,255,255,0.5)"
                value={selectedFood}
                onChangeText={setSelectedFood}
              />
              
              <TextInput
                style={styles.foodInput}
                placeholder="Amount in grams"
                placeholderTextColor="rgba(255,255,255,0.5)"
                value={foodAmount}
                onChangeText={setFoodAmount}
                keyboardType="numeric"
              />
              
              <TouchableOpacity style={styles.addFoodButton} onPress={addFood}>
                <Text style={styles.addFoodButtonText}>Add Food</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>

        {/* Meal Planning Modal */}
        <Modal
          visible={showMealPlanModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowMealPlanModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Meal Planning</Text>
                <TouchableOpacity onPress={() => setShowMealPlanModal(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.mealSelector}>
                <TouchableOpacity 
                  style={[styles.mealOption, selectedMeal === 'breakfast' && styles.mealOptionSelected]}
                  onPress={() => setSelectedMeal('breakfast')}
                >
                  <Text style={styles.mealOptionText}>Breakfast</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.mealOption, selectedMeal === 'lunch' && styles.mealOptionSelected]}
                  onPress={() => setSelectedMeal('lunch')}
                >
                  <Text style={styles.mealOptionText}>Lunch</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.mealOption, selectedMeal === 'dinner' && styles.mealOptionSelected]}
                  onPress={() => setSelectedMeal('dinner')}
                >
                  <Text style={styles.mealOptionText}>Dinner</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.mealOption, selectedMeal === 'snacks' && styles.mealOptionSelected]}
                  onPress={() => setSelectedMeal('snacks')}
                >
                  <Text style={styles.mealOptionText}>Snacks</Text>
                </TouchableOpacity>
              </View>
              
              <TextInput
                style={styles.foodInput}
                placeholder="Food name"
                placeholderTextColor="rgba(255,255,255,0.5)"
                value={selectedFood}
                onChangeText={setSelectedFood}
              />
              
              <TextInput
                style={styles.foodInput}
                placeholder="Amount in grams"
                placeholderTextColor="rgba(255,255,255,0.5)"
                value={foodAmount}
                onChangeText={setFoodAmount}
                keyboardType="numeric"
              />
              
              <TouchableOpacity style={styles.addFoodButton} onPress={addFoodToMeal}>
                <Text style={styles.addFoodButtonText}>Add to {selectedMeal}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>

        {/* Barcode Scan Modal */}
        <Modal
          visible={showBarcodeModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowBarcodeModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Barcode Scanner</Text>
                <TouchableOpacity onPress={() => setShowBarcodeModal(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.barcodeContainer}>
                <Ionicons name="scan" size={80} color="#667eea" />
                <Text style={styles.barcodeText}>Simulated Barcode Scan</Text>
                <Text style={styles.barcodeSubtext}>Tap to scan a random food item</Text>
              </View>
              
              <TouchableOpacity style={styles.addFoodButton} onPress={simulateBarcodeScan}>
                <Text style={styles.addFoodButtonText}>Scan Barcode</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>

        {/* Food Search Modal */}
        <Modal
          visible={showSearchModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowSearchModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Food Search</Text>
                <TouchableOpacity onPress={() => setShowSearchModal(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              
              <TextInput
                style={styles.foodInput}
                placeholder="Search for food items..."
                placeholderTextColor="rgba(255,255,255,0.5)"
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
              
              <TouchableOpacity style={styles.addFoodButton} onPress={searchFood}>
                <Text style={styles.addFoodButtonText}>Search Food</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>

        {/* Water Tracker Modal */}
        <Modal
          visible={showWaterModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowWaterModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Water Intake</Text>
                <TouchableOpacity onPress={() => setShowWaterModal(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.waterModalContent}>
                <Ionicons name="water" size={60} color="#667eea" />
                <Text style={styles.waterModalText}>Current: {waterIntake} glasses</Text>
                <Text style={styles.waterModalSubtext}>Goal: {dailyGoals.water} glasses</Text>
              </View>
              
              <TextInput
                style={styles.foodInput}
                placeholder="Number of glasses"
                placeholderTextColor="rgba(255,255,255,0.5)"
                value={waterAmount}
                onChangeText={setWaterAmount}
                keyboardType="numeric"
              />
              
              <TouchableOpacity style={styles.addFoodButton} onPress={addWater}>
                <Text style={styles.addFoodButtonText}>Add Water</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0c0c0c',
  },
  loadingText: {
    color: '#fff',
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(102, 126, 234, 0.6)',
    borderRadius: 2,
    zIndex: 0,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'android' ? 25 : 10,
    paddingBottom: 15,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  headerSection: {
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.7)',
    textAlign: 'center',
  },
  statsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  statCard: {
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 12,
    padding: 15,
    width: '30%',
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.1)',
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginTop: 5,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
  },
  waterSection: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  waterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 10,
  },
  addWaterButton: {
    padding: 5,
  },
  waterProgress: {
    alignItems: 'center',
  },
  waterProgressBar: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 4,
    marginBottom: 8,
  },
  waterProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  waterProgressText: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  nutritionSection: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  nutritionCard: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 12,
    padding: 15,
    width: '48%',
    marginBottom: 15,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.1)',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginLeft: 8,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 2,
    marginRight: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  nutritionValues: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  currentValue: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  goalValue: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
    marginLeft: 4,
  },
  mealSection: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  mealHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  addMealButton: {
    padding: 5,
  },
  mealTabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
  },
  mealTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  mealTabActive: {
    backgroundColor: '#667eea',
    borderColor: '#667eea',
  },
  mealTabText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#aaa',
    marginLeft: 8,
  },
  mealTabTextActive: {
    color: '#fff',
  },
  mealContent: {
    //
  },
  emptyMeal: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  emptyMealText: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
    marginTop: 15,
  },
  mealList: {
    //
  },
  mealItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.1)',
  },
  mealItemInfo: {
    flex: 1,
  },
  mealItemName: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginBottom: 2,
  },
  mealItemAmount: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.7)',
  },
  mealItemNutrition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  mealItemCalories: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#FF9800',
    marginRight: 10,
  },
  removeFoodButton: {
    padding: 5,
  },
  addMoreFoodButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 15,
    backgroundColor: 'rgba(102,126,234,0.1)',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  addMoreFoodText: {
    color: '#667eea',
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    marginLeft: 8,
  },
  quickActionsSection: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%',
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 12,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.1)',
  },
  quickActionText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#1a1a2e',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  foodInput: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  addFoodButton: {
    backgroundColor: '#667eea',
    borderRadius: 12,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginTop: 15,
  },
  addFoodButtonText: {
    color: '#fff',
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
  },
  mealSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  mealOption: {
    width: '48%',
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.1)',
  },
  mealOptionSelected: {
    backgroundColor: '#667eea',
    borderColor: '#667eea',
  },
  mealOptionText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  barcodeContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  barcodeText: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginTop: 10,
  },
  barcodeSubtext: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
    marginTop: 5,
  },
  waterModalContent: {
    alignItems: 'center',
    marginBottom: 20,
  },
  waterModalText: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginTop: 10,
  },
  waterModalSubtext: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
    marginTop: 5,
  },
  waterProgressFill: {
    backgroundColor: '#2196F3',
    height: '100%',
    borderRadius: 4,
  },
  // Add missing styles
  mealContent: {
    marginTop: 15,
  },
  mealList: {
    //
  },
}); 