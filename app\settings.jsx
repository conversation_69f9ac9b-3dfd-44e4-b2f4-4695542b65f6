// This file is used in the project. Do NOT delete.
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
  Linking,
  Share,
  Dimensions,
  SafeAreaView,
  StatusBar,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useUser } from '../context/UserContext';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';

const { width, height } = Dimensions.get('window');

const AdvancedParticle = ({ style }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const speed = 4000 + Math.random() * 2000;
    const delay = Math.random() * 3000;

    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, { toValue: 1, duration: speed, useNativeDriver: true }),
        Animated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = Animated.sequence([
      Animated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 1, duration: speed - 1000, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);

    setTimeout(() => {
      animation.start();
      Animated.loop(opacitySequence).start();
    }, delay);

  }, []);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <Animated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

export default function SettingsPage() {
  const router = useRouter();
  const { currentUser: user } = useUser();
  const [settings, setSettings] = useState({
    notifications: true,
    darkMode: false,
    autoSync: true,
    locationTracking: true,
    biometricAuth: false,
    dataSharing: false,
  });

  const handleBack = () => {
    router.back();
  };

  // Load settings from AsyncStorage
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem(`@settings:${user?.email}`);
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const saveSettings = async (newSettings) => {
    try {
      await AsyncStorage.setItem(`@settings:${user?.email}`, JSON.stringify(newSettings));
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  const toggleSetting = (key) => {
    const newSettings = {
      ...settings,
      [key]: !settings[key]
    };
    
    setSettings(newSettings);
    saveSettings(newSettings);
    
    // Handle dark mode toggle with actual functionality
    if (key === 'darkMode') {
      // Apply dark mode theme changes
      const isDarkMode = newSettings.darkMode;
      
      // You can add more dark mode logic here
      // For example, updating the app's theme context
      
      Alert.alert(
        isDarkMode ? '🌙 Dark Mode Enabled' : '☀️ Light Mode Enabled',
        `The app will now use ${isDarkMode ? 'dark' : 'light'} theme.`,
        [{ text: 'OK', style: 'default' }]
      );
    }
  };

  const handleResetData = () => {
    Alert.alert(
      '⚠️ Reset All Data',
      'This will permanently delete all your fitness data, progress, and achievements. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset', 
          style: 'destructive',
          onPress: async () => {
            try {
              // Clear all user data from AsyncStorage
              const keys = await AsyncStorage.getAllKeys();
              const userKeys = keys.filter(key => key.includes(user?.email || 'user'));
              await AsyncStorage.multiRemove(userKeys);
              
              Alert.alert('✅ Data Reset', 'All data has been reset successfully.');
            } catch (error) {
              console.error('Error resetting data:', error);
              Alert.alert('❌ Error', 'Failed to reset data. Please try again.');
            }
          }
        }
      ]
    );
  };

  const handleExportData = async () => {
    try {
      // Collect user data from AsyncStorage
      const keys = await AsyncStorage.getAllKeys();
      const userKeys = keys.filter(key => key.includes(user?.email || 'user'));
      const userData = {};
      
      for (const key of userKeys) {
        const value = await AsyncStorage.getItem(key);
        userData[key] = value;
      }
      
      const exportData = {
        user: user,
        settings: settings,
        userData: userData,
        exportDate: new Date().toISOString(),
        version: '1.0.0'
      };
      
      const dataString = JSON.stringify(exportData, null, 2);
      
      // Use Share API to export data
      await Share.share({
        message: `My Fitness App Data Export\n\nExported on: ${new Date().toLocaleDateString()}\n\nData:\n${dataString}`,
        title: 'Fitness Data Export'
      });
      
      Alert.alert('✅ Export Complete', 'Your data has been exported successfully!');
    } catch (error) {
      console.error('Export error:', error);
      Alert.alert('❌ Export Failed', 'There was an error exporting your data. Please try again.');
    }
  };

  const handleContactSupport = () => {
    Alert.alert(
      '📞 Contact Support',
      'How would you like to contact our support team?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Email', 
          onPress: () => {
            Linking.openURL('mailto:<EMAIL>?subject=Support Request&body=Please describe your issue...');
          }
        },
        { 
          text: 'Phone', 
          onPress: () => {
            Linking.openURL('tel:+1234567890');
          }
        }
      ]
    );
  };

  const handleRateApp = () => {
    Alert.alert(
      '⭐ Rate Our App',
      'We hope you\'re enjoying My Fitness App! Would you like to rate us on the App Store?',
      [
        { text: 'Not Now', style: 'cancel' },
        { 
          text: 'Rate App', 
          onPress: () => {
            // In a real app, you would use the actual App Store URL
            Linking.openURL('https://apps.apple.com/app/my-fitness-app');
          }
        }
      ]
    );
  };

  const handleHelpCenter = () => {
    Alert.alert(
      '❓ Help Center',
      'Visit our help center for FAQs, tutorials, and troubleshooting guides.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Open Help Center', 
          onPress: () => {
            Linking.openURL('https://help.myfitnessapp.com');
          }
        }
      ]
    );
  };

  const getSettingIcon = (key) => {
    switch(key) {
      case 'notifications': return 'notifications-outline';
      case 'darkMode': return 'moon-outline';
      case 'autoSync': return 'sync-outline';
      case 'locationTracking': return 'location-outline';
      case 'biometricAuth': return 'finger-print-outline';
      case 'dataSharing': return 'share-outline';
      default: return 'settings-outline';
    }
  };

  const getSettingLabel = (key) => {
    switch(key) {
      case 'notifications': return 'Push Notifications';
      case 'darkMode': return 'Dark Mode';
      case 'autoSync': return 'Auto Sync';
      case 'locationTracking': return 'Location Tracking';
      case 'biometricAuth': return 'Biometric Auth';
      case 'dataSharing': return 'Data Sharing';
      default: return key;
    }
  };

  const getSettingDescription = (key) => {
    switch(key) {
      case 'notifications': return 'Receive workout reminders and updates';
      case 'darkMode': return 'Switch to dark theme';
      case 'autoSync': return 'Automatically sync your data';
      case 'locationTracking': return 'Track your workout location';
      case 'biometricAuth': return 'Use fingerprint or face ID';
      case 'dataSharing': return 'Share your progress with friends';
      default: return '';
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      {[...Array(20)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      <View style={[styles.glowCircle, { top: '10%', right: -50 }]} />
      <View style={[styles.glowCircle, { bottom: '15%', left: -50 }]} />
      
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Settings</Text>
          <View style={{ width: 40 }} />
        </View>

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
        >
          {/* Account Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Account</Text>
            <View style={styles.sectionCard}>
              <TouchableOpacity style={styles.menuItem} onPress={() => router.push('profile/profile_updated')}>
                <Ionicons name="person-circle-outline" size={22} color="rgba(255,255,255,0.8)" />
                <Text style={styles.menuItemText}>Edit Profile</Text>
                <Ionicons name="chevron-forward" size={20} color="rgba(255,255,255,0.5)" />
              </TouchableOpacity>
              <TouchableOpacity style={styles.menuItem} onPress={() => {/* Change password logic */}}>
                <Ionicons name="lock-closed-outline" size={22} color="rgba(255,255,255,0.8)" />
                <Text style={styles.menuItemText}>Change Password</Text>
                <Ionicons name="chevron-forward" size={20} color="rgba(255,255,255,0.5)" />
              </TouchableOpacity>
            </View>
          </View>

          {/* General Settings */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>General</Text>
            <View style={styles.sectionCard}>
              {Object.keys(settings).map((key, index) => (
                <View key={key} style={[styles.settingItem, index === Object.keys(settings).length - 1 && styles.noBorder]}>
                  <Ionicons name={getSettingIcon(key)} size={24} color="rgba(255,255,255,0.8)" />
                  <View style={styles.settingTextContainer}>
                    <Text style={styles.settingLabel}>{getSettingLabel(key)}</Text>
                    <Text style={styles.settingDescription}>{getSettingDescription(key)}</Text>
                  </View>
                  <Switch
                    value={settings[key]}
                    onValueChange={() => toggleSetting(key)}
                    trackColor={{ false: 'rgba(255,255,255,0.2)', true: '#8f72db' }}
                    thumbColor={settings[key] ? '#667eea' : '#f4f3f4'}
                  />
                </View>
              ))}
            </View>
          </View>
          
          {/* Data Management */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Data Management</Text>
            <View style={styles.sectionCard}>
              <TouchableOpacity style={styles.menuItem} onPress={handleExportData}>
                <Ionicons name="cloud-download-outline" size={22} color="rgba(255,255,255,0.8)" />
                <Text style={styles.menuItemText}>Export My Data</Text>
                <Ionicons name="chevron-forward" size={20} color="rgba(255,255,255,0.5)" />
              </TouchableOpacity>
              <TouchableOpacity style={[styles.menuItem, styles.noBorder]} onPress={handleResetData}>
                <Ionicons name="trash-outline" size={22} color="#ff6b6b" />
                <Text style={[styles.menuItemText, { color: '#ff6b6b' }]}>Reset All Data</Text>
                <Ionicons name="chevron-forward" size={20} color="#ff6b6b" />
              </TouchableOpacity>
            </View>
          </View>

          {/* About & Support */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About & Support</Text>
            <View style={styles.sectionCard}>
              <TouchableOpacity style={styles.menuItem} onPress={handleHelpCenter}>
                <Ionicons name="help-circle-outline" size={22} color="rgba(255,255,255,0.8)" />
                <Text style={styles.menuItemText}>Help Center</Text>
                <Ionicons name="chevron-forward" size={20} color="rgba(255,255,255,0.5)" />
              </TouchableOpacity>
              <TouchableOpacity style={styles.menuItem} onPress={handleContactSupport}>
                <Ionicons name="mail-outline" size={22} color="rgba(255,255,255,0.8)" />
                <Text style={styles.menuItemText}>Contact Support</Text>
                <Ionicons name="chevron-forward" size={20} color="rgba(255,255,255,0.5)" />
              </TouchableOpacity>
              <TouchableOpacity style={[styles.menuItem, styles.noBorder]} onPress={handleRateApp}>
                <Ionicons name="star-outline" size={22} color="rgba(255,255,255,0.8)" />
                <Text style={styles.menuItemText}>Rate App</Text>
                <Ionicons name="chevron-forward" size={20} color="rgba(255,255,255,0.5)" />
              </TouchableOpacity>
            </View>
          </View>

        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 5,
    zIndex: 0,
  },
  glowCircle: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    zIndex: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 40,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 22,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  scrollContainer: {
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 15,
  },
  sectionCard: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  settingTextContainer: {
    flex: 1,
    marginLeft: 15,
  },
  settingLabel: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  settingDescription: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
    marginTop: 2,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  menuItemText: {
    flex: 1,
    marginLeft: 15,
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
  },
  noBorder: {
    borderBottomWidth: 0,
  },
});