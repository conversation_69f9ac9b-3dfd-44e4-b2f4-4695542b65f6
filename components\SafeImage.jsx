import React, { useState, useEffect } from 'react';
import { Image, View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const SafeImage = ({ source, style, fallbackSource, onError, onLoadStart, onLoadEnd, ...props }) => {
  // Add logging for debugging
  console.log('SafeImage render: source =', source, 'fallbackSource =', fallbackSource);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [currentSource, setCurrentSource] = useState(source);
  
  // Determine the type of content based on the source or style
  const getContentType = () => {
    if (source && typeof source === 'object' && source.uri) {
      const uri = source.uri.toLowerCase();
      if (uri.includes('exercise') || uri.includes('workout')) return 'exercise';
      if (uri.includes('nutrition') || uri.includes('diet')) return 'nutrition';
      if (uri.includes('cardio') || uri.includes('run')) return 'cardio';
      if (uri.includes('strength') || uri.includes('muscle')) return 'strength';
      if (uri.includes('yoga') || uri.includes('flexibility')) return 'yoga';
    }
    return 'default';
  };

  const contentType = getContentType();
  
  const getFallbackStyle = () => {
    const baseStyle = {
      ...style,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: style?.borderRadius || 12,
    };

    switch (contentType) {
      case 'exercise':
        return { ...baseStyle, backgroundColor: '#FF6B6B' };
      case 'nutrition':
        return { ...baseStyle, backgroundColor: '#4ECDC4' };
      case 'cardio':
        return { ...baseStyle, backgroundColor: '#45B7D1' };
      case 'strength':
        return { ...baseStyle, backgroundColor: '#FFA07A' };
      case 'yoga':
        return { ...baseStyle, backgroundColor: '#98D8C8' };
      default:
        return { ...baseStyle, backgroundColor: '#667eea' };
    }
  };

  const getIconName = () => {
    switch (contentType) {
      case 'exercise':
        return 'fitness-outline';
      case 'nutrition':
        return 'nutrition-outline';
      case 'cardio':
        return 'flash-outline';
      case 'strength':
        return 'barbell-outline';
      case 'yoga':
        return 'leaf-outline';
      default:
        return 'image-outline';
    }
  };

  const getIconColor = () => {
    return '#FFFFFF';
  };

  const getIconSize = () => {
    const width = style?.width || 100;
    const height = style?.height || 100;
    const minDimension = Math.min(width, height);
    return Math.max(24, minDimension * 0.3);
  };

  const handleError = (error) => {
    console.error('Image loading error:', error);
    setHasError(true);
    setIsLoading(false);
    
    // Try fallback source if available and different from current
    if (fallbackSource && fallbackSource !== currentSource) {
      setCurrentSource(fallbackSource);
      setHasError(false);
      setIsLoading(true);
    }
    
    if (onError) onError(error);
  };

  const handleLoadStart = () => {
    setIsLoading(true);
    if (onLoadStart) onLoadStart();
  };

  const handleLoadEnd = () => {
    setIsLoading(false);
    if (onLoadEnd) onLoadEnd();
  };

  // Reset error state when source changes
  useEffect(() => {
    setHasError(false);
    setIsLoading(true);
    setCurrentSource(source);
  }, [source]);

  // If we have an error and no fallback, show icon-based fallback
  if (hasError && !fallbackSource) {
    return (
      <View style={getFallbackStyle()}>
        <Ionicons 
          name={getIconName()} 
          size={getIconSize()} 
          color={getIconColor()} 
        />
      </View>
    );
  }

  // If loading, show loading state
  if (isLoading) {
    return (
      <View style={[style, { backgroundColor: '#f0f0f0', justifyContent: 'center', alignItems: 'center' }]}>
        <Ionicons name="image-outline" size={getIconSize()} color="#999" />
      </View>
    );
  }

  return (
    <Image
      source={currentSource}
      style={style}
      onError={handleError}
      onLoadStart={handleLoadStart}
      onLoadEnd={handleLoadEnd}
      {...props}
    />
  );
};

export default SafeImage; 