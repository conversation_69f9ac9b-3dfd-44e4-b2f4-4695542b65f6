<<<<<<<
<<<<<<<



<<<<<<<















{































































  "expo": {































































    "name": "My-fitness",































































    "slug": "My-fitness",































































    "version": "1.0.0",































































    "orientation": "portrait",































































    "icon": "./assets/images/icon.png",































































    "scheme": "myapp",































































    "userInterfaceStyle": "automatic",































































    "newArchEnabled": true,































































    "ios": {































































      "supportsTablet": true































































    },































































    "android": {































































      "adaptiveIcon": {































































        "foregroundImage": "./assets/images/adaptive-icon.png",































































        "backgroundColor": "#ffffff"































































      }































































    },































































    "web": {































































      "bundler": "metro",































































      "output": "static",































































      "favicon": "./assets/images/favicon.png"































































    },































































    "plugins": [































































      "expo-router",































































      [































































        "expo-splash-screen",































































        {































































          "image": "./assets/images/splash-icon.png",































































          "imageWidth": 200,































































          "resizeMode": "contain",































































          "backgroundColor": "#ffffff"































































        }































































      ]































































    ],































































    "experiments": {































































      "typedRoutes": true































































    }































































  }































































}































































=======















{































  "expo": {































    "name": "My-fitness",































    "slug": "My-fitness",































    "version": "1.0.0",































    "orientation": "portrait",































    "icon": "./assets/images/icon.png",































    "scheme": "myapp",































    "userInterfaceStyle": "automatic",































    "newArchEnabled": true,































    "ios": {































      "supportsTablet": true,































      "infoPlist": {































        "NSAppTransportSecurity": {































          "NSAllowsArbitraryLoads": true































        }































      }































    },































    "android": {































      "adaptiveIcon": {































        "foregroundImage": "./assets/images/adaptive-icon.png",































        "backgroundColor": "#ffffff"































      },































      "permissions": [































        "android.permission.INTERNET",































        "android.permission.READ_EXTERNAL_STORAGE",































        "android.permission.WRITE_EXTERNAL_STORAGE"































      ]































    },































    "web": {































      "bundler": "metro",































      "output": "static",































      "favicon": "./assets/images/favicon.png"































    },































    "plugins": [































      "expo-router",































      [































        "expo-splash-screen",































        {































          "image": "./assets/images/splash-icon.png",































          "imageWidth": 200,































          "resizeMode": "contain",































          "backgroundColor": "#ffffff"































        }































      ]































    ],































    "experiments": {































      "typedRoutes": true































    }































  }































}































>>>>>>>















=======



{







  "expo": {







    "name": "My-fitness",







    "slug": "My-fitness",







    "version": "1.0.0",







    "orientation": "portrait",







    "icon": "./assets/images/icon.png",







    "scheme": "myapp",







    "userInterfaceStyle": "automatic",







    "newArchEnabled": true,







    "ios": {







      "supportsTablet": true,







      "infoPlist": {







        "NSAppTransportSecurity": {







          "NSAllowsArbitraryLoads": true







        }







      }







    },







    "android": {







      "adaptiveIcon": {







        "foregroundImage": "./assets/images/adaptive-icon.png",







        "backgroundColor": "#ffffff"







      },







      "permissions": [







        "android.permission.INTERNET",







        "android.permission.READ_EXTERNAL_STORAGE",







        "android.permission.WRITE_EXTERNAL_STORAGE"







      ]







    },







    "web": {







      "bundler": "metro",







      "output": "static",







      "favicon": "./assets/images/favicon.png"







    },







    "plugins": [







      "expo-router",







      [







        "expo-splash-screen",







        {







          "image": "./assets/images/splash-icon.png",







          "imageWidth": 200,







          "resizeMode": "contain",







          "backgroundColor": "#ffffff"







        }







      ]







    ],







    "experiments": {







      "typedRoutes": true







    }







  }







}







>>>>>>>



=======
{

  "expo": {

    "name": "My-fitness",

    "slug": "My-fitness",

    "version": "1.0.0",

    "orientation": "portrait",

    "icon": "./assets/images/icon.png",

    "scheme": "myapp",

    "userInterfaceStyle": "automatic",

    "newArchEnabled": true,

    "ios": {

      "supportsTablet": true,

      "infoPlist": {

        "NSAppTransportSecurity": {

          "NSAllowsArbitraryLoads": true

        }

      }

    },

    "android": {

      "adaptiveIcon": {

        "foregroundImage": "./assets/images/adaptive-icon.png",

        "backgroundColor": "#ffffff"

      },

      "permissions": [

        "android.permission.INTERNET",

        "android.permission.READ_EXTERNAL_STORAGE",

        "android.permission.WRITE_EXTERNAL_STORAGE"

      ]

    },

    "web": {

      "bundler": "metro",

      "output": "static",

      "favicon": "./assets/images/favicon.png"

    },

    "plugins": [

      "expo-router",

      [

        "expo-splash-screen",

        {

          "image": "./assets/images/splash-icon.png",

          "imageWidth": 200,

          "resizeMode": "contain",

          "backgroundColor": "#ffffff"

        }

      ]

    ],

    "experiments": {

      "typedRoutes": true

    }

  }

}

>>>>>>>
