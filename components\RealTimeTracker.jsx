import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TouchableOpacity,
  Platform,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Pedometer } from 'expo-sensors';
import StepCounter from './StepCounter';
import GPSTracker from './GPSTracker';
import SleepTracker from './SleepTracker';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { useColorScheme } from 'react-native';

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

const RealTimeTracker = ({ userEmail, onDataUpdate }) => {
  const [trackingData, setTrackingData] = useState({
    steps: 0,
    distance: 0, // in km
    calories: 0,
    activeMinutes: 0,
    heartRate: 0, // simulated
    waterIntake: 0,
    sleepHours: 0,
  });

  const [goals, setGoals] = useState({
    steps: 10000,
    distance: 5, // km
    calories: 500,
    activeMinutes: 30,
    water: 8, // glasses
    sleep: 8, // hours
  });

  const [isTracking, setIsTracking] = useState(false);
  const [location, setLocation] = useState(null);
  const [lastLocation, setLastLocation] = useState(null);
  const [startTime, setStartTime] = useState(null);
  const [showMetrics, setShowMetrics] = useState({
    steps: true,
    distance: true,
    calories: true,
    activeMinutes: true,
  });
  const [metricsOrder, setMetricsOrder] = useState(['steps', 'distance', 'calories', 'activeMinutes']);
  const [darkMode, setDarkMode] = useState(true);
  const [greetingName, setGreetingName] = useState('User');

  useEffect(() => {
    initializeTracking();
    loadTodayData();
    setupNotifications();
    
    // Try to get user name from AsyncStorage
    (async () => {
      let email = userEmail;
      if (email) {
        const userDataString = await AsyncStorage.getItem(`@user_data:${email}`);
        if (userDataString) {
          const userData = JSON.parse(userDataString);
          setGreetingName(userData.name || 'User');
        }
      }
    })();
    
    return () => {
      // Cleanup
    };
  }, []);

  useEffect(() => {
    if (isTracking) {
      startRealTimeTracking();
    } else {
      stopRealTimeTracking();
    }
  }, [isTracking]);

  const initializeTracking = async () => {
    try {
      // Request permissions
      const { status: locationStatus } = await Location.requestForegroundPermissionsAsync();
      if (locationStatus !== 'granted') {
        Alert.alert('Permission Denied', 'Location permission is required for GPS tracking.');
        return;
      }

      const { status: notificationStatus } = await Notifications.requestPermissionsAsync();
      if (notificationStatus !== 'granted') {
        Alert.alert('Permission Denied', 'Notification permission is required for reminders.');
      }

      // Check if pedometer is available
      const isAvailable = await Pedometer.isAvailableAsync();
      if (!isAvailable) {
        console.log('Pedometer not available, using simulated data');
      }
    } catch (error) {
      console.error('Error initializing tracking:', error);
    }
  };

  const loadTodayData = async () => {
    try {
      const today = new Date().toISOString().split('T')[0];
      const dataKey = `@tracking_data:${userEmail}:${today}`;
      const savedData = await AsyncStorage.getItem(dataKey);
      
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        setTrackingData(parsedData);
        if (onDataUpdate) {
          onDataUpdate(parsedData);
        }
      }
    } catch (error) {
      console.error('Error loading today data:', error);
    }
  };

  const saveTodayData = async (data) => {
    try {
      const today = new Date().toISOString().split('T')[0];
      const dataKey = `@tracking_data:${userEmail}:${today}`;
      await AsyncStorage.setItem(dataKey, JSON.stringify(data));
      
      if (onDataUpdate) {
        onDataUpdate(data);
      }
    } catch (error) {
      console.error('Error saving today data:', error);
    }
  };

  const startRealTimeTracking = () => {
    setStartTime(new Date());
    
    // Start step tracking
    const stepSubscription = Pedometer.watchStepCount(result => {
      updateSteps(result.steps);
    });

    // Start location tracking for distance
    const locationSubscription = Location.watchPositionAsync(
      {
        accuracy: Location.Accuracy.High,
        timeInterval: 5000, // Update every 5 seconds
        distanceInterval: 10, // Update every 10 meters
      },
      (newLocation) => {
        updateLocation(newLocation);
      }
    );

    // Start heart rate simulation (in real app, use actual sensor)
    const heartRateInterval = setInterval(() => {
      simulateHeartRate();
    }, 10000); // Update every 10 seconds

    // Start active minutes tracking
    const activeMinutesInterval = setInterval(() => {
      updateActiveMinutes();
    }, 60000); // Update every minute

    return () => {
      stepSubscription && stepSubscription.remove();
      locationSubscription && locationSubscription.remove();
      clearInterval(heartRateInterval);
      clearInterval(activeMinutesInterval);
    };
  };

  const stopRealTimeTracking = () => {
    // Cleanup handled in return function above
  };

  const updateSteps = (newSteps) => {
    const calories = Math.round(newSteps * 0.04); // Rough calculation
    setTrackingData(prev => {
      const updated = {
        ...prev,
        steps: newSteps,
        calories: prev.calories + (calories - prev.calories),
      };
      saveTodayData(updated);
      updateAnalyticsTracker(updated);
      return updated;
    });
  };

  const updateLocation = (newLocation) => {
    setLocation(newLocation);
    if (lastLocation) {
      const distance = calculateDistance(
        lastLocation.coords.latitude,
        lastLocation.coords.longitude,
        newLocation.coords.latitude,
        newLocation.coords.longitude
      );
      setTrackingData(prev => {
        const updated = {
          ...prev,
          distance: prev.distance + distance,
        };
        saveTodayData(updated);
        updateAnalyticsTracker(updated);
        return updated;
      });
    }
    setLastLocation(newLocation);
  };

  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const simulateHeartRate = () => {
    // Simulate heart rate based on activity level
    const baseRate = 70;
    const activityBonus = isTracking ? Math.random() * 50 : 0;
    const heartRate = Math.round(baseRate + activityBonus);
    
    setTrackingData(prev => {
      const updated = { ...prev, heartRate };
      saveTodayData(updated);
      updateAnalyticsTracker(updated);
      return updated;
    });
  };

  const updateActiveMinutes = () => {
    if (isTracking && startTime) {
      const now = new Date();
      const minutesActive = Math.floor((now - startTime) / (1000 * 60));
      
      setTrackingData(prev => {
        const updated = {
          ...prev,
          activeMinutes: prev.activeMinutes + 1,
        };
        saveTodayData(updated);
        updateAnalyticsTracker(updated);
        return updated;
      });
    }
  };

  const updateWaterIntake = () => {
    setTrackingData(prev => {
      const updated = {
        ...prev,
        waterIntake: prev.waterIntake + 1,
      };
      saveTodayData(updated);
      updateAnalyticsTracker(updated);
      return updated;
    });

    // Check if goal reached
    if (trackingData.waterIntake + 1 >= goals.water) {
      scheduleNotification('🎉 Water Goal Achieved!', 'Great job staying hydrated today!');
    }
  };

  const updateSleepHours = (hours) => {
    setTrackingData(prev => {
      const updated = {
        ...prev,
        sleepHours: hours,
      };
      saveTodayData(updated);
      updateAnalyticsTracker(updated);
      return updated;
    });
  };

  const setupNotifications = async () => {
    // Water reminder every 2 hours
    await Notifications.scheduleNotificationAsync({
      content: {
        title: '💧 Hydration Reminder',
        body: 'Time to drink some water! Stay hydrated.',
        sound: true,
      },
      trigger: {
        seconds: 7200, // 2 hours
        repeats: true,
      },
    });

    // Inactivity reminder
    await Notifications.scheduleNotificationAsync({
      content: {
        title: '🚶 Move Your Body',
        body: 'You\'ve been inactive for a while. Time for a quick walk!',
        sound: true,
      },
      trigger: {
        seconds: 3600, // 1 hour
        repeats: true,
      },
    });

    // Sleep reminder
    await Notifications.scheduleNotificationAsync({
      content: {
        title: '😴 Sleep Time',
        body: 'Consider winding down for better sleep quality.',
        sound: true,
      },
      trigger: {
        hour: 22, // 10 PM
        minute: 0,
        repeats: true,
      },
    });
  };

  const scheduleNotification = async (title, body) => {
    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        sound: true,
      },
      trigger: null, // Immediate
    });
  };

  const getProgressPercentage = (current, goal) => {
    return Math.min(100, (current / goal) * 100);
  };

  const toggleTracking = () => {
    setIsTracking(!isTracking);
    
    if (!isTracking) {
      Alert.alert(
        '🎯 Real-Time Tracking Started',
        'GPS tracking, step counting, and activity monitoring are now active.',
        [{ text: 'Got it!', style: 'default' }]
      );
    } else {
      Alert.alert(
        '⏸️ Tracking Paused',
        'Real-time tracking has been paused. Your data is saved.',
        [{ text: 'OK', style: 'default' }]
      );
    }
  };

  // Add this function to update analytics tracker
  const updateAnalyticsTracker = async (summary) => {
    try {
      const today = new Date().toDateString();
      const trackerKey = `tracker_${userEmail}_${today}`;
      await AsyncStorage.mergeItem(trackerKey, JSON.stringify(summary));
    } catch (error) {
      console.error('Error updating analytics tracker:', error);
    }
  };

  function getMotivationalQuote() {
    const quotes = [
      "Every step counts. Keep moving forward!",
      "You are stronger than you think.",
      "Progress, not perfection.",
      "Small changes make a big difference.",
      "Stay consistent and results will follow.",
      "Your only limit is you.",
      "Believe in yourself and all that you are.",
      "Push yourself, because no one else is going to do it for you.",
      "Success starts with self-discipline.",
      "Dream big. Work hard. Stay focused.",
    ];
    const today = new Date().getDate();
    return quotes[today % quotes.length];
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: darkMode ? '#0c0c0c' : '#f8f9fa' }]} showsVerticalScrollIndicator={false}>
      {/* Animated Background Elements */}
      <Animated.View entering={FadeInUp.delay(100)} style={{ position: 'absolute', top: 0, left: 0, right: 0, height: 80, zIndex: 0 }}>
        <View style={{ position: 'absolute', top: -40, left: -40, width: 120, height: 120, borderRadius: 60, backgroundColor: darkMode ? 'rgba(102,126,234,0.08)' : 'rgba(102,126,234,0.12)' }} />
        <View style={{ position: 'absolute', top: 10, right: -30, width: 80, height: 80, borderRadius: 40, backgroundColor: darkMode ? 'rgba(255,255,255,0.05)' : 'rgba(102,126,234,0.08)' }} />
      </Animated.View>
      {/* Greeting and Motivation */}
      <View style={{ alignItems: 'center', marginTop: 16, marginBottom: 8 }}>
        <Text style={{ fontFamily: 'Poppins_700Bold', fontSize: 20, color: darkMode ? '#fff' : '#222', textAlign: 'center' }}>
          Hello, {greetingName}
        </Text>
        <Text style={{ fontFamily: 'Poppins_400Regular', fontSize: 14, color: darkMode ? 'rgba(255,255,255,0.7)' : '#555', textAlign: 'center', marginTop: 2 }}>
          {getMotivationalQuote()}
        </Text>
        <TouchableOpacity
          style={{ marginTop: 10, backgroundColor: darkMode ? 'rgba(102,126,234,0.15)' : 'rgba(102,126,234,0.08)', borderRadius: 16, paddingHorizontal: 14, paddingVertical: 6 }}
          onPress={() => setDarkMode((d) => !d)}
          activeOpacity={0.85}
        >
          <Text style={{ color: '#667eea', fontFamily: 'Poppins_600SemiBold', fontSize: 14 }}>{darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}</Text>
        </TouchableOpacity>
      </View>
      {/* Dashboard Customization */}
      <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 8 }}>
        {metricsOrder.map((metric) => (
          <TouchableOpacity
            key={metric}
            style={{ marginHorizontal: 6, padding: 6, borderRadius: 8, backgroundColor: showMetrics[metric] ? '#667eea' : 'rgba(102,126,234,0.15)' }}
            onPress={() => setShowMetrics((prev) => ({ ...prev, [metric]: !prev[metric] }))}
          >
            <Text style={{ color: showMetrics[metric] ? '#fff' : '#667eea', fontFamily: 'Poppins_600SemiBold', fontSize: 13 }}>{metric.charAt(0).toUpperCase() + metric.slice(1)}</Text>
          </TouchableOpacity>
        ))}
      </View>
      {/* Tracking Header */}
      <LinearGradient colors={darkMode ? ['#667eea', '#764ba2'] : ['#fff', '#e3e6f3']} style={[styles.header, { backgroundColor: darkMode ? undefined : '#fff' }]}> 
        <Text style={[styles.headerTitle, { color: darkMode ? '#fff' : '#222' }]}>📊 Real-Time Tracking</Text>
        <TouchableOpacity style={styles.toggleButton} onPress={toggleTracking} activeOpacity={0.8}>
          <Animated.Text style={[styles.toggleButtonText, { color: darkMode ? '#fff' : '#222' }]} entering={FadeInUp}>
            {isTracking ? '⏸️ Pause' : '▶️ Start'}
          </Animated.Text>
        </TouchableOpacity>
      </LinearGradient>
      {/* Metrics Grid (customizable) */}
      <View style={styles.metricsGrid}>
        {showMetrics.steps && (
          <LinearGradient colors={['#FF6B6B', '#FF8E53']} style={styles.metricCard}>
            <Text style={styles.metricTitle}>🦶 Steps</Text>
            <Text style={styles.metricValue}>{trackingData.steps.toLocaleString()}</Text>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { width: `${getProgressPercentage(trackingData.steps, goals.steps)}%` }
                ]} 
              />
            </View>
            <Text style={styles.goalText}>{goals.steps.toLocaleString()} goal</Text>
          </LinearGradient>
        )}
        {showMetrics.distance && (
          <LinearGradient colors={['#4ECDC4', '#44A08D']} style={styles.metricCard}>
            <Text style={styles.metricTitle}>📍 Distance</Text>
            <Text style={styles.metricValue}>{trackingData.distance.toFixed(2)} km</Text>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { width: `${getProgressPercentage(trackingData.distance, goals.distance)}%` }
                ]} 
              />
            </View>
            <Text style={styles.goalText}>{goals.distance} km goal</Text>
          </LinearGradient>
        )}
        {showMetrics.calories && (
          <LinearGradient colors={['#A8E6CF', '#88D8C0']} style={styles.metricCard}>
            <Text style={styles.metricTitle}>🔥 Calories</Text>
            <Text style={styles.metricValue}>{trackingData.calories}</Text>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { width: `${getProgressPercentage(trackingData.calories, goals.calories)}%` }
                ]} 
              />
            </View>
            <Text style={styles.goalText}>{goals.calories} goal</Text>
          </LinearGradient>
        )}
        {showMetrics.activeMinutes && (
          <LinearGradient colors={['#FFD93D', '#FF6B6B']} style={styles.metricCard}>
            <Text style={styles.metricTitle}>⏱️ Active</Text>
            <Text style={styles.metricValue}>{trackingData.activeMinutes} min</Text>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { width: `${getProgressPercentage(trackingData.activeMinutes, goals.activeMinutes)}%` }
                ]} 
              />
            </View>
            <Text style={styles.goalText}>{goals.activeMinutes} min goal</Text>
          </LinearGradient>
        )}
      </View>

      {/* Additional Metrics */}
      <View style={styles.additionalMetrics}>
        <View style={styles.metricRow}>
          <Text style={styles.metricLabel}>💧 Water Intake</Text>
          <View style={styles.waterControls}>
            <Text style={styles.waterValue}>{trackingData.waterIntake}/{goals.water}</Text>
            <TouchableOpacity style={styles.addButton} onPress={updateWaterIntake}>
              <Text style={styles.addButtonText}>+</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.metricRow}>
          <Text style={styles.metricLabel}>❤️ Heart Rate</Text>
          <Text style={styles.heartRateValue}>{trackingData.heartRate} BPM</Text>
        </View>

        <View style={styles.metricRow}>
          <Text style={styles.metricLabel}>😴 Sleep</Text>
          <Text style={styles.sleepValue}>{trackingData.sleepHours}h / {goals.sleep}h</Text>
        </View>
      </View>

      {isTracking && (
        <View style={styles.trackingIndicator}>
          <View style={styles.pulseDot} />
          <Text style={styles.trackingText}>Live tracking active</Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#fff',
  },
  toggleButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  toggleButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  metricCard: {
    width: '48%',
    padding: 12,
    borderRadius: 12,
    marginBottom: 12,
    alignItems: 'center',
  },
  metricTitle: {
    fontSize: 14,
    color: '#fff',
    marginBottom: 4,
    fontWeight: '600',
  },
  metricValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 8,
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#fff',
    borderRadius: 2,
  },
  goalText: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.8,
  },
  additionalMetrics: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  metricLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  waterControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  waterValue: {
    fontSize: 16,
    color: '#666',
    marginRight: 8,
  },
  addButton: {
    backgroundColor: '#007AFF',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  heartRateValue: {
    fontSize: 16,
    color: '#FF6B6B',
    fontWeight: '600',
  },
  sleepValue: {
    fontSize: 16,
    color: '#666',
  },
  trackingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    padding: 8,
    borderRadius: 8,
  },
  pulseDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginRight: 8,
  },
  trackingText: {
    color: '#4CAF50',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default RealTimeTracker;