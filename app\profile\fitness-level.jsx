import React, { useEffect, useRef, useState } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet, 
  Image, 
  Platform,
  SafeAreaView,
  Alert,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Animated as RNAnimated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useUser } from '../../context/UserContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StatusBar } from 'expo-status-bar';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import {
  useFonts,
  Poppins_700Bold,
  Poppins_400Regular,
  Poppins_600SemiBold,
} from '@expo-google-fonts/poppins';
import Animated, { SlideInUp } from 'react-native-reanimated';

const { width, height } = Dimensions.get('window');

// Advanced Particle component with physics
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;
  const scaleAnim = useRef(new RNAnimated.Value(0)).current;
  const rotationAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.parallel([
          RNAnimated.timing(particleAnim, {
            toValue: 1,
            duration: (4000 + Math.random() * 3000) * speed,
            useNativeDriver: true,
          }),
          RNAnimated.timing(opacityAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
          RNAnimated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 40,
            useNativeDriver: true,
          }),
          RNAnimated.timing(rotationAnim, {
            toValue: 1,
            duration: 3000 * speed,
            useNativeDriver: true,
          }),
        ]),
        RNAnimated.parallel([
          RNAnimated.timing(particleAnim, {
            toValue: 0,
            duration: (4000 + Math.random() * 3000) * speed,
            useNativeDriver: true,
          }),
          RNAnimated.timing(opacityAnim, {
            toValue: 0,
            duration: 1500,
            useNativeDriver: true,
          }),
          RNAnimated.spring(scaleAnim, {
            toValue: 0,
            friction: 8,
            tension: 40,
            useNativeDriver: true,
          }),
          RNAnimated.timing(rotationAnim, {
            toValue: 0,
            duration: 3000 * speed,
            useNativeDriver: true,
          }),
        ]),
      ])
    );

    setTimeout(() => {
      animation.start();
    }, delay);

    return () => animation.stop();
  }, []);

  const particleStyle = {
    transform: [
      {
        translateY: particleAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0, -100],
        }),
      },
      {
        scale: scaleAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0.3, 1],
        }),
      },
      {
        rotate: rotationAnim.interpolate({
          inputRange: [0, 1],
          outputRange: ['0deg', '360deg'],
        }),
      },
    ],
    opacity: opacityAnim,
  };

  return (
    <RNAnimated.View style={[styles.particle, particleStyle, style]}>
      <LinearGradient
        colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
        style={styles.particleGradient}
      />
    </RNAnimated.View>
  );
};

// Floating shape component
const FloatingShape = ({ style, delay = 0, rotationSpeed = 1 }) => {
  const floatAnim = useRef(new RNAnimated.Value(0)).current;
  const rotateAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    setTimeout(() => {
      RNAnimated.loop(
        RNAnimated.sequence([
          RNAnimated.timing(floatAnim, {
            toValue: 1,
            duration: 3000,
            useNativeDriver: true,
          }),
          RNAnimated.timing(floatAnim, {
            toValue: 0,
            duration: 3000,
            useNativeDriver: true,
          }),
        ])
      ).start();

      RNAnimated.loop(
        RNAnimated.timing(rotateAnim, {
          toValue: 1,
          duration: 10000 / rotationSpeed,
          useNativeDriver: true,
        })
      ).start();
    }, delay);
  }, []);

  const shapeStyle = {
    transform: [
      {
        translateY: floatAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0, -15],
        }),
      },
      {
        rotate: rotateAnim.interpolate({
          inputRange: [0, 1],
          outputRange: ['0deg', '360deg'],
        }),
      },
    ],
  };

  return (
    <RNAnimated.View style={[styles.floatingShape, shapeStyle, style]}>
      <LinearGradient
        colors={['rgba(102, 126, 234, 0.3)', 'rgba(118, 75, 162, 0.2)']}
        style={styles.shapeGradient}
      />
    </RNAnimated.View>
  );
};

export default function FitnessLevel() {
  const { fitnessLevel, setFitnessLevel, setUserHistory, currentUser, updateCurrentUser } = useUser();
  const router = useRouter();
  const userEmail = currentUser?.email;
  const [loading, setLoading] = useState(true);

  // Animation values
  const fadeAnim = useRef(new RNAnimated.Value(0)).current;
  const slideAnim = useRef(new RNAnimated.Value(50)).current;
  const scaleAnim = useRef(new RNAnimated.Value(0.9)).current;
  const buttonScale = useRef(new RNAnimated.Value(1)).current;

  let [fontsLoaded] = useFonts({
    Poppins_700Bold,
    Poppins_400Regular,
    Poppins_600SemiBold,
  });

  useEffect(() => {
    // Main entrance animations
    RNAnimated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
    
    RNAnimated.timing(slideAnim, {
      toValue: 0,
      duration: 800,
      useNativeDriver: true,
    }).start();
    
    RNAnimated.spring(scaleAnim, {
      toValue: 1,
      damping: 12,
      stiffness: 100,
      useNativeDriver: true,
    }).start();
  }, []);

  useEffect(() => {
    if (!userEmail) return;
    (async () => {
      try {
        // Always start with empty state for new users
        if (currentUser && currentUser.isNewUser) {
          setFitnessLevel('');
          setLoading(false);
          return;
        }
        // Try to get from user-specific data
        const userDataKey = `@user_data:${userEmail}`;
        const userDataString = await AsyncStorage.getItem(userDataKey);
        if (userDataString) {
          const userData = JSON.parse(userDataString);
          if (userData.fitnessLevel) {
            setFitnessLevel(userData.fitnessLevel);
            setLoading(false);
            return;
          }
        }
        // Try to get from user-specific profile
        const profileKey = `@user_profile:${userEmail}`;
        const profileData = await AsyncStorage.getItem(profileKey);
        if (profileData) {
          const profile = JSON.parse(profileData);
          if (profile.fitnessLevel) {
            setFitnessLevel(profile.fitnessLevel);
            setLoading(false);
            return;
          }
        }
        // Default to empty
        setFitnessLevel('');
        setLoading(false);
      } catch (error) {
        setFitnessLevel('');
        setLoading(false);
      }
    })();
  }, [userEmail, currentUser]);

  const handleNext = async () => {
    if (!fitnessLevel) {
      Alert.alert('Selection Required', 'Please select your fitness level');
      return;
    }

    // Button press animation
    RNAnimated.sequence([
      RNAnimated.timing(buttonScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      RNAnimated.spring(buttonScale, {
        toValue: 1,
        damping: 15,
        stiffness: 150,
        useNativeDriver: true,
      })
    ]).start();

    try {
      if (!userEmail) {
        Alert.alert('Error', 'User email not found. Please log in again.');
        return;
      }
      
      console.log('Fitness Level: Saving fitness level:', fitnessLevel, 'for user:', userEmail);
      
      // Save to user-specific profile
      const profileKey = `@user_profile:${userEmail}`;
      const profileData = await AsyncStorage.getItem(profileKey);
      const profile = profileData ? JSON.parse(profileData) : {};
      profile.fitnessLevel = fitnessLevel;
      await AsyncStorage.setItem(profileKey, JSON.stringify(profile));
      console.log('Fitness Level: Saved to profile:', profile);
      
      // Also save to user data
      const userDataKey = `@user_data:${userEmail}`;
      const userDataString = await AsyncStorage.getItem(userDataKey);
      const userData = userDataString ? JSON.parse(userDataString) : {};
      userData.fitnessLevel = fitnessLevel;
      await AsyncStorage.setItem(userDataKey, JSON.stringify(userData));
      console.log('Fitness Level: Saved to user data:', userData);
      
      // Also save to shared current user storage
      await AsyncStorage.setItem('@FitnessApp:currentUser', JSON.stringify({
        ...userData,
        fitnessLevel: fitnessLevel
      }));
      console.log('Fitness Level: Saved to current user storage');
      
      // Create history entry
      const entry = {
        action: 'Fitness Level Selected',
        userEmail: userEmail,
        userName: currentUser?.name,
        timestamp: new Date().toISOString(),
        details: `Selected fitness level: ${fitnessLevel}`
      };
      
      // Add to user history
      setUserHistory(prev => [...(prev || []), entry]);
      
      // Save to user-specific history
      const userHistoryKey = `@userHistory:${userEmail}`;
      const existingHistory = await AsyncStorage.getItem(userHistoryKey);
      const history = existingHistory ? JSON.parse(existingHistory) : [];
      await AsyncStorage.setItem(userHistoryKey, JSON.stringify([...history, entry]));
      
      // Update user context with fitness level
      await updateCurrentUser({ fitnessLevel });
      console.log('Fitness Level: Updated user context with fitness level:', fitnessLevel);
      
      // Slight delay before navigation
      setTimeout(() => {
        router.push('/profile/measurements');
      }, 200);
    } catch (error) {
      console.error('Error saving fitness level:', error);
      Alert.alert('Error', 'Failed to save your fitness level. Please try again.');
    }
  };

  const handleBack = () => {
    router.back();
  };

  const AnimatedContent = RNAnimated.createAnimatedComponent(View);
  const contentAnimStyle = {
    opacity: fadeAnim,
    transform: [
      { translateY: slideAnim },
      { scale: scaleAnim }
    ]
  };

  const AnimatedButton = RNAnimated.createAnimatedComponent(View);
  const buttonAnimStyle = {
    transform: [{ scale: buttonScale }]
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ffffff" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      <LinearGradient 
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} 
        style={styles.background}
      >
        {/* Animated Background Elements */}
        <AdvancedParticle
          style={{ left: wp(10), top: hp(20) }}
          delay={0}
          speed={1.2}
        />
        <AdvancedParticle
          style={{ left: wp(80), top: hp(30) }}
          delay={500}
          speed={0.8}
        />
        <AdvancedParticle
          style={{ left: wp(20), top: hp(70) }}
          delay={1000}
          speed={1.5}
        />
        <FloatingShape
          style={{ left: wp(85), top: hp(15) }}
          delay={0}
          rotationSpeed={0.5}
        />
        <FloatingShape
          style={{ left: wp(5), top: hp(80) }}
          delay={1000}
          rotationSpeed={1.2}
        />
        <View style={[styles.glowCircle, { top: hp(10), right: wp(10) }]} />
        <View style={[styles.glowCircle, { bottom: hp(20), left: wp(5) }]} />

        {/* Back Button */}
        <TouchableOpacity 
          onPress={handleBack} 
          style={styles.backButton}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
            style={styles.backButtonGradient}
          >
            <Ionicons name="arrow-back" size={24} color="#ffffff" />
          </LinearGradient>
        </TouchableOpacity>

        {/* Main Content */}
        <AnimatedContent style={[styles.content, contentAnimStyle]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Fitness Level</Text>
            <Text style={styles.subtitle}>Select your current fitness level</Text>
          </View>

          {/* Progress Indicator */}
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: '25%' }]} />
            </View>
            <Text style={styles.progressText}>Step 1 of 4</Text>
          </View>

          {/* Options */}
          <View style={styles.optionsContainer}>
            {['Beginner', 'Intermediate', 'Advanced'].map((level, index) => (
              <Animated.View 
                key={level} 
                entering={SlideInUp.delay(300 + index * 100).springify()}
                style={styles.optionWrapper}
              >
                <TouchableOpacity
                  style={[
                    styles.optionBtn,
                    fitnessLevel === level && styles.selectedBtn,
                  ]}
                  onPress={() => setFitnessLevel(level)}
                  activeOpacity={0.8}
                >
                  <LinearGradient
                    colors={
                      fitnessLevel === level
                        ? ['#667eea', '#764ba2']
                        : ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']
                    }
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.optionGradient}
                  >
                    <View style={styles.optionContent}>
                      <View style={styles.iconContainer}>
                        <Ionicons
                          name={
                            level === 'Beginner' 
                              ? 'body-outline'
                              : level === 'Intermediate'
                              ? 'barbell-outline'
                              : 'fitness-outline'
                          }
                          size={24}
                          color={fitnessLevel === level ? '#fff' : 'rgba(255,255,255,0.8)'}
                        />
                      </View>
                      <Text style={[styles.optionText, fitnessLevel === level && styles.selectedOptionText]}>
                        {level}
                      </Text>
                    </View>
                  </LinearGradient>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </View>

          {/* Next Button */}
          <AnimatedButton style={[styles.buttonContainer, buttonAnimStyle]}>
            <TouchableOpacity 
              onPress={handleNext} 
              activeOpacity={0.8} 
              disabled={!fitnessLevel}
              style={styles.nextButton}
            >
              <LinearGradient 
                colors={fitnessLevel ? ['#667eea', '#764ba2'] : ['rgba(255,255,255,0.2)', 'rgba(255,255,255,0.1)']} 
                style={styles.buttonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={[styles.buttonText, !fitnessLevel && styles.buttonTextDisabled]}>
                  Continue
                </Text>
                <Ionicons 
                  name="arrow-forward" 
                  size={20} 
                  color={fitnessLevel ? "#fff" : "rgba(255,255,255,0.5)"} 
                  style={styles.buttonIcon}
                />
              </LinearGradient>
            </TouchableOpacity>
          </AnimatedButton>
        </AnimatedContent>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0c0c0c',
  },
  background: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'android' ? 40 : 20,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontFamily: 'Poppins_700Bold',
    fontSize: 28,
    color: '#ffffff',
    marginBottom: 5,
  },
  subtitle: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  progressContainer: {
    marginBottom: 30,
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#667eea',
    borderRadius: 4,
  },
  progressText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  optionsContainer: {
    marginBottom: 40,
  },
  optionWrapper: {
    marginBottom: 16,
  },
  optionBtn: {
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  selectedBtn: {
    borderColor: '#667eea',
  },
  optionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  selectedOptionText: {
    color: '#ffffff',
  },
  buttonContainer: {
    marginTop: 'auto',
    marginBottom: 30,
  },
  nextButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  buttonText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#ffffff',
    marginRight: 8,
  },
  buttonTextDisabled: {
    color: 'rgba(255, 255, 255, 0.5)',
  },
  buttonIcon: {
    marginLeft: 4,
  },
  backButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 30,
    left: 20,
    zIndex: 10,
  },
  backButtonGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  particle: {
    position: 'absolute',
    width: 20,
    height: 20,
    zIndex: 1,
  },
  particleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  },
  floatingShape: {
    position: 'absolute',
    width: 40,
    height: 40,
    zIndex: 1,
  },
  shapeGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
  },
  glowCircle: {
    position: 'absolute',
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    zIndex: 0,
  },
});