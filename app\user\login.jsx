import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  Platform,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { Poppins_400Regular, Poppins_700Bold, Poppins_600SemiBold, useFonts } from '@expo-google-fonts/poppins';
import * as Notifications from 'expo-notifications';
// Temporarily disable Firebase imports
// import { savePushTokenToFirebase } from '../../firebaseConfig';

// Mock Firebase function
const savePushTokenToFirebase = () => Promise.resolve();
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useUser } from '../../context/UserContext';

const { width, height } = Dimensions.get('window');

// Advanced Particle component
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.parallel([
          Animated.timing(particleAnim, {
            toValue: 1,
            duration: (4000 + Math.random() * 3000) * speed,
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 40,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(opacityAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      ])
    );
    
    Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 8000 + Math.random() * 4000,
        useNativeDriver: true,
      })
    ).start();
    
    setTimeout(() => animation.start(), delay);
  }, []);

  return (
    <Animated.View
      style={[
        style,
        {
          opacity: opacityAnim,
          transform: [
            {
              translateY: particleAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -height * 1.5],
              }),
            },
            {
              scale: scaleAnim,
            },
            {
              rotate: rotationAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '360deg'],
              }),
            },
          ],
        },
      ]}
    />
  );
};

// Floating geometric shapes
const FloatingShape = ({ style, delay = 0, rotationSpeed = 1 }) => {
  const floatAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
        Animated.timing(floatAnim, {
          toValue: 0,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 15000 * rotationSpeed,
        useNativeDriver: true,
      })
    ).start();

    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [
            {
              translateY: floatAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -30],
              }),
            },
            {
              rotate: rotationAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '360deg'],
              }),
            },
            {
              scale: scaleAnim,
            },
          ],
        },
      ]}
    />
  );
};

// Shimmer effect component
const Shimmer = ({ children, style }) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.timing(shimmerAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: false,
      })
    ).start();
  }, []);

  const shimmerOpacity = shimmerAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.3, 0.8, 0.3],
  });

  return (
    <View style={[style, { overflow: 'hidden' }]}>
      {children}
      <Animated.View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          opacity: shimmerOpacity,
          transform: [
            {
              translateX: shimmerAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [-width, width],
              }),
            },
          ],
        }}
      />
    </View>
  );
};

// Gradient Back Button
const GradientBackButton = ({ onPress, style }) => (
  <TouchableOpacity onPress={onPress} style={[styles.backButton, style]}>
    <LinearGradient
      colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
      style={styles.backButtonGradient}
    >
      <Ionicons name="arrow-back" size={24} color="#ffffff" />
    </LinearGradient>
  </TouchableOpacity>
);

export default function UserLogin() {
  const router = useRouter();
  const { loginUser, logoutUser } = useUser();
  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_700Bold,
    Poppins_600SemiBold,
  });

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const buttonScale = useRef(new Animated.Value(1)).current;
  const backScale = useRef(new Animated.Value(1)).current;
  const formScale = useRef(new Animated.Value(0.9)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Main entrance animations
    Animated.stagger(200, [
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(formScale, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
    
    // Logo pulse animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    // Check if coming from password reset
    const checkPasswordReset = async () => {
      try {
        const passwordReset = await AsyncStorage.getItem('@FitnessApp:passwordReset');
        if (passwordReset === 'true') {
          setErrorMessage('Your password has been reset. Please login with your new password.');
          // Clear the flag
          await AsyncStorage.removeItem('@FitnessApp:passwordReset');
        }
      } catch (error) {
        console.error('Error checking password reset:', error);
      }
    };
    
    checkPasswordReset();
  }, []);

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (password) => {
    return password.length >= 6;
  };

  const handleEmailChange = (text) => {
    setEmail(text);
    setEmailError('');
    setErrorMessage('');
  };

  const handlePasswordChange = (text) => {
    setPassword(text);
    setPasswordError('');
    setErrorMessage('');
  };

  const handleLogin = async () => {
    try {
      // Always logout first to clear any previous user data
      if (logoutUser) await logoutUser();
      // Now login the new user
      const user = await loginUser(email, password);
      if (user) {
        // Navigate to home or profile
        router.replace('/homepage');
      }
    } catch (error) {
      Alert.alert('Login Error', error.message || 'Failed to login.');
    }
  };

  const handleBackPress = () => {
    Animated.sequence([
      Animated.timing(backScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.spring(backScale, {
        toValue: 1,
        friction: 3,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start(() => {
      router.push('/role');
    });
  };

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      setErrorMessage('');
      
      // Animate button press
      Animated.sequence([
        Animated.timing(buttonScale, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.spring(buttonScale, {
          toValue: 1,
          friction: 3,
          tension: 40,
          useNativeDriver: true,
        }),
      ]).start();
      
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const googleUser = {
        email: '<EMAIL>',
        name: 'Google User',
        password: 'google123',
        joinDate: new Date().toISOString(),
        lastLogin: new Date().toISOString(),
        provider: 'google'
      };

      const usersData = await AsyncStorage.getItem('@FitnessApp:users');
      const users = usersData ? JSON.parse(usersData) : [];
      
      const existingUser = users.find(u => u.email === googleUser.email);
      if (!existingUser) {
        users.push(googleUser);
        await AsyncStorage.setItem('@FitnessApp:users', JSON.stringify(users));
      } else {
        // Update last login time for existing user
        const updatedUsers = users.map(u => 
          u.email === googleUser.email ? {...u, lastLogin: new Date().toISOString()} : u
        );
        await AsyncStorage.setItem('@FitnessApp:users', JSON.stringify(updatedUsers));
      }
      
      await AsyncStorage.setItem('@FitnessApp:currentUser', JSON.stringify(googleUser));
      router.push('/homepage');
    } catch (error) {
      console.error('Google sign-in error:', error);
      setErrorMessage('Google sign-in failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const bottomNav = [
    { icon: 'body-outline', label: 'Exercises', onPress: () => router.push('/homepage'), iconLib: Ionicons },
    { icon: 'restaurant-outline', label: 'Diet Plans', onPress: () => router.push('/plans'), iconLib: Ionicons },
    { icon: 'robot', label: 'AI Chat', onPress: () => router.push('/chat'), iconLib: MaterialCommunityIcons },
    { icon: 'analytics-outline', label: 'Daily Report', onPress: () => router.push('/analyze'), iconLib: Ionicons },
  ];

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ffffff" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      <LinearGradient
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      />

      <AdvancedParticle
        style={[styles.particle, { left: wp(10), top: hp(20) }]}
        delay={0}
        speed={1.2}
      />
      <AdvancedParticle
        style={[styles.particle, { left: wp(80), top: hp(30) }]}
        delay={500}
        speed={0.8}
      />
      <AdvancedParticle
        style={[styles.particle, { left: wp(20), top: hp(70) }]}
        delay={1000}
        speed={1.5}
      />

      <FloatingShape
        style={[styles.floatingShape, { left: wp(85), top: hp(15) }]}
        delay={0}
        rotationSpeed={0.5}
      />
      <FloatingShape
        style={[styles.floatingShape, { left: wp(5), top: hp(80) }]}
        delay={1000}
        rotationSpeed={1.2}
      />

      <View style={[styles.glowCircle, { top: hp(10), right: wp(10) }]} />
      <View style={[styles.glowCircle, { bottom: hp(20), left: wp(5) }]} />

      <Animated.View style={[styles.backButtonContainer, { transform: [{ scale: backScale }] }]}>
        <GradientBackButton onPress={handleBackPress} />
      </Animated.View>

      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [
              { translateY: slideAnim },
              { scale: formScale }
            ],
          },
        ]}
      >
        <View style={styles.header}>
          <Animated.View style={[styles.logoContainer, { transform: [{ scale: pulseAnim }] }]}>
            <LinearGradient
              colors={['rgba(102, 126, 234, 0.3)', 'rgba(118, 75, 162, 0.2)', 'rgba(240, 147, 251, 0.1)']}
              style={styles.logoGradient}
            >
              <Ionicons name="fitness" size={hp(8)} color="#fff" />
            </LinearGradient>
          </Animated.View>
          <Text style={styles.title}>Welcome Back</Text>
          <Text style={styles.subtitle}>Sign in to continue your fitness journey</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Email</Text>
            <View style={[styles.inputWrapper, emailError ? styles.inputError : null]}>
              <Ionicons name="mail-outline" size={20} color="#ffffff" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Enter your email"
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                value={email}
                onChangeText={handleEmailChange}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>
            {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Password</Text>
            <View style={[styles.inputWrapper, passwordError ? styles.inputError : null]}>
              <Ionicons name="lock-closed-outline" size={20} color="#ffffff" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Enter your password"
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                value={password}
                onChangeText={handlePasswordChange}
                secureTextEntry={!showPassword}
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                style={styles.eyeIcon}
              >
                <Ionicons
                  name={showPassword ? "eye-off-outline" : "eye-outline"}
                  size={20}
                  color="#ffffff"
                />
              </TouchableOpacity>
            </View>
            {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
          </View>

          {errorMessage ? (
            <View style={[
              styles.messageContainer, 
              errorMessage.toLowerCase().includes('successful') ? styles.successContainer : styles.errorContainer
            ]}>
              <Text style={[
                styles.messageText,
                errorMessage.toLowerCase().includes('successful') ? styles.successText : styles.errorText
              ]}>
                {errorMessage}
              </Text>
            </View>
          ) : null}

          <Animated.View style={{ transform: [{ scale: buttonScale }] }}>
            <Shimmer style={styles.loginButtonContainer}>
              <TouchableOpacity
                style={styles.loginButton}
                onPress={handleLogin}
                disabled={isLoading}
              >
                <LinearGradient
                  colors={['#667eea', '#764ba2']}
                  style={styles.loginButtonGradient}
                >
                  {isLoading ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <Text style={styles.loginButtonText}>Sign In</Text>
                  )}
                </LinearGradient>
              </TouchableOpacity>
            </Shimmer>
          </Animated.View>

          <TouchableOpacity
            style={styles.forgotPasswordContainer}
            onPress={() => router.push('/user/forgotpassword')}
          >
            <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
          </TouchableOpacity>

          <View style={styles.signupContainer}>
            <Text style={styles.signupText}>Don't have an account? </Text>
            <TouchableOpacity onPress={() => router.push('/user/signup')}>
              <Text style={styles.signupLink}>Sign up</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0c0c0c',
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  particle: {
    position: 'absolute',
    width: 4,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  floatingShape: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
  },
  glowCircle: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 20,
    elevation: 10,
  },
  backButtonContainer: {
    position: 'absolute',
    top: hp(5),
    left: wp(5),
    zIndex: 10,
  },
  backButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    overflow: 'hidden',
  },
  backButtonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 25,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: wp(6),
    paddingTop: Platform.OS === 'ios' ? hp(15) : hp(8),
    paddingBottom: Platform.OS === 'ios' ? hp(5) : hp(3),
  },
  logoContainer: {
    width: Platform.OS === 'ios' ? wp(25) : wp(20),
    height: Platform.OS === 'ios' ? wp(25) : wp(20),
    borderRadius: Platform.OS === 'ios' ? wp(12.5) : wp(10),
    overflow: 'hidden',
    marginBottom: hp(2),
    alignSelf: 'center',
  },
  logoGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: Platform.OS === 'ios' ? hp(4) : hp(2),
  },
  title: {
    fontSize: Platform.OS === 'ios' ? wp(8) : wp(7),
    fontFamily: 'Poppins_700Bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: hp(0.5),
  },
  subtitle: {
    fontSize: Platform.OS === 'ios' ? wp(4) : wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  form: {
    width: '100%',
  },
  inputContainer: {
    marginBottom: Platform.OS === 'ios' ? hp(3) : hp(2),
  },
  inputLabel: {
    fontSize: Platform.OS === 'ios' ? wp(4) : wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#ffffff',
    marginBottom: hp(0.5),
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    paddingHorizontal: wp(3),
    paddingVertical: Platform.OS === 'ios' ? hp(2) : hp(1),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  inputError: {
    borderColor: '#ff6b6b',
  },
  inputIcon: {
    marginRight: wp(3),
  },
  input: {
    flex: 1,
    fontSize: Platform.OS === 'ios' ? wp(4) : wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#ffffff',
    paddingVertical: Platform.OS === 'ios' ? hp(1.2) : hp(0.8),
    height: Platform.OS === 'ios' ? hp(5) : hp(4.5),
  },
  eyeIcon: {
    padding: wp(1),
  },
  errorText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#ff6b6b',
    marginTop: hp(1),
  },
  messageContainer: {
    borderRadius: 8,
    padding: wp(3),
    marginBottom: hp(3),
    borderWidth: 1,
  },
  errorContainer: {
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
    borderColor: 'rgba(255, 107, 107, 0.3)',
  },
  successContainer: {
    backgroundColor: 'rgba(76, 209, 55, 0.1)',
    borderColor: 'rgba(76, 209, 55, 0.3)',
  },
  messageText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    textAlign: 'center',
  },
  successText: {
    color: '#4cd137',
  },
  loginButtonContainer: {
    borderRadius: 12,
    marginBottom: Platform.OS === 'ios' ? hp(3) : hp(2),
    overflow: 'hidden',
  },
  loginButton: {
    width: '100%',
  },
  loginButtonGradient: {
    paddingVertical: Platform.OS === 'ios' ? hp(2.5) : hp(2),
    alignItems: 'center',
    borderRadius: 12,
  },
  loginButtonText: {
    fontSize: Platform.OS === 'ios' ? wp(4.5) : wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: '#ffffff',
  },
  forgotPasswordContainer: {
    alignItems: 'flex-end',
    marginBottom: hp(4),
  },
  forgotPasswordText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(4),
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  dividerText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: wp(4),
  },
  googleButtonContainer: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: hp(4),
  },
  googleButton: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  googleButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(2.5),
  },
  googleIconContainer: {
    marginRight: wp(3),
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    width: wp(8),
    height: wp(8),
    borderRadius: wp(4),
    justifyContent: 'center',
    alignItems: 'center',
  },
  googleButtonText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: '#ffffff',
  },
  signupContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: hp(4),
    paddingVertical: hp(1),
  },
  signupText: {
    fontSize: wp(3.8),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  signupLink: {
    fontSize: wp(3.8),
    fontFamily: 'Poppins_600SemiBold',
    color: '#667eea',
    marginLeft: wp(1),
    textDecorationLine: 'underline',
  },
}); 