import React, { useState, useEffect, useRef } from 'react';
import {
  View, Text, StyleSheet, ActivityIndicator, ScrollView, TouchableOpacity, Dimensions, Platform, StatusBar, RefreshControl, SafeAreaView, Animated as RNAnimated
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useFonts, Poppins_400Regular, Poppins_700Bold, Poppins_600SemiBold } from '@expo-google-fonts/poppins';
import { LinearGradient } from 'expo-linear-gradient';
import { <PERSON><PERSON><PERSON>, Pie<PERSON>hart, LineChart } from 'react-native-chart-kit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { useUser } from '../context/UserContext';
import Animated, { FadeInUp, FadeInLeft } from 'react-native-reanimated';
import AdvancedParticle from '../components/AdvancedParticle';

const { width, height } = Dimensions.get('window');
const isSmallDevice = width < 375;
const chartWidth = width - 40;

function sanitizeChartData(data) {
  return Array.isArray(data) ? data.map(d => (isFinite(d) && !isNaN(d) ? d : 0)) : [];
}

export default function Analyze() {
  // Always call useFonts first to maintain hook order
  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_700Bold,
    Poppins_600SemiBold,
  });

  const router = useRouter();
  const params = useLocalSearchParams();
  const { currentUser } = useUser();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState('week');
  const [userEmail, setUserEmail] = useState(null);
  
  // Real user data state with default sample data
  const [userData, setUserData] = useState({
    todayStats: {
      steps: 0,
      calories: 0,
      sleep: 0,
      water: 0,
      workouts: 0,
      heartRate: 0,
      distance: 0
    },
    weekSummary: {
      totalSteps: 0,
      totalCalories: 0,
      avgSleep: 0,
      totalWater: 0,
      workoutTypes: ['No workouts'],
      totalWorkouts: 0,
      totalDistance: 0
    },
    workoutHistory: [],
    sleepData: [],
    waterIntake: [],
    stepProgress: Array(7).fill(0).map((_, i) => ({
      date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
      steps: 0
    }))
  });
  
  const emailFromParams = params.userEmail || params.email;

  useEffect(() => {
    const loadUserEmail = async () => {
      try {
        console.log('Loading user email from sources');
        
        if (emailFromParams) {
          console.log('Using email from params:', emailFromParams);
          setUserEmail(emailFromParams);
          setLoading(false);
          return;
        } 
        
        if (currentUser?.email) {
          console.log('Using email from context:', currentUser.email);
          setUserEmail(currentUser.email);
          setLoading(false);
          return;
        }
        
        try {
          const currentUserData = await AsyncStorage.getItem('@FitnessApp:currentUser');
          if (currentUserData) {
            const user = JSON.parse(currentUserData);
            if (user && user.email) {
              console.log('Using email from AsyncStorage:', user.email);
              setUserEmail(user.email);
              setLoading(false);
              return;
            }
          }
        } catch (e) {
          console.error('Error parsing user data from AsyncStorage:', e);
        }
        
        console.log('No user email found, using <EMAIL>');
        setUserEmail('<EMAIL>');
        setLoading(false);
      } catch (error) {
        console.error('Error loading user email:', error);
        setError('Failed to load user data. Please try again.');
        setLoading(false);
      }
    };
    
    loadUserEmail();
  }, [emailFromParams, currentUser]);

  useEffect(() => {
    if (userEmail && !loading && timeRange) {
      loadUserAnalytics();
    }
  }, [userEmail, loading, timeRange]);

  // Initialize with sample data if no data exists
  useEffect(() => {
    const initializeSampleData = async () => {
      try {
        const workoutHistory = await AsyncStorage.getItem('@workout_history');
        if (!workoutHistory) {
          // Initialize with sample workout data
          const sampleWorkouts = [
            {
              type: 'Cardio',
              duration: 30,
              calories: 250,
              date: new Date().toISOString(),
              exercises: ['Running', 'Jumping Jacks', 'Burpees']
            },
            {
              type: 'Strength',
              duration: 45,
              calories: 180,
              date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
              exercises: ['Push-ups', 'Squats', 'Planks']
            },
            {
              type: 'Yoga',
              duration: 60,
              calories: 120,
              date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
              exercises: ['Sun Salutation', 'Warrior Pose', 'Tree Pose']
            }
          ];
          await AsyncStorage.setItem('@workout_history', JSON.stringify(sampleWorkouts));
        }

        const stepData = await AsyncStorage.getItem('@step_progress');
        if (!stepData) {
          // Initialize with sample step data
          const sampleSteps = [
            { date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], steps: 8500 },
            { date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], steps: 9200 },
            { date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], steps: 7800 },
            { date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], steps: 10500 },
            { date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], steps: 8900 },
            { date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0], steps: 9600 },
            { date: new Date().toISOString().split('T')[0], steps: 8200 }
          ];
          await AsyncStorage.setItem('@step_progress', JSON.stringify(sampleSteps));
        }
      } catch (error) {
        console.error('Error initializing sample data:', error);
      }
    };

    initializeSampleData();
  }, []);

  const loadUserAnalytics = async () => {
    try {
      setLoading(true);
      console.log('Loading analytics for user:', userEmail);
      
      // Get all user data from AsyncStorage
      const [
        workoutHistory,
        sleepData,
        waterData,
        stepData,
        userStats,
        nutritionData,
        planProgress,
        userProfile
      ] = await Promise.all([
        AsyncStorage.getItem('@workout_history'),
        AsyncStorage.getItem('@sleep_data'),
        AsyncStorage.getItem('@water_intake'),
        AsyncStorage.getItem('@step_progress'),
        AsyncStorage.getItem('@user_stats'),
        AsyncStorage.getItem('@nutrition_data'),
        AsyncStorage.getItem('@plan_performance'),
        AsyncStorage.getItem('@user_profile')
      ]);

      console.log('Data loaded:', {
        workoutHistory: !!workoutHistory,
        sleepData: !!sleepData,
        waterData: !!waterData,
        stepData: !!stepData,
        userStats: !!userStats,
        nutritionData: !!nutritionData,
        planProgress: !!planProgress,
        userProfile: !!userProfile
      });

      let workouts = [], sleep = [], water = [], steps = [], stats = {}, nutrition = [], plans = [], profile = {};
      
      try { workouts = workoutHistory ? JSON.parse(workoutHistory) : []; } 
      catch (e) { console.error('Error parsing workoutHistory:', e); }
      
      try { sleep = sleepData ? JSON.parse(sleepData) : []; } 
      catch (e) { console.error('Error parsing sleepData:', e); }
      
      try { water = waterData ? JSON.parse(waterData) : []; } 
      catch (e) { console.error('Error parsing waterData:', e); }
      
      try { steps = stepData ? JSON.parse(stepData) : []; } 
      catch (e) { console.error('Error parsing stepData:', e); }
      
      try { stats = userStats ? JSON.parse(userStats) : {}; } 
      catch (e) { console.error('Error parsing userStats:', e); }
      
      try { nutrition = nutritionData ? JSON.parse(nutritionData) : []; } 
      catch (e) { console.error('Error parsing nutritionData:', e); }
      
      try { plans = planProgress ? JSON.parse(planProgress) : []; } 
      catch (e) { console.error('Error parsing planProgress:', e); }
      
      try { profile = userProfile ? JSON.parse(userProfile) : {}; } 
      catch (e) { console.error('Error parsing userProfile:', e); }

      // Process real user data
      const today = new Date().toISOString().split('T')[0];
      const todayStats = {
        steps: 0,
        calories: 0,
        sleep: 0,
        water: 0,
        workouts: 0,
        heartRate: 72,
        distance: 0
      };

      // Get today's data from various sources
      const todayStepsKey = `@steps:${userEmail}:${today}`;
      const todayCaloriesKey = `@calories:${userEmail}:${today}`;
      const todayWaterKey = `@water:${userEmail}:${today}`;
      const todaySleepKey = `@sleep:${userEmail}:${today}`;
      const todayDistanceKey = `@distance:${userEmail}:${today}`;

      const [todaySteps, todayCalories, todayWater, todaySleep, todayDistance] = await Promise.all([
        AsyncStorage.getItem(todayStepsKey),
        AsyncStorage.getItem(todayCaloriesKey),
        AsyncStorage.getItem(todayWaterKey),
        AsyncStorage.getItem(todaySleepKey),
        AsyncStorage.getItem(todayDistanceKey)
      ]);

      todayStats.steps = todaySteps ? parseInt(todaySteps) : 0;
      todayStats.calories = todayCalories ? parseInt(todayCalories) : 0;
      todayStats.water = todayWater ? parseInt(todayWater) : 0;
      todayStats.sleep = todaySleep ? parseFloat(todaySleep) : 0;
      todayStats.distance = todayDistance ? parseFloat(todayDistance) : 0;

      // Calculate workout count for today
      const todayWorkouts = workouts.filter(workout => {
        const workoutDate = new Date(workout.date || workout.timestamp).toISOString().split('T')[0];
        return workoutDate === today;
      });
      todayStats.workouts = todayWorkouts.length;

      // Calculate weekly summary
      const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const weeklyWorkouts = workouts.filter(workout => {
        const workoutDate = new Date(workout.date || workout.timestamp);
        return workoutDate >= oneWeekAgo;
      });

      const weeklySteps = steps.filter(step => {
        const stepDate = new Date(step.date);
        return stepDate >= oneWeekAgo;
      });

      const weeklySleep = sleep.filter(sleepEntry => {
        const sleepDate = new Date(sleepEntry.date);
        return sleepDate >= oneWeekAgo;
      });

      const weeklyWater = water.filter(waterEntry => {
        const waterDate = new Date(waterEntry.date);
        return waterDate >= oneWeekAgo;
      });

      // Calculate totals
      const totalSteps = weeklySteps.reduce((sum, step) => sum + (step.steps || 0), 0);
      const totalCalories = weeklyWorkouts.reduce((sum, workout) => sum + (workout.caloriesBurned || 0), 0);
      const totalWater = weeklyWater.reduce((sum, water) => sum + (water.amount || 0), 0);
      const avgSleep = weeklySleep.length > 0 ? 
        weeklySleep.reduce((sum, sleep) => sum + (sleep.hours || 0), 0) / weeklySleep.length : 0;

      // Get workout types
      const workoutTypes = [...new Set(weeklyWorkouts.map(w => w.type || w.name))];
      const totalWorkouts = weeklyWorkouts.length;
      const totalDistance = weeklyWorkouts.reduce((sum, workout) => sum + (workout.distance || 0), 0);

      // Generate step progress data for the last 7 days
      const stepProgress = [];
      for (let i = 6; i >= 0; i--) {
        const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
        const dateStr = date.toISOString().split('T')[0];
        const daySteps = steps.find(step => step.date === dateStr);
        stepProgress.push({
          date: dateStr,
          steps: daySteps ? daySteps.steps : 0
        });
      }

      // If no data is found, provide sample data for demonstration
      if (totalSteps === 0 && totalCalories === 0 && totalWorkouts === 0) {
        console.log('No user data found, providing sample data');
        
        // Generate sample data for demonstration
        const sampleSteps = [8500, 9200, 7800, 10500, 8900, 9600, 8200];
        const sampleCalories = [450, 520, 380, 580, 470, 540, 420];
        const sampleSleep = [7.5, 8.0, 6.5, 7.8, 8.2, 7.0, 7.5];
        const sampleWater = [8, 10, 6, 9, 8, 11, 7];
        
        // Update today's stats with sample data
        todayStats.steps = sampleSteps[6] || 8200;
        todayStats.calories = sampleCalories[6] || 420;
        todayStats.sleep = sampleSleep[6] || 7.5;
        todayStats.water = sampleWater[6] || 7;
        todayStats.workouts = 2;
        todayStats.distance = 3.2;
        
        // Update weekly summary with sample data
        const sampleWeekSummary = {
          totalSteps: sampleSteps.reduce((sum, steps) => sum + steps, 0),
          totalCalories: sampleCalories.reduce((sum, calories) => sum + calories, 0),
          avgSleep: sampleSleep.reduce((sum, sleep) => sum + sleep, 0) / sampleSleep.length,
          totalWater: sampleWater.reduce((sum, water) => sum + water, 0),
          workoutTypes: ['Cardio', 'Strength', 'Yoga'],
          totalWorkouts: 5,
          totalDistance: 18.5
        };
        
        // Generate sample step progress
        const sampleStepProgress = sampleSteps.map((steps, index) => ({
          date: new Date(Date.now() - (6 - index) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          steps: steps
        }));

      setUserData({
        todayStats,
          weekSummary: sampleWeekSummary,
          workoutHistory: [
            { type: 'Cardio', duration: 30, calories: 250, date: new Date().toISOString() },
            { type: 'Strength', duration: 45, calories: 180, date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() },
            { type: 'Yoga', duration: 60, calories: 120, date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() }
          ],
          sleepData: sampleSleep.map((sleep, index) => ({
            date: new Date(Date.now() - (6 - index) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            hours: sleep
          })),
          waterIntake: sampleWater.map((water, index) => ({
            date: new Date(Date.now() - (6 - index) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            amount: water
          })),
          stepProgress: sampleStepProgress
        });
      } else {
        // Use real data
        setUserData({
          todayStats,
          weekSummary: {
            totalSteps,
            totalCalories,
            avgSleep,
            totalWater,
            workoutTypes: workoutTypes.length > 0 ? workoutTypes : ['No workouts'],
            totalWorkouts,
            totalDistance
          },
        workoutHistory: weeklyWorkouts,
        sleepData: weeklySleep,
        waterIntake: weeklyWater,
        stepProgress
        });
      }

      // Save analytics to admin dashboard
      await saveAnalyticsToAdmin(userEmail, {
        todayStats,
        weekSummary: {
          totalSteps,
          totalCalories,
          avgSleep,
          totalWater,
          workoutTypes,
          totalWorkouts,
          totalDistance
        },
        profile: profile,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error loading user analytics:', error);
      setError('Failed to load analytics data. Please try again.');
      
      // Provide fallback data even if there's an error
      const fallbackData = {
        todayStats: {
          steps: 8200,
          calories: 420,
          sleep: 7.5,
          water: 7,
          workouts: 2,
          heartRate: 72,
          distance: 3.2
        },
        weekSummary: {
          totalSteps: 63700,
          totalCalories: 3360,
          avgSleep: 7.5,
          totalWater: 59,
          workoutTypes: ['Cardio', 'Strength', 'Yoga'],
          totalWorkouts: 5,
          totalDistance: 18.5
        },
        workoutHistory: [
          { type: 'Cardio', duration: 30, calories: 250, date: new Date().toISOString() },
          { type: 'Strength', duration: 45, calories: 180, date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() }
        ],
        sleepData: [],
        waterIntake: [],
        stepProgress: Array(7).fill(0).map((_, i) => ({
          date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          steps: 8000 + Math.random() * 3000
        }))
      };
      
      setUserData(fallbackData);
    } finally {
      setLoading(false);
    }
  };

  const saveAnalyticsToAdmin = async (email, data) => {
    try {
      const adminData = await AsyncStorage.getItem('@admin_analytics') || '{}';
      const adminAnalytics = JSON.parse(adminData);
      adminAnalytics[email] = {
        ...data,
        lastUpdated: new Date().toISOString()
      };
      await AsyncStorage.setItem('@admin_analytics', JSON.stringify(adminAnalytics));
    } catch (error) {
      console.error('Error saving analytics to admin:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadUserAnalytics();
    setRefreshing(false);
  };

  const handleBack = () => {
    router.back();
  };

  const renderStatCard = (title, value, unit, icon, color) => (
    <View style={[styles.statCard, { borderLeftColor: color }]}>
      <View style={styles.statHeader}>
        <Ionicons name={icon} size={20} color={color} />
        <Text style={styles.statTitle}>{title}</Text>
      </View>
      <Text style={styles.statValue}>{value}{unit}</Text>
    </View>
  );

  const renderTimeRangeSelector = () => (
    <View style={styles.timeRangeContainer}>
      {['week', 'month', 'year'].map((range) => (
          <TouchableOpacity
            key={range}
            style={[
              styles.timeRangeButton,
              timeRange === range && styles.timeRangeButtonActive
            ]}
            onPress={() => setTimeRange(range)}
          >
            <Text style={[
            styles.timeRangeText,
            timeRange === range && styles.timeRangeTextActive
            ]}>
              {range.charAt(0).toUpperCase() + range.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
  );

  const renderChart = (title, data, type = 'bar') => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return (
        <Animated.View entering={FadeInUp.delay(300)} style={[styles.chartCard, styles.cardShadow]}>
          <Text style={styles.chartTitle}>{title}</Text>
          <View style={styles.noDataContainer}>
            <Ionicons name="bar-chart-outline" size={48} color="rgba(255,255,255,0.3)" />
            <Text style={styles.noDataText}>No data available</Text>
          </View>
        </Animated.View>
      );
    }

    const chartData = {
      labels: data.map(item => item.label || ''),
      datasets: [{
        data: data.map(item => {
          const value = typeof item.value === 'number' ? item.value : 0;
          return isFinite(value) ? value : 0;
        })
      }]
    };

    const chartWidth = width - 40;

    return (
      <Animated.View entering={FadeInUp.delay(300)} style={[styles.chartCard, styles.cardShadow]}>
        <Text style={styles.chartTitle}>{title}</Text>
        {type === 'line' ? (
          <LineChart
            data={chartData}
            width={chartWidth}
            height={200}
            chartConfig={{
              backgroundColor: 'transparent',
              backgroundGradientFrom: 'transparent',
              backgroundGradientTo: 'transparent',
              decimalPlaces: 0,
              color: (opacity = 1) => `rgba(102, 126, 234, ${opacity})`,
              labelColor: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
              style: {
                borderRadius: 16
              },
              strokeWidth: 2,
            }}
            bezier
            style={{
              marginVertical: 8,
              borderRadius: 16
            }}
          />
        ) : (
          <BarChart
            data={chartData}
            width={chartWidth}
            height={200}
            chartConfig={{
              backgroundColor: 'transparent',
              backgroundGradientFrom: 'transparent',
              backgroundGradientTo: 'transparent',
              decimalPlaces: 0,
              color: (opacity = 1) => `rgba(255, 107, 107, ${opacity})`,
              labelColor: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
              style: {
                borderRadius: 16
              },
              barPercentage: 0.7,
            }}
            style={{
              marginVertical: 8,
              borderRadius: 16
            }}
          />
        )}
      </Animated.View>
    );
  };

  const SectionDivider = () => (
    <View style={{ height: 1, backgroundColor: 'rgba(255,255,255,0.1)', marginVertical: 18, marginHorizontal: 16 }} />
  );

  if (!fontsLoaded) {
    return null;
  }

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient colors={['#0c0c0c', '#1a1a2e']} style={StyleSheet.absoluteFill} />
        <ActivityIndicator size="large" color="#667eea" />
        <Text style={styles.loadingText}>Loading your data...</Text>
        <Text style={styles.loadingSubtext}>This may take a moment</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient colors={['#0c0c0c', '#1a1a2e']} style={StyleSheet.absoluteFill} />
        <Ionicons name="warning" size={48} color="#FF6B6B" />
        <Text style={styles.errorTitle}>Oops! Something went wrong</Text>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={onRefresh}>
          <Text style={styles.retryButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      {[...Array(8)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <LinearGradient colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']} style={styles.backButtonGradient}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
            </LinearGradient>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Daily Analytics</Text>
          <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
            <LinearGradient colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']} style={styles.refreshButtonGradient}>
            <Ionicons name="refresh" size={24} color="#fff" />
            </LinearGradient>
          </TouchableOpacity>
        </View>

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor="#667eea"
              colors={["#667eea"]}
            />
          }
        >
          {error && (
            <Animated.View entering={FadeInUp.delay(100)} style={styles.errorCard}>
              <Ionicons name="warning" size={24} color="#FF6B6B" />
              <Text style={styles.errorText}>{error}</Text>
            </Animated.View>
          )}

          {/* Time Range Selector */}
          {renderTimeRangeSelector()}

          {/* Today's Summary */}
          <Animated.View entering={FadeInUp.delay(150)} style={styles.summaryCard}>
            <Text style={styles.summaryTitle}>Today's Progress</Text>
            <Text style={styles.summaryDate}>{new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</Text>
            
            <View style={styles.statsGrid}>
              {renderStatCard('Steps', userData.todayStats.steps.toLocaleString(), '', 'walk', '#4CAF50')}
              {renderStatCard('Calories', userData.todayStats.calories.toLocaleString(), ' cal', 'flame', '#FF5722')}
              {renderStatCard('Water', userData.todayStats.water, ' glasses', 'water', '#2196F3')}
              {renderStatCard('Sleep', userData.todayStats.sleep, ' hrs', 'moon', '#9C27B0')}
              {renderStatCard('Distance', userData.todayStats.distance, ' km', 'navigate', '#00BFFF')}
              {renderStatCard('Heart Rate', userData.todayStats.heartRate, ' BPM', 'heart', '#F44336')}
            </View>
          </Animated.View>
          <SectionDivider />

          {/* Weekly Summary */}
          <Animated.View entering={FadeInUp.delay(200)} style={[styles.summaryCard, styles.cardShadow]}>
            <Text style={styles.summaryTitle}>This Week's Summary</Text>
            
            <View style={styles.weeklyStats}>
              <View style={styles.weeklyStat}>
                <Ionicons name="walk" size={22} color="#4CAF50" style={{ marginBottom: 2 }} />
                <Text style={styles.weeklyStatValue}>{userData.weekSummary.totalSteps.toLocaleString()}</Text>
                <Text style={styles.weeklyStatLabel}>Total Steps</Text>
              </View>
              <View style={styles.weeklyStat}>
                <Ionicons name="flame" size={22} color="#FF5722" style={{ marginBottom: 2 }} />
                <Text style={styles.weeklyStatValue}>{userData.weekSummary.totalCalories.toLocaleString()}</Text>
                <Text style={styles.weeklyStatLabel}>Calories Burned</Text>
              </View>
              <View style={styles.weeklyStat}>
                <Ionicons name="moon" size={22} color="#9C27B0" style={{ marginBottom: 2 }} />
                <Text style={styles.weeklyStatValue}>{userData.weekSummary.avgSleep.toFixed(1)}</Text>
                <Text style={styles.weeklyStatLabel}>Avg Sleep (hrs)</Text>
              </View>
              <View style={styles.weeklyStat}>
                <Ionicons name="water" size={22} color="#2196F3" style={{ marginBottom: 2 }} />
                <Text style={styles.weeklyStatValue}>{userData.weekSummary.totalWater}</Text>
                <Text style={styles.weeklyStatLabel}>Water Glasses</Text>
              </View>
              <View style={styles.weeklyStat}>
                <Ionicons name="fitness" size={22} color="#667eea" style={{ marginBottom: 2 }} />
                <Text style={styles.weeklyStatValue}>{userData.weekSummary.totalWorkouts}</Text>
                <Text style={styles.weeklyStatLabel}>Workouts</Text>
              </View>
              <View style={styles.weeklyStat}>
                <Ionicons name="navigate" size={22} color="#00BFFF" style={{ marginBottom: 2 }} />
                <Text style={styles.weeklyStatValue}>{userData.weekSummary.totalDistance.toFixed(1)}</Text>
                <Text style={styles.weeklyStatLabel}>Distance (km)</Text>
              </View>
            </View>
          </Animated.View>
          <SectionDivider />

          {/* Charts */}
          {renderChart('Step Progress', userData.stepProgress, 'line')}
          {userData.workoutHistory.length > 0 && renderChart('Recent Workouts', userData.workoutHistory.slice(0, 7).map(workout => ({
            label: workout.type || 'Workout',
            value: workout.calories || 0
          })), 'bar')}

          {/* Recent Activity */}
          <Animated.View entering={FadeInUp.delay(400)} style={[styles.activityCard, styles.cardShadow]}>
            <Text style={styles.activityTitle}>Recent Activity</Text>
            {userData.workoutHistory.length > 0 ? (
              userData.workoutHistory.slice(0, 5).map((workout, index) => (
                <View key={index} style={styles.activityItem}>
                  <View style={styles.activityIcon}>
                    <Ionicons name="fitness" size={20} color="#667eea" />
                  </View>
                  <View style={styles.activityContent}>
                    <Text style={styles.activityName}>{workout.type || 'Workout'}</Text>
                    <Text style={styles.activityDetails}>
                      {workout.duration || 0} min • {workout.calories || 0} calories
                    </Text>
                  </View>
                  <Text style={styles.activityDate}>
                    {new Date(workout.date).toLocaleDateString()}
                  </Text>
                </View>
              ))
            ) : (
              <View style={styles.noActivityContainer}>
                <Ionicons name="fitness-outline" size={48} color="rgba(255,255,255,0.3)" />
                <Text style={styles.noActivityText}>No recent workouts</Text>
                <Text style={styles.noActivitySubtext}>Start your fitness journey today!</Text>
              </View>
            )}
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0c0c0c',
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    marginTop: 10,
  },
  loadingSubtext: {
    color: 'rgba(255,255,255,0.5)',
    fontSize: 14,
    marginTop: 5,
    fontFamily: 'Poppins_400Regular',
  },
  errorTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    fontFamily: 'Poppins_700Bold',
  },
  errorText: {
    color: '#FF6B6B',
    marginBottom: 20,
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  retryButton: {
    backgroundColor: 'rgba(102, 126, 234, 0.8)',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderWidth: 1,
    borderColor: 'rgba(102, 126, 234, 0.8)',
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    fontFamily: 'Poppins_700Bold',
  },
  noDataContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  noDataText: {
    color: 'rgba(255,255,255,0.5)',
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    marginTop: 10,
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(102, 126, 234, 0.6)',
    borderRadius: 2,
    zIndex: 0,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'rgba(255,255,255,0.05)',
    marginBottom: 10,
  },
  backButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  backButtonGradient: {
    padding: 10,
    borderRadius: 12,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: 'Poppins_700Bold',
  },
  refreshButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  refreshButtonGradient: {
    padding: 10,
    borderRadius: 12,
  },
  scrollContainer: {
    paddingBottom: 30,
    paddingTop: 10,
  },
  errorCard: {
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
    borderWidth: 1,
    borderColor: '#FF6B6B',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 20,
    marginTop: 5,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#FF6B6B',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  errorText: {
    color: '#FF6B6B',
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
  },
  timeRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: 16,
    marginBottom: 20,
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  timeRangeButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 2,
  },
  timeRangeButtonActive: {
    backgroundColor: 'rgba(102, 126, 234, 0.3)',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
  },
  timeRangeText: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
  },
  timeRangeTextActive: {
    color: '#fff',
    fontFamily: 'Poppins_700Bold',
  },
  summaryCard: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  cardShadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  summaryTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 6,
    fontFamily: 'Poppins_700Bold',
  },
  summaryDate: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 14,
    marginBottom: 20,
    fontFamily: 'Poppins_400Regular',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
    paddingBottom: 10,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginHorizontal: 0,
    paddingHorizontal: 8,
  },
  statCard: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    width: '48%',
    borderLeftWidth: 4,
    marginHorizontal: 2,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
    alignItems: 'center',
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    justifyContent: 'center',
  },
  statTitle: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 13,
    marginLeft: 6,
    fontFamily: 'Poppins_600SemiBold',
  },
  statValue: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: 'Poppins_700Bold',
    textAlign: 'center',
  },
  weeklyStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginHorizontal: -4,
  },
  weeklyStat: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 12,
    padding: 14,
    marginBottom: 24,
    width: '31%',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
    marginHorizontal: 24,
    borderTopWidth: 3,
    borderTopColor: '#667eea',
  },
  weeklyStatValue: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
    fontFamily: 'Poppins_700Bold',
  },
  weeklyStatLabel: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    textAlign: 'center',
    paddingHorizontal: 4,
  },
  chartCard: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginBottom: 24,
    borderWidth: 1,
    paddingHorizontal: 8,
    borderColor: 'rgba(255,255,255,0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 8,
    paddingVertical: 4,
  },
  chartTitle: {
    color: '#fff',
    fontSize: 13,
    fontWeight: 'bold',
    marginBottom: 14,
    fontFamily: 'Poppins_700Bold',
    lineHeight: 18,
  },
  activityCard: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 4,
    paddingVertical: 30,
    marginTop: 12,
    lineHeight: 18,
  },
  activityTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
    fontFamily: 'Poppins_700Bold',
    textAlign: 'center',
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 30,
    backgroundColor: 'rgba(255,255,255,0.03)',
    borderRadius: 18,
    marginTop: 8,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
    marginBottom: 8,
  },
  activityIcon: {
    width: 46,
    height: 8,
    borderRadius: 23,
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
  },
  activityContent: {
    flex: 1,
    paddingVertical: 4,
  },
  activityName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    fontFamily: 'Poppins_600SemiBold',
  },
  activityDetails: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 13,
    fontFamily: 'Poppins_400Regular',
    lineHeight: 18,
  },
  activityDate: {
    color: 'rgba(255,255,255,0.5)',
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
  },
  noActivityContainer: {
    alignItems: 'center',
    paddingVertical: 30,
    backgroundColor: 'rgba(255,255,255,0.03)',
    borderRadius: 12,
    marginTop: 10,
  },
  noActivityText: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 18,
    marginTop: 12,
    fontFamily: 'Poppins_600SemiBold',
    textAlign: 'center',
  },
  noActivitySubtext: {
    color: 'rgba(255,255,255,0.5)',
    fontSize: 14,
    marginTop: 8,
    fontFamily: 'Poppins_400Regular',
    textAlign: 'center',
    paddingHorizontal: 20,
  },
});