// This file is used in the project. Do NOT delete.
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  SafeAreaView,
  StatusBar,
  Animated as RNAnimated,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInLeft } from 'react-native-reanimated';

const { width, height } = Dimensions.get('window');

const AdvancedParticle = ({ style }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const speed = 4000 + Math.random() * 2000;
    const delay = Math.random() * 3000;

    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(particleAnim, { toValue: 1, duration: speed, useNativeDriver: true }),
        RNAnimated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = RNAnimated.sequence([
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: speed - 1000, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);

    setTimeout(() => {
      animation.start();
      RNAnimated.loop(opacitySequence).start();
    }, delay);

  }, []);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <RNAnimated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

export default function NotificationsPage() {
  const router = useRouter();
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    loadNotifications();
  }, []);

  const handleBack = () => {
    router.back();
  };

  const loadNotifications = () => {
    // Simulate loading notifications
    const mockNotifications = [
      {
        id: 1,
        type: 'achievement',
        title: '🏆 New Achievement Unlocked!',
        message: 'You completed your first 10,000 steps! Keep up the great work.',
        time: '2 hours ago',
        read: false,
      },
      {
        id: 2,
        type: 'reminder',
        title: '💧 Hydration Reminder',
        message: 'Time to drink some water! Stay hydrated throughout the day.',
        time: '4 hours ago',
        read: false,
      },
      {
        id: 3,
        type: 'workout',
        title: '🏋️ Workout Reminder',
        message: 'Your scheduled strength training session starts in 30 minutes.',
        time: '6 hours ago',
        read: true,
      },
      {
        id: 4,
        type: 'social',
        title: '👥 Friend Activity',
        message: 'Sarah completed a 5K run! Send her some encouragement.',
        time: '1 day ago',
        read: true,
      },
      {
        id: 5,
        type: 'nft',
        title: '🎉 NFT Ready to Mint!',
        message: 'You\'ve earned the "Workout Warrior" NFT. Mint it now!',
        time: '2 days ago',
        read: false,
      },
      {
        id: 6,
        type: 'class',
        title: '📹 Virtual Class Starting',
        message: 'Your registered HIIT class starts in 15 minutes. Join now!',
        time: '3 days ago',
        read: true,
      },
    ];

    setNotifications(mockNotifications);
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'achievement': return '🏆';
      case 'reminder': return '⏰';
      case 'workout': return '🏋️';
      case 'social': return '👥';
      case 'nft': return '🎉';
      case 'class': return '📹';
      default: return '📱';
    }
  };

  const markAsRead = (id) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const deleteNotification = (id) => {
    Alert.alert(
      'Delete Notification',
      'Are you sure you want to delete this notification?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => {
            setNotifications(prev => prev.filter(notif => notif.id !== id));
          }
        }
      ]
    );
  };

  const clearAllNotifications = () => {
    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to clear all notifications?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Clear All', 
          style: 'destructive',
          onPress: () => {
            setNotifications([]);
          }
        }
      ]
    );
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <View style={styles.container}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      {[...Array(20)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notifications</Text>
          <TouchableOpacity onPress={clearAllNotifications} style={styles.clearButton}>
            <Text style={styles.clearButtonText}>Clear All</Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
        >
          {notifications.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="notifications-off-outline" size={80} color="rgba(255,255,255,0.5)" />
              <Text style={styles.emptyTitle}>All Clear!</Text>
              <Text style={styles.emptySubtitle}>You have no new notifications.</Text>
            </View>
          ) : (
            notifications.map((notification, index) => (
              <Animated.View key={notification.id} entering={FadeInLeft.delay(index * 100).duration(600)}>
                <TouchableOpacity
                  style={[styles.notificationCard, !notification.read && styles.unreadCard]}
                  onPress={() => markAsRead(notification.id)}
                  onLongPress={() => deleteNotification(notification.id)}
                >
                  <View style={styles.iconContainer}>
                    <Text style={styles.notificationIcon}>{getNotificationIcon(notification.type)}</Text>
                  </View>
                  <View style={styles.notificationContent}>
                    <Text style={styles.notificationTitle}>{notification.title}</Text>
                    <Text style={styles.notificationMessage}>{notification.message}</Text>
                    <Text style={styles.notificationTime}>{notification.time}</Text>
                  </View>
                  {!notification.read && <View style={styles.unreadDot} />}
                </TouchableOpacity>
              </Animated.View>
            ))
          )}
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 5,
    zIndex: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 40,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 22,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  clearButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 10,
  },
  clearButtonText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
  },
  scrollContainer: {
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: height * 0.2,
  },
  emptyTitle: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: 'rgba(255,255,255,0.7)',
    marginTop: 20,
  },
  emptySubtitle: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.5)',
    marginTop: 10,
    textAlign: 'center',
  },
  notificationCard: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 16,
    padding: 15,
    marginBottom: 15,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  unreadCard: {
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    borderColor: 'rgba(102, 126, 234, 0.4)',
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  notificationIcon: {
    fontSize: 24,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  notificationMessage: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
    marginVertical: 4,
  },
  notificationTime: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
  },
  unreadDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#667eea',
    marginLeft: 10,
  },
});