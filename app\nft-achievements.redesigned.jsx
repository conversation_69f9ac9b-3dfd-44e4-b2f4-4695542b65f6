// This file is used in the project. Do NOT delete.
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Image,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInLeft, FadeInRight, FadeInDown } from 'react-native-reanimated';
import { useUser } from '../context/UserContext';
import {
  useFonts,
  Poppins_700Bold,
  Poppins_400Regular,
  Poppins_600SemiBold,
} from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SafeImage from '../components/SafeImage';
import { getImageByCategory } from '../constants/remoteImages';

const { width } = Dimensions.get('window');

export default function NFTAchievementsPage() {
  const router = useRouter();
  const { currentUser: user } = useUser();
  const [nftCollection, setNftCollection] = useState([]);
  const [availableNFTs, setAvailableNFTs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [userStats, setUserStats] = useState({
    totalNFTs: 0,
    rareNFTs: 0,
    totalValue: 0,
  });
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [achievements, setAchievements] = useState([]);

  let [fontsLoaded] = useFonts({
    Poppins_700Bold,
    Poppins_400Regular,
    Poppins_600SemiBold,
  });

  useEffect(() => {
    loadNFTData();
    // Simulate loading achievements
    const mockAchievements = [
      {
        id: 1,
        title: 'First Workout',
        description: 'Complete your first workout',
        category: 'beginner',
        rarity: 'common',
        image: { uri: getImageByCategory('exercise') },
        unlocked: true,
        date: '2024-01-15',
      },
      {
        id: 2,
        title: 'Week Warrior',
        description: 'Workout for 7 consecutive days',
        category: 'streak',
        rarity: 'rare',
        image: { uri: getImageByCategory('exercise') },
        unlocked: true,
        date: '2024-01-22',
      },
      {
        id: 3,
        title: 'Strength Master',
        description: 'Lift 100kg total weight',
        category: 'strength',
        rarity: 'epic',
        image: { uri: getImageByCategory('strength') },
        unlocked: false,
        progress: 75,
      },
      {
        id: 4,
        title: 'Cardio King',
        description: 'Run 10km in under 50 minutes',
        category: 'cardio',
        rarity: 'legendary',
        image: { uri: getImageByCategory('cardio') },
        unlocked: false,
        progress: 30,
      },
    ];
    setAchievements(mockAchievements);
  }, []);

  const loadNFTData = async () => {
    try {
      // Simulate loading NFT data
      setTimeout(() => {
        setNftCollection([
          {
            id: 1,
            name: 'First Steps',
            description: 'Completed your first 1000 steps',
            rarity: 'Common',
            image: '👟',
            earned: true,
            earnedDate: '2024-01-15',
            value: 0.1,
          },
          {
            id: 2,
            name: 'Hydration Hero',
            description: 'Drank 8 glasses of water in a day',
            rarity: 'Common',
            image: '💧',
            earned: true,
            earnedDate: '2024-01-20',
            value: 0.15,
          },
          {
            id: 3,
            name: 'Workout Warrior',
            description: 'Completed 10 workouts',
            rarity: 'Rare',
            image: '🏋️',
            earned: true,
            earnedDate: '2024-02-01',
            value: 0.5,
          },
          {
            id: 4,
            name: 'Sleep Master',
            description: 'Maintained 8+ hours sleep for 7 days',
            rarity: 'Epic',
            image: '😴',
            earned: true,
            earnedDate: '2024-02-10',
            value: 1.2,
          },
        ]);

        setAvailableNFTs([
          {
            id: 5,
            name: 'Marathon Legend',
            description: 'Complete a virtual marathon (42.2km)',
            rarity: 'Legendary',
            image: '🏃',
            earned: false,
            progress: 15.3,
            target: 42.2,
            value: 5.0,
          },
          {
            id: 6,
            name: 'Strength Titan',
            description: 'Lift 10,000kg total weight',
            rarity: 'Epic',
            image: '💪',
            earned: false,
            progress: 3420,
            target: 10000,
            value: 2.5,
          },
          {
            id: 7,
            name: 'Yoga Zen Master',
            description: 'Complete 50 yoga sessions',
            rarity: 'Rare',
            image: '🧘',
            earned: false,
            progress: 23,
            target: 50,
            value: 0.8,
          },
          {
            id: 8,
            name: 'Social Butterfly',
            description: 'Join 20 virtual classes',
            rarity: 'Rare',
            image: '🦋',
            earned: false,
            progress: 7,
            target: 20,
            value: 0.6,
          },
        ]);

        // Calculate user stats
        const earned = [
          { rarity: 'Common', value: 0.1 },
          { rarity: 'Common', value: 0.15 },
          { rarity: 'Rare', value: 0.5 },
          { rarity: 'Epic', value: 1.2 },
        ];

        setUserStats({
          totalNFTs: earned.length,
          rareNFTs: earned.filter(nft => nft.rarity !== 'Common').length,
          totalValue: earned.reduce((sum, nft) => sum + nft.value, 0),
        });

        setLoading(false);
      }, 1500);
    } catch (error) {
      console.error('Error loading NFT data:', error);
      setLoading(false);
    }
  };

  const getRarityColor = (rarity) => {
    switch (rarity.toLowerCase()) {
      case 'common': return '#9E9E9E';
      case 'rare': return '#2196F3';
      case 'epic': return '#9C27B0';
      case 'legendary': return '#FF9800';
      default: return '#9E9E9E';
    }
  };

  const getRarityGradient = (rarity) => {
    switch (rarity.toLowerCase()) {
      case 'common': return ['rgba(158, 158, 158, 0.8)', 'rgba(117, 117, 117, 0.8)'];
      case 'rare': return ['rgba(33, 150, 243, 0.8)', 'rgba(25, 118, 210, 0.8)'];
      case 'epic': return ['rgba(156, 39, 176, 0.8)', 'rgba(123, 31, 162, 0.8)'];
      case 'legendary': return ['rgba(255, 152, 0, 0.8)', 'rgba(245, 124, 0, 0.8)'];
      default: return ['rgba(158, 158, 158, 0.8)', 'rgba(117, 117, 117, 0.8)'];
    }
  };

  const viewNFTDetails = (nft) => {
    Alert.alert(
      `${nft.image} ${nft.name}`,
      `${nft.description}\n\nRarity: ${nft.rarity}\nValue: ${nft.value} ETH\n\n${nft.earned ? `Earned on: ${nft.earnedDate}` : `Progress: ${nft.progress}/${nft.target}`}`,
      [
        { text: 'Close', style: 'cancel' },
        nft.earned && { 
          text: 'Share Achievement', 
          onPress: () => {
            Alert.alert(
              '🎉 Achievement Shared!',
              'Your NFT achievement has been shared to your social fitness profile!',
              [{ text: 'Great!', style: 'default' }]
            );
          }
        }
      ].filter(Boolean)
    );
  };

  const mintNFT = (nft) => {
    if (nft.progress >= nft.target) {
      Alert.alert(
        '🎉 Mint NFT',
        `Congratulations! You've completed the requirements for "${nft.name}". Would you like to mint this NFT to your collection?`,
        [
          { text: 'Later', style: 'cancel' },
          { 
            text: 'Mint Now', 
            onPress: () => {
              Alert.alert(
                '✅ NFT Minted!',
                `"${nft.name}" has been successfully minted to your collection! It's now available in your wallet.`,
                [{ text: 'Awesome!', style: 'default' }]
              );
            }
          }
        ]
      );
    } else {
      Alert.alert(
        '📈 Keep Going!',
        `You're ${((nft.progress / nft.target) * 100).toFixed(1)}% of the way to earning "${nft.name}". Keep up the great work!`,
        [{ text: 'Got it!', style: 'default' }]
      );
    }
  };

  const categories = [
    { id: 'all', name: 'All', icon: 'trophy' },
    { id: 'beginner', name: 'Beginner', icon: 'star' },
    { id: 'streak', name: 'Streaks', icon: 'flame' },
    { id: 'strength', name: 'Strength', icon: 'barbell' },
    { id: 'cardio', name: 'Cardio', icon: 'fitness' },
  ];

  const filteredAchievements = selectedCategory === 'all' 
    ? achievements 
    : achievements.filter(achievement => achievement.category === selectedCategory);

  if (!fontsLoaded) {
    return (
      <View style={styles.container}>
        <LinearGradient 
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} 
          style={StyleSheet.absoluteFill}
        >
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#ffffff" />
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        </LinearGradient>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar style="light" />
        <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#ffffff" />
            <Text style={styles.loadingText}>Loading your NFT collection...</Text>
          </View>
        </LinearGradient>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* Background */}
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill}>
        <View style={styles.circle1} />
        <View style={styles.circle2} />
        <View style={styles.circle3} />
      </LinearGradient>
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>🏆 NFT Achievements</Text>
      </View>

      {/* Styled Back Button */}
      <Animated.View entering={FadeInLeft.delay(100)} style={styles.backWrapper}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
      </Animated.View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* User Stats */}
        <Animated.View entering={FadeInDown.delay(200)} style={styles.statsContainer}>
          <LinearGradient colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.8)']} style={styles.statsCard}>
            <Text style={styles.statsTitle}>Your Collection</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{userStats.totalNFTs}</Text>
                <Text style={styles.statLabel}>Total NFTs</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{userStats.rareNFTs}</Text>
                <Text style={styles.statLabel}>Rare+</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{userStats.totalValue.toFixed(2)}</Text>
                <Text style={styles.statLabel}>ETH Value</Text>
              </View>
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Category Filter */}
        <View style={styles.categoryContainer}>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoryScroll}
          >
            {categories.map((category, index) => (
              <Animated.View 
                key={category.id}
                entering={FadeInDown.delay(300 + index * 50)}
              >
                <TouchableOpacity
                  style={[
                    styles.categoryButton,
                    selectedCategory === category.id && styles.categoryButtonActive
                  ]}
                  onPress={() => setSelectedCategory(category.id)}
                >
                  <LinearGradient
                    colors={selectedCategory === category.id 
                      ? ['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.8)'] 
                      : ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']
                    }
                    style={styles.categoryGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <Ionicons 
                      name={category.icon} 
                      size={20} 
                      color={selectedCategory === category.id ? '#fff' : 'rgba(255, 255, 255, 0.8)'} 
                    />
                    <Text style={[
                      styles.categoryText,
                      selectedCategory === category.id && styles.categoryTextActive
                    ]}>
                      {category.name}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </ScrollView>
        </View>

        {/* Achievements Grid */}
        <View style={styles.achievementsContainer}>
          {filteredAchievements.map((achievement, index) => (
            <Animated.View 
              key={achievement.id}
              entering={FadeInRight.delay(400 + index * 100)}
              style={styles.achievementCardContainer}
            >
              <TouchableOpacity
                style={styles.achievementCard}
                onPress={() => Alert.alert(achievement.title, achievement.description)}
              >
                <LinearGradient
                  colors={achievement.unlocked 
                    ? getRarityGradient(achievement.rarity)
                    : ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']}
                  style={styles.achievementGradient}
                >
                  <View style={styles.achievementHeader}>
                    <View style={[
                      styles.rarityBadge, 
                      { backgroundColor: achievement.unlocked ? getRarityColor(achievement.rarity) : 'rgba(255, 255, 255, 0.2)' }
                    ]}>
                      <Text style={styles.rarityText}>{achievement.rarity}</Text>
                    </View>
                  </View>
                  
                  <View style={styles.achievementIcon}>
                    <Image 
                      source={achievement.image} 
                      style={[
                        styles.achievementImage,
                        !achievement.unlocked && styles.achievementImageLocked
                      ]} 
                    />
                  </View>
                  
                  <Text style={[
                    styles.achievementTitle,
                    !achievement.unlocked && styles.achievementTitleLocked
                  ]}>
                    {achievement.title}
                  </Text>
                  
                  <Text style={[
                    styles.achievementDescription,
                    !achievement.unlocked && styles.achievementDescriptionLocked
                  ]}>
                    {achievement.description}
                  </Text>
                  
                  {!achievement.unlocked ? (
                    <View style={styles.progressContainer}>
                      <View style={styles.progressBar}>
                        <View 
                          style={[
                            styles.progressFill, 
                            { width: `${achievement.progress}%` }
                          ]} 
                        />
                      </View>
                      <Text style={styles.progressText}>{achievement.progress}% Complete</Text>
                    </View>
                  ) : (
                    <Text style={styles.unlockDate}>Unlocked: {achievement.date}</Text>
                  )}
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>
          ))}
        </View>

        {/* Your NFT Collection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Your NFT Collection</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.nftScrollContent}
          >
            {nftCollection.map((nft, index) => (
              <Animated.View 
                key={nft.id}
                entering={FadeInRight.delay(500 + index * 100)}
              >
                <TouchableOpacity
                  style={styles.nftCard}
                  onPress={() => viewNFTDetails(nft)}
                >
                  <LinearGradient
                    colors={getRarityGradient(nft.rarity)}
                    style={styles.nftCardGradient}
                  >
                    <Text style={styles.nftEmoji}>{nft.image}</Text>
                    <Text style={styles.nftName}>{nft.name}</Text>
                    <View style={[styles.nftRarityBadge, { backgroundColor: getRarityColor(nft.rarity) }]}>
                      <Text style={styles.nftRarityText}>{nft.rarity}</Text>
                    </View>
                    <Text style={styles.nftValue}>{nft.value} ETH</Text>
                  </LinearGradient>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </ScrollView>
        </View>

        {/* Available NFTs */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Available to Earn</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.nftScrollContent}
          >
            {availableNFTs.map((nft, index) => (
              <Animated.View 
                key={nft.id}
                entering={FadeInRight.delay(600 + index * 100)}
              >
                <TouchableOpacity
                  style={styles.nftCard}
                  onPress={() => mintNFT(nft)}
                >
                  <LinearGradient
                    colors={['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']}
                    style={styles.nftCardGradient}
                  >
                    <Text style={styles.nftEmoji}>{nft.image}</Text>
                    <Text style={styles.nftName}>{nft.name}</Text>
                    <View style={[styles.nftRarityBadge, { backgroundColor: getRarityColor(nft.rarity) }]}>
                      <Text style={styles.nftRarityText}>{nft.rarity}</Text>
                    </View>
                    <Text style={styles.nftValue}>{nft.value} ETH</Text>
                    
                    <View style={styles.nftProgressContainer}>
                      <View style={styles.nftProgressBar}>
                        <View 
                          style={[
                            styles.nftProgressFill, 
                            { width: `${(nft.progress / nft.target) * 100}%` }
                          ]} 
                        />
                      </View>
                      <Text style={styles.nftProgressText}>
                        {nft.progress}/{nft.target} ({((nft.progress / nft.target) * 100).toFixed(0)}%)
                      </Text>
                    </View>
                  </LinearGradient>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </ScrollView>
        </View>

        {/* NFT Marketplace Teaser */}
        <Animated.View entering={FadeInDown.delay(700)} style={styles.marketplaceContainer}>
          <LinearGradient
            colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.8)']}
            style={styles.marketplaceCard}
          >
            <Text style={styles.marketplaceTitle}>NFT Marketplace</Text>
            <Text style={styles.marketplaceDescription}>
              Trade your fitness NFTs or purchase exclusive items to enhance your fitness journey.
            </Text>
            <TouchableOpacity 
              style={styles.marketplaceButton}
              onPress={() => Alert.alert('Coming Soon', 'The NFT Marketplace will be available in the next update!')}
            >
              <Text style={styles.marketplaceButtonText}>Coming Soon</Text>
            </TouchableOpacity>
          </LinearGradient>
        </Animated.View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  // Background Elements
  circle1: {
    position: 'absolute',
    top: -100,
    right: -100,
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  circle2: {
    position: 'absolute',
    bottom: -50,
    left: -50,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
  },
  circle3: {
    position: 'absolute',
    top: '40%',
    right: -30,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.06)',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(5),
    paddingVertical: hp(1.5),
    paddingTop: Platform.OS === 'ios' ? hp(6) : hp(4),
  },
  backWrapper: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? hp(6) : hp(4),
    left: wp(5),
    zIndex: 10,
  },
  backButton: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: wp(7),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: wp(5),
    paddingBottom: hp(3),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: hp(2),
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
  },
  // Stats Section
  statsContainer: {
    marginBottom: hp(3),
  },
  statsCard: {
    borderRadius: 16,
    padding: wp(5),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statsTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(2),
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: wp(6),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(0.5),
  },
  statLabel: {
    fontSize: wp(3),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  // Category Filter
  categoryContainer: {
    marginBottom: hp(3),
  },
  categoryScroll: {
    paddingRight: wp(5),
  },
  categoryButton: {
    marginRight: wp(2),
    borderRadius: 12,
    overflow: 'hidden',
  },
  categoryButtonActive: {
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  categoryGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(4),
    paddingVertical: hp(1.2),
  },
  categoryText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.8)',
    marginLeft: wp(2),
  },
  categoryTextActive: {
    color: '#fff',
  },
  // Achievements
  achievementsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: hp(3),
  },
  achievementCardContainer: {
    width: (width - wp(15)) / 2,
    marginBottom: hp(2),
  },
  achievementCard: {
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  achievementGradient: {
    padding: wp(4),
    alignItems: 'center',
  },
  achievementHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    width: '100%',
    marginBottom: hp(1.5),
  },
  rarityBadge: {
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.5),
    borderRadius: 12,
  },
  rarityText: {
    fontSize: wp(2.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  achievementIcon: {
    width: wp(15),
    height: wp(15),
    borderRadius: wp(7.5),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp(1.5),
  },
  achievementImage: {
    width: wp(8),
    height: wp(8),
    tintColor: '#fff',
  },
  achievementImageLocked: {
    tintColor: 'rgba(255, 255, 255, 0.5)',
  },
  achievementTitle: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: hp(0.8),
  },
  achievementTitleLocked: {
    color: 'rgba(255, 255, 255, 0.6)',
  },
  achievementDescription: {
    fontSize: wp(2.8),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: hp(1.5),
    lineHeight: hp(2),
  },
  achievementDescriptionLocked: {
    color: 'rgba(255, 255, 255, 0.5)',
  },
  progressContainer: {
    width: '100%',
    marginBottom: hp(1),
  },
  progressBar: {
    height: hp(0.8),
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: hp(0.5),
  },
  progressFill: {
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 4,
  },
  progressText: {
    fontSize: wp(2.5),
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  unlockDate: {
    fontSize: wp(2.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  // NFT Collection
  section: {
    marginBottom: hp(3),
  },
  sectionTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(2),
  },
  nftScrollContent: {
    paddingRight: wp(5),
  },
  nftCard: {
    width: wp(40),
    marginRight: wp(3),
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  nftCardGradient: {
    padding: wp(4),
    alignItems: 'center',
  },
  nftEmoji: {
    fontSize: wp(10),
    marginBottom: hp(1),
  },
  nftName: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: hp(1),
  },
  nftRarityBadge: {
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.5),
    borderRadius: 12,
    marginBottom: hp(1),
  },
  nftRarityText: {
    fontSize: wp(2.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  nftValue: {
    fontSize: wp(3),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginBottom: hp(1),
  },
  nftProgressContainer: {
    width: '100%',
  },
  nftProgressBar: {
    height: hp(0.8),
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: hp(0.5),
  },
  nftProgressFill: {
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 4,
  },
  nftProgressText: {
    fontSize: wp(2.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  // Marketplace
  marketplaceContainer: {
    marginBottom: hp(3),
  },
  marketplaceCard: {
    borderRadius: 16,
    padding: wp(5),
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  marketplaceTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(1),
  },
  marketplaceDescription: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: hp(2),
    lineHeight: hp(2.5),
  },
  marketplaceButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: wp(5),
    paddingVertical: hp(1.2),
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  marketplaceButtonText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  // Empty State
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(6),
  },
  emptyStateTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginTop: hp(2),
    marginBottom: hp(1),
  },
  emptyStateText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    paddingHorizontal: wp(10),
  },
});