import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  SafeAreaView,
  StatusBar,
  Animated as RNAnimated,
  Dimensions,
  ActivityIndicator,
  Platform
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { Poppins_400Regular, Poppins_700Bold, Poppins_600SemiBold, useFonts } from '@expo-google-fonts/poppins';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useUser } from '../../context/UserContext';

const { width, height } = Dimensions.get('window');

const AdvancedParticle = ({ style }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const speed = 4000 + Math.random() * 2000;
    const delay = Math.random() * 3000;

    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(particleAnim, { toValue: 1, duration: speed, useNativeDriver: true }),
        RNAnimated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = RNAnimated.sequence([
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: speed - 1000, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);

    setTimeout(() => {
      animation.start();
      RNAnimated.loop(opacitySequence).start();
    }, delay);

  }, []);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <RNAnimated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

export default function UserProfile() {
  const router = useRouter();
  const { currentUser, loading } = useUser();
  const [userData, setUserData] = useState(null);
  const [loadingData, setLoadingData] = useState(true);

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_700Bold,
    Poppins_600SemiBold,
  });

  useEffect(() => {
    if (!loading && !currentUser) {
      router.replace('/user/login');
    } else if (currentUser) {
      loadUserData();
    }
  }, [currentUser, loading]);

  const loadUserData = async () => {
    try {
      setLoadingData(true);
      // Load additional user data from AsyncStorage
      const userDataString = await AsyncStorage.getItem(`@user_data:${currentUser.email}`);
      if (userDataString) {
        const data = JSON.parse(userDataString);
        setUserData(data);
      } else {
        setUserData(currentUser);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
      setUserData(currentUser);
    } finally {
      setLoadingData(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleEditProfile = () => {
    router.push('/profile/updateprofile');
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('@current_user');
              await AsyncStorage.removeItem('@FitnessApp:currentUser');
              router.replace('/user/login');
            } catch (error) {
              console.error('Error during logout:', error);
              router.replace('/user/login');
            }
          }
        }
      ]
    );
  };

  const getProfileIcon = (type) => {
    switch (type) {
      case 'personal': return '👤';
      case 'fitness': return '💪';
      case 'goals': return '🎯';
      case 'stats': return '📊';
      default: return '📱';
    }
  };

  if (!fontsLoaded || loading || loadingData) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#0c0c0c' }}>
        <LinearGradient colors={['#0c0c0c', '#1a1a2e']} style={StyleSheet.absoluteFill} />
        <ActivityIndicator size="large" color="#fff" />
      </View>
    );
  }

  const profileSections = [
    {
      id: 'personal',
      title: 'Personal Information',
      icon: 'person-outline',
      items: [
        { label: 'Name', value: userData?.name || 'Not set', icon: 'person' },
        { label: 'Email', value: userData?.email || 'Not set', icon: 'mail' },
        { label: 'Gender', value: userData?.gender || 'Not set', icon: 'male-female' },
      ]
    },
    {
      id: 'fitness',
      title: 'Fitness Details',
      icon: 'fitness-outline',
      items: [
        { label: 'Height', value: userData?.height ? `${userData.height} cm` : 'Not set', icon: 'resize' },
        { label: 'Weight', value: userData?.weight ? `${userData.weight} kg` : 'Not set', icon: 'scale' },
        { label: 'Target Weight', value: userData?.targetWeight ? `${userData.targetWeight} kg` : 'Not set', icon: 'trending-up' },
      ]
    },
    {
      id: 'goals',
      title: 'Goals & Preferences',
      icon: 'flag-outline',
      items: [
        { label: 'Activity Level', value: userData?.activityLevel || 'Not set', icon: 'speedometer' },
        { label: 'Fitness Level', value: userData?.fitnessLevel || 'Not set', icon: 'trophy' },
        { label: 'Target Areas', value: userData?.targetAreas?.length > 0 ? userData.targetAreas.join(', ') : 'Not set', icon: 'body' },
      ]
    }
  ];

  return (
    <View style={styles.container}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      {[...Array(20)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profile</Text>
          <TouchableOpacity onPress={handleEditProfile} style={styles.editButton}>
            <Ionicons name="create-outline" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
        >
          {/* Profile Avatar Section */}
          <Animated.View entering={FadeInUp.delay(100).duration(600)} style={styles.avatarSection}>
            <View style={styles.avatarContainer}>
              <Text style={styles.avatarText}>
                {userData?.name ? userData.name.charAt(0).toUpperCase() : 'U'}
              </Text>
            </View>
            <Text style={styles.userName}>{userData?.name || 'User'}</Text>
            <Text style={styles.userEmail}>{userData?.email || '<EMAIL>'}</Text>
          </Animated.View>

          {/* Profile Sections */}
          {profileSections.map((section, index) => (
            <Animated.View key={section.id} entering={FadeInUp.delay((index + 2) * 100).duration(600)}>
              <View style={styles.sectionHeader}>
                <Ionicons name={section.icon} size={20} color="#667eea" />
                <Text style={styles.sectionTitle}>{section.title}</Text>
              </View>
              
              <View style={styles.sectionCard}>
                {section.items.map((item, itemIndex) => (
                  <View key={itemIndex} style={styles.profileItem}>
                    <View style={styles.itemIcon}>
                      <Ionicons name={item.icon} size={16} color="rgba(255,255,255,0.6)" />
                    </View>
                    <View style={styles.itemContent}>
                      <Text style={styles.itemLabel}>{item.label}</Text>
                      <Text style={styles.itemValue}>{item.value}</Text>
                    </View>
                  </View>
                ))}
              </View>
            </Animated.View>
          ))}

          {/* Action Buttons */}
          <Animated.View entering={FadeInUp.delay(500).duration(600)} style={styles.actionButtons}>
            <TouchableOpacity style={styles.actionButton} onPress={handleEditProfile}>
              <Ionicons name="create-outline" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Edit Profile</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={[styles.actionButton, styles.logoutButton]} onPress={handleLogout}>
              <Ionicons name="log-out-outline" size={20} color="#FF5252" />
              <Text style={[styles.actionButtonText, styles.logoutText]}>Logout</Text>
            </TouchableOpacity>
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 5,
    zIndex: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 40,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 22,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  editButton: {
    padding: 8,
  },
  scrollContainer: {
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  avatarSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(102,126,234,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    borderWidth: 2,
    borderColor: 'rgba(102,126,234,0.3)',
  },
  avatarText: {
    fontSize: 32,
    fontFamily: 'Poppins_700Bold',
    color: '#667eea',
  },
  userName: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 5,
  },
  userEmail: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.7)',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginLeft: 10,
  },
  sectionCard: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 16,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  profileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.05)',
  },
  itemIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  itemContent: {
    flex: 1,
  },
  itemLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
    marginBottom: 2,
  },
  itemValue: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  actionButtons: {
    marginTop: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 12,
    paddingVertical: 15,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  actionButtonText: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginLeft: 10,
  },
  logoutButton: {
    backgroundColor: 'rgba(255,82,82,0.1)',
    borderColor: 'rgba(255,82,82,0.3)',
  },
  logoutText: {
    color: '#FF5252',
  },
});