<<<<<<<
/**



 * Utility functions for media handling in the fitness app



 */







export interface MediaItem {



  id: string;



  type: 'image' | 'video' | 'youtube';



  source: string;



  title?: string;



  description?: string;



  fallbackSource?: string;



  thumbnail?: string;



}







/**



 * Extract YouTube video ID from various URL formats



 */



export const extractYoutubeId = (url: string): string | null => {



  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;



  const match = url.match(regExp);



  return match && match[2].length === 11 ? match[2] : null;



};







/**



 * Convert YouTube watch URL to embed URL



 */



export const getYoutubeEmbedUrl = (url: string): string | null => {



  const videoId = extractYoutubeId(url);



  if (!videoId) return null;



  return `https://www.youtube.com/embed/${videoId}?autoplay=0&rel=0&showinfo=0&controls=1`;



};







/**



 * Get YouTube thumbnail URL



 */



export const getYoutubeThumbnail = (url: string, quality: 'default' | 'medium' | 'high' | 'maxres' = 'medium'): string | null => {



  const videoId = extractYoutubeId(url);



  if (!videoId) return null;



  return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;



};







/**



 * Validate if a URL is a valid image URL



 */



export const isValidImageUrl = (url: string): boolean => {



  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];



  const lowercaseUrl = url.toLowerCase();



  return imageExtensions.some(ext => lowercaseUrl.includes(ext)) || 



         lowercaseUrl.includes('images.') || 



         lowercaseUrl.includes('image') ||



         lowercaseUrl.includes('photo');



};







/**



 * Validate if a URL is a valid video URL



 */



export const isValidVideoUrl = (url: string): boolean => {



  const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];



  const lowercaseUrl = url.toLowerCase();



  return videoExtensions.some(ext => lowercaseUrl.includes(ext)) ||



         lowercaseUrl.includes('video');



};







/**



 * Validate if a URL is a YouTube URL



 */



export const isYoutubeUrl = (url: string): boolean => {



  return url.includes('youtube.com') || url.includes('youtu.be');



};







/**



 * Determine media type from URL



 */



export const getMediaType = (url: string): 'image' | 'video' | 'youtube' => {



  if (isYoutubeUrl(url)) return 'youtube';



  if (isValidVideoUrl(url)) return 'video';



  return 'image'; // Default to image



};







/**



 * Create a media item from a URL



 */



export const createMediaItem = (



  id: string,



  source: string,



  title?: string,



  description?: string,



  fallbackSource?: string



): MediaItem => {



  const type = getMediaType(source);



  const item: MediaItem = {



    id,



    type,



    source,



    title,



    description,



    fallbackSource,



  };







  // Add thumbnail for YouTube videos



  if (type === 'youtube') {



    item.thumbnail = getYoutubeThumbnail(source);



  }







  return item;



};







/**



 * Sample fitness-related media data



 */



export const getFitnessMediaSamples = (): MediaItem[] => [



  createMediaItem(



    '1',



    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',



    'Strength Training',



    'Build muscle and increase strength',



    'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400&h=300&fit=crop'



  ),



  createMediaItem(



    '2',



    'https://www.youtube.com/watch?v=tWjBnQX3if0',



    '5 Minute Push Ups Workout',



    'Quick and effective push-up routine'



  ),



  createMediaItem(



    '3',



    'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop',



    'Cardio Workout',



    'Improve cardiovascular health',



    'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=400&h=300&fit=crop'



  ),



  createMediaItem(



    '4',



    'https://www.youtube.com/watch?v=HHRDXEG1YCU',



    'Push-ups for Beginners',



    'Learn proper push-up form'



  ),



];







/**



 * Error handling for media loading



 */



export const handleMediaError = (error: any, mediaType: string, source: string) => {



  console.error(`Media Error (${mediaType}):`, {



    error,



    source,



    timestamp: new Date().toISOString(),



  });



  



  // You can add analytics or crash reporting here



  // Example: Analytics.track('media_load_error', { mediaType, source, error: error.message });



};







/**



 * Cache configuration for FastImage



 */



export const getImageCacheConfig = () => ({



  priority: 'normal' as const,



  cache: 'immutable' as const,



});







/**



 * Default fallback images for different contexts



 */



export const getFallbackImages = () => ({



  workout: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400&h=300&fit=crop',



  profile: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=300&fit=crop',



  exercise: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',



  nutrition: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=400&h=300&fit=crop',



});







/**



 * Resize image URL for different screen densities



 */



export const getOptimizedImageUrl = (url: string, width: number, height: number): string => {



  // For Unsplash images



  if (url.includes('unsplash.com')) {



    return `${url}&w=${width}&h=${height}&fit=crop&dpr=2`;



  }



  



  // For Pexels images



  if (url.includes('pexels.com')) {



    return `${url}?auto=compress&w=${width}&h=${height}&fit=crop`;



  }



  



  // Return original URL if no optimization available



  return url;



};







/**



 * Preload images for better performance



 */



export const preloadImages = async (urls: string[]): Promise<void> => {



  try {



    const FastImage = require('react-native-fast-image').default;



    await FastImage.preload(



      urls.map(url => ({



        uri: url,



        priority: FastImage.priority.normal,



      }))



    );



  } catch (error) {



    console.warn('Image preloading failed:', error);



  }



};



=======
/**

 * Utility functions for media handling in the fitness app

 */



export interface MediaItem {

  id: string;

  type: 'image' | 'video' | 'youtube';

  source: string;

  title?: string;

  description?: string;

  fallbackSource?: string;

  thumbnail?: string;

}



/**

 * Extract YouTube video ID from various URL formats

 */

export const extractYoutubeId = (url: string): string | null => {

  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;

  const match = url.match(regExp);

  return match && match[2].length === 11 ? match[2] : null;

};



/**

 * Convert YouTube watch URL to embed URL

 */

export const getYoutubeEmbedUrl = (url: string): string | null => {

  const videoId = extractYoutubeId(url);

  if (!videoId) return null;

  return `https://www.youtube.com/embed/${videoId}?autoplay=0&rel=0&showinfo=0&controls=1`;

};



/**

 * Get YouTube thumbnail URL

 */

export const getYoutubeThumbnail = (url: string, quality: 'default' | 'medium' | 'high' | 'maxres' = 'medium'): string | null => {

  const videoId = extractYoutubeId(url);

  if (!videoId) return null;

  return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;

};



/**

 * Validate if a URL is a valid image URL

 */

export const isValidImageUrl = (url: string): boolean => {

  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];

  const lowercaseUrl = url.toLowerCase();

  return imageExtensions.some(ext => lowercaseUrl.includes(ext)) || 

         lowercaseUrl.includes('images.') || 

         lowercaseUrl.includes('image') ||

         lowercaseUrl.includes('photo');

};



/**

 * Validate if a URL is a valid video URL

 */

export const isValidVideoUrl = (url: string): boolean => {

  const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];

  const lowercaseUrl = url.toLowerCase();

  return videoExtensions.some(ext => lowercaseUrl.includes(ext)) ||

         lowercaseUrl.includes('video');

};



/**

 * Validate if a URL is a YouTube URL

 */

export const isYoutubeUrl = (url: string): boolean => {

  return url.includes('youtube.com') || url.includes('youtu.be');

};



/**

 * Determine media type from URL

 */

export const getMediaType = (url: string): 'image' | 'video' | 'youtube' => {

  if (isYoutubeUrl(url)) return 'youtube';

  if (isValidVideoUrl(url)) return 'video';

  return 'image'; // Default to image

};



/**

 * Create a media item from a URL

 */

export const createMediaItem = (

  id: string,

  source: string,

  title?: string,

  description?: string,

  fallbackSource?: string

): MediaItem => {

  const type = getMediaType(source);

  const item: MediaItem = {

    id,

    type,

    source,

    title,

    description,

    fallbackSource,

  };



  // Add thumbnail for YouTube videos

  if (type === 'youtube') {

    const thumbnail = getYoutubeThumbnail(source);

    if (thumbnail) {

      item.thumbnail = thumbnail;

    }

  }



  return item;

};



/**

 * Sample fitness-related media data

 */

export const getFitnessMediaSamples = (): MediaItem[] => [

  createMediaItem(

    '1',

    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',

    'Strength Training',

    'Build muscle and increase strength',

    'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400&h=300&fit=crop'

  ),

  createMediaItem(

    '2',

    'https://www.youtube.com/watch?v=tWjBnQX3if0',

    '5 Minute Push Ups Workout',

    'Quick and effective push-up routine'

  ),

  createMediaItem(

    '3',

    'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop',

    'Cardio Workout',

    'Improve cardiovascular health',

    'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=400&h=300&fit=crop'

  ),

  createMediaItem(

    '4',

    'https://www.youtube.com/watch?v=HHRDXEG1YCU',

    'Push-ups for Beginners',

    'Learn proper push-up form'

  ),

];



/**

 * Error handling for media loading

 */

export const handleMediaError = (error: any, mediaType: string, source: string) => {

  console.error(`Media Error (${mediaType}):`, {

    error,

    source,

    timestamp: new Date().toISOString(),

  });

  

  // You can add analytics or crash reporting here

  // Example: Analytics.track('media_load_error', { mediaType, source, error: error.message });

};



/**

 * Cache configuration for FastImage

 */

export const getImageCacheConfig = () => ({

  priority: 'normal' as const,

  cache: 'immutable' as const,

});



/**

 * Default fallback images for different contexts

 */

export const getFallbackImages = () => ({

  workout: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400&h=300&fit=crop',

  profile: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=300&fit=crop',

  exercise: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',

  nutrition: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=400&h=300&fit=crop',

});



/**

 * Resize image URL for different screen densities

 */

export const getOptimizedImageUrl = (url: string, width: number, height: number): string => {

  // For Unsplash images

  if (url.includes('unsplash.com')) {

    return `${url}&w=${width}&h=${height}&fit=crop&dpr=2`;

  }

  

  // For Pexels images

  if (url.includes('pexels.com')) {

    return `${url}?auto=compress&w=${width}&h=${height}&fit=crop`;

  }

  

  // Return original URL if no optimization available

  return url;

};



/**

 * Preload images for better performance

 */

export const preloadImages = async (urls: string[]): Promise<void> => {

  try {

    const FastImage = require('react-native-fast-image').default;

    await FastImage.preload(

      urls.map(url => ({

        uri: url,

        priority: FastImage.priority.normal,

      }))

    );

  } catch (error) {

    console.warn('Image preloading failed:', error);

  }

};

>>>>>>>
