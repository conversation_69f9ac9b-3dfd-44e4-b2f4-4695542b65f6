import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Platform,
  SafeAreaView,
  Alert,
  Dimensions,
  ActivityIndicator,
  TextInput,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useUser } from '../../context/UserContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StatusBar } from 'expo-status-bar';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { Animated as RNAnimated } from 'react-native';
import {
  useFonts,
  Poppins_700Bold,
  Poppins_400Regular,
  Poppins_600SemiBold,
} from '@expo-google-fonts/poppins';

const { width, height } = Dimensions.get('window');

const GENDER_OPTIONS = [
  { id: 'male', label: 'Male', icon: 'male-outline' },
  { id: 'female', label: 'Female', icon: 'female-outline' },
  { id: 'other', label: 'Other', icon: 'person-outline' },
];

const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;
  const scaleAnim = useRef(new RNAnimated.Value(0)).current;
  const rotationAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.parallel([
          RNAnimated.timing(particleAnim, {
            toValue: 1,
            duration: (4000 + Math.random() * 3000) * speed,
            useNativeDriver: true,
          }),
          RNAnimated.timing(opacityAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
          RNAnimated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 40,
            useNativeDriver: true,
          }),
          RNAnimated.timing(rotationAnim, {
            toValue: 1,
            duration: 3000 * speed,
            useNativeDriver: true,
          }),
        ]),
        RNAnimated.parallel([
          RNAnimated.timing(particleAnim, {
            toValue: 0,
            duration: (4000 + Math.random() * 3000) * speed,
            useNativeDriver: true,
          }),
          RNAnimated.timing(opacityAnim, {
            toValue: 0,
            duration: 1500,
            useNativeDriver: true,
          }),
          RNAnimated.spring(scaleAnim, {
            toValue: 0,
            friction: 8,
            tension: 40,
            useNativeDriver: true,
          }),
          RNAnimated.timing(rotationAnim, {
            toValue: 0,
            duration: 3000 * speed,
            useNativeDriver: true,
          }),
        ]),
      ])
    );
    setTimeout(() => {
      animation.start();
    }, delay);
    return () => animation.stop();
  }, []);

  const particleStyle = {
    transform: [
      {
        translateY: particleAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0, -100],
        }),
      },
      {
        scale: scaleAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0.3, 1],
        }),
      },
      {
        rotate: rotationAnim.interpolate({
          inputRange: [0, 1],
          outputRange: ['0deg', '360deg'],
        }),
      },
    ],
    opacity: opacityAnim,
  };

  return (
    <RNAnimated.View style={[styles.particle, particleStyle, style]}>
      <LinearGradient
        colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
        style={styles.particleGradient}
      />
    </RNAnimated.View>
  );
};

export default function Gender() {
  const { 
    gender, setGender, 
    age, setAge, 
    targetHeight, setTargetHeight, 
    setUserHistory, 
    currentUser,
    updateCurrentUser
  } = useUser();
  const router = useRouter();
  const userEmail = currentUser?.email;
  const [loading, setLoading] = useState(true);
  const [selectedGender, setSelectedGender] = useState(gender || '');
  const fadeAnim = useRef(new RNAnimated.Value(0)).current;
  const slideAnim = useRef(new RNAnimated.Value(50)).current;
  const scaleAnim = useRef(new RNAnimated.Value(0.9)).current;
  const buttonScale = useRef(new RNAnimated.Value(1)).current;

  let [fontsLoaded] = useFonts({
    Poppins_700Bold,
    Poppins_400Regular,
    Poppins_600SemiBold,
  });

  useEffect(() => {
    // Main entrance animations
    RNAnimated.timing(fadeAnim, { 
      toValue: 1, 
      duration: 1000, 
      useNativeDriver: true 
    }).start();
    
    RNAnimated.timing(slideAnim, { 
      toValue: 0, 
      duration: 800, 
      useNativeDriver: true 
    }).start();
    
    RNAnimated.spring(scaleAnim, { 
      toValue: 1, 
      damping: 12, 
      stiffness: 100, 
      useNativeDriver: true 
    }).start();
  }, []);

  const contentAnimStyle = {
    opacity: fadeAnim,
    transform: [ { translateY: slideAnim }, { scale: scaleAnim } ]
  };

  const buttonAnimStyle = { transform: [{ scale: buttonScale }] };

  const handleNext = async () => {
    if (!selectedGender) {
      Alert.alert('Selection Required', 'Please select your gender');
      return;
    }
    try {
      await saveGender(selectedGender);
      router.push('/profile/target-area');
    } catch (error) {
      Alert.alert('Error', 'Failed to save your gender.');
    }
  };

  const handleBack = () => {
    router.push('/profile/measurements');
  };

  useEffect(() => {
    if (!userEmail) return;
    (async () => {
      try {
        if (currentUser && currentUser.isNewUser) {
          setSelectedGender('');
          setLoading(false);
          return;
        }
        // Try to get from user-specific data
        const userDataKey = `@user_data:${userEmail}`;
        const userDataString = await AsyncStorage.getItem(userDataKey);
        if (userDataString) {
          const userData = JSON.parse(userDataString);
          if (userData.gender) {
            setSelectedGender(userData.gender);
            setLoading(false);
            return;
          }
        }
        // Try to get from user-specific profile
        const profileKey = `@user_profile:${userEmail}`;
        const profileData = await AsyncStorage.getItem(profileKey);
        if (profileData) {
          const profile = JSON.parse(profileData);
          if (profile.gender) {
            setSelectedGender(profile.gender);
            setLoading(false);
            return;
          }
        }
        setSelectedGender('');
        setLoading(false);
      } catch (error) {
        setSelectedGender('');
        setLoading(false);
      }
    })();
  }, [userEmail, currentUser]);

  const saveGender = async (gender) => {
    if (!userEmail) return;
    // Save to user-specific profile
    const profileKey = `@user_profile:${userEmail}`;
    const profileData = await AsyncStorage.getItem(profileKey);
    const profile = profileData ? JSON.parse(profileData) : {};
    profile.gender = gender;
    await AsyncStorage.setItem(profileKey, JSON.stringify(profile));
    // Also save to user data
    const userDataKey = `@user_data:${userEmail}`;
    const userDataString = await AsyncStorage.getItem(userDataKey);
    const userData = userDataString ? JSON.parse(userDataString) : {};
    userData.gender = gender;
    await AsyncStorage.setItem(userDataKey, JSON.stringify(userData));
    // Update user context
    await updateCurrentUser({ gender });
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
          style={StyleSheet.absoluteFill}
        />
        <ActivityIndicator size="large" color="#667eea" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      <LinearGradient
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      >
        {/* Animated Background Elements */}
        <AdvancedParticle style={{ left: wp(10), top: hp(20) }} delay={0} speed={1.2} />
        <AdvancedParticle style={{ left: wp(80), top: hp(30) }} delay={500} speed={0.8} />
        <AdvancedParticle style={{ left: wp(20), top: hp(70) }} delay={1000} speed={1.5} />
        <View style={[styles.glowCircle, { top: hp(10), right: wp(10) }]} />
        <View style={[styles.glowCircle, { bottom: hp(20), left: wp(5) }]} />
        <View style={styles.circle1} />
        <View style={styles.circle2} />
        <View style={styles.circle3} />

        {/* Back Button */}
        <TouchableOpacity onPress={handleBack} style={styles.backButton} activeOpacity={0.8}>
          <LinearGradient 
            colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']} 
            style={styles.backButtonGradient}
          >
            <Ionicons name="arrow-back" size={24} color="#ffffff" />
          </LinearGradient>
        </TouchableOpacity>

        <ScrollView style={styles.scrollView}>
          <RNAnimated.View style={[styles.content, contentAnimStyle]}>
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.title}>Select Gender</Text>
              <Text style={styles.subtitle}>Let us know your gender to personalize your experience</Text>
            </View>

          {/* Progress Indicator */}
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: '75%' }]} />
            </View>
            <Text style={styles.progressText}>Step 3 of 5</Text>
          </View>

          {/* Gender Options */}
          <View style={styles.optionsContainer}>
            {GENDER_OPTIONS.map(option => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.optionItem,
                  selectedGender === option.id && styles.optionItemSelected
                ]}
                onPress={() => setSelectedGender(option.id)}
                activeOpacity={0.8}
              >
                <View style={[
                  styles.optionIcon,
                  selectedGender === option.id && styles.optionIconSelected
                ]}>
                  <Ionicons
                    name={option.icon}
                    size={28}
                    color={selectedGender === option.id ? '#667eea' : 'rgba(255,255,255,0.8)'}
                  />
                </View>
                <Text style={[
                  styles.optionLabel,
                  selectedGender === option.id && styles.optionLabelSelected
                ]}>
                  {option.label}
                </Text>
                {selectedGender === option.id && (
                  <Ionicons name="checkmark-circle" size={24} color="#667eea" style={{ marginLeft: 10 }} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </RNAnimated.View>
        </ScrollView>

        {/* Continue Button - always visible at the bottom */}
        <RNAnimated.View style={[styles.fixedButtonContainer, buttonAnimStyle]}>
          <TouchableOpacity
            style={styles.button}
            onPress={handleNext}
            activeOpacity={0.8}
            disabled={!selectedGender}
          >
            <LinearGradient
              colors={selectedGender ? ['#667eea', '#764ba2'] : ['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.2)']}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={[styles.buttonText, !selectedGender && styles.buttonTextDisabled]}>
                Continue
              </Text>
              <Ionicons name="arrow-forward" size={20} color={selectedGender ? "#fff" : "rgba(255,255,255,0.5)"} />
            </LinearGradient>
          </TouchableOpacity>
        </RNAnimated.View>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    paddingBottom: 80, // Space for the fixed button
  },
  additionalFields: {
    marginBottom: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 20,
  },
  sectionTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 15,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 15,
    color: '#ffffff',
    fontFamily: 'Poppins_400Regular',
    fontSize: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0c0c0c',
  },
  background: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  circle1: {
    position: 'absolute',
    top: -100,
    right: -100,
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  circle2: {
    position: 'absolute',
    bottom: -50,
    left: -50,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  circle3: {
    position: 'absolute',
    top: '40%',
    right: -30,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  glowCircle: {
    position: 'absolute',
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    zIndex: 0,
  },
  backButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 30,
    left: 20,
    zIndex: 10,
  },
  backButtonGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'android' ? 40 : 20,
    paddingBottom: 30,
    marginTop: Platform.OS === 'ios' ? 60 : 40,
  },
  header: {
    marginBottom: 20,
    alignItems: 'center',
  },
  title: {
    fontFamily: 'Poppins_700Bold',
    fontSize: 28,
    color: '#ffffff',
    marginBottom: 5,
    textAlign: 'center',
  },
  subtitle: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  progressContainer: {
    marginBottom: 30,
    alignItems: 'center',
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
    width: '100%',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#667eea',
    borderRadius: 4,
  },
  progressText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  optionsContainer: {
    marginBottom: 40,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    marginBottom: 16,
  },
  optionItemSelected: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#667eea',
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionIconSelected: {
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
  },
  optionLabel: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.9)',
    flex: 1,
  },
  optionLabelSelected: {
    color: '#2c3e50',
  },
  fixedButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 30,
    alignItems: 'center',
    zIndex: 100,
  },
  button: {
    width: '90%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  buttonText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#ffffff',
    marginRight: 8,
  },
  buttonTextDisabled: {
    color: 'rgba(255, 255, 255, 0.5)',
  },
  particle: {
    position: 'absolute',
    width: 20,
    height: 20,
    zIndex: 1,
  },
  particleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  },
});