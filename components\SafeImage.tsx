<<<<<<<
import React, { useState, useCallback } from 'react';



import { View, StyleSheet, ActivityIndicator, Text } from 'react-native';



import FastImage from 'react-native-fast-image';



import { useThemeColor } from '@/hooks/useThemeColor';







interface SafeImageProps {



  source: { uri: string } | number;



  fallbackSource?: { uri: string } | number;



  style?: any;



  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';



  onLoad?: () => void;



  onError?: (error: any) => void;



  showLoadingIndicator?: boolean;



  loadingIndicatorColor?: string;



  errorText?: string;



}







const SafeImage: React.FC<SafeImageProps> = ({



  source,



  fallbackSource,



  style,



  resizeMode = 'cover',



  onLoad,



  onError,



  showLoadingIndicator = true,



  loadingIndicatorColor,



  errorText = 'Image not available',



  ...props



}) => {



  const [loading, setLoading] = useState(true);



  const [error, setError] = useState(false);



  const [usingFallback, setUsingFallback] = useState(false);







  const backgroundColor = useThemeColor({}, 'background');



  const textColor = useThemeColor({}, 'text');



  const defaultLoadingColor = useThemeColor({}, 'tint');







  const handleLoad = useCallback(() => {



    console.log('SafeImage render: source =', source, 'fallbackSource =', fallbackSource);



    setLoading(false);



    onLoad?.();



  }, [source, fallbackSource, onLoad]);







  const handleError = useCallback((err: any) => {



    console.log('SafeImage error:', err);



    setLoading(false);



    



    if (!usingFallback && fallbackSource) {



      console.log('Switching to fallback source:', fallbackSource);



      setUsingFallback(true);



      setError(false);



      setLoading(true);



    } else {



      setError(true);



      onError?.(err);



    }



  }, [usingFallback, fallbackSource, onError]);







  const getCurrentSource = () => {



    if (usingFallback && fallbackSource) {



      return fallbackSource;



    }



    return source;



  };







  const renderErrorState = () => (



    <View style={[style, styles.errorContainer, { backgroundColor }]}>



      <Text style={[styles.errorText, { color: textColor }]}>



        {errorText}



      </Text>



    </View>



  );







  const renderLoadingState = () => (



    <View style={styles.loadingOverlay}>



      <ActivityIndicator 



        size="small" 



        color={loadingIndicatorColor || defaultLoadingColor} 



      />



    </View>



  );







  if (error) {



    return renderErrorState();



  }







  return (



    <View style={[style, styles.container]}>



      <FastImage



        style={[style, styles.image]}



        source={getCurrentSource()}



        resizeMode={FastImage.resizeMode[resizeMode]}



        onLoad={handleLoad}



        onError={handleError}



        {...props}



      />



      {loading && showLoadingIndicator && renderLoadingState()}



    </View>



  );



};







const styles = StyleSheet.create({



  container: {



    position: 'relative',



    overflow: 'hidden',



  },



  image: {



    width: '100%',



    height: '100%',



  },



  errorContainer: {



    justifyContent: 'center',



    alignItems: 'center',



    padding: 10,



  },



  errorText: {



    fontSize: 12,



    textAlign: 'center',



    opacity: 0.6,



  },



  loadingOverlay: {



    position: 'absolute',



    top: 0,



    left: 0,



    right: 0,



    bottom: 0,



    justifyContent: 'center',



    alignItems: 'center',



    backgroundColor: 'rgba(0, 0, 0, 0.05)',



  },



});







export default SafeImage;



=======
import React, { useState, useCallback } from 'react';

import { View, StyleSheet, ActivityIndicator, Text } from 'react-native';

import FastImage from 'react-native-fast-image';

import { useThemeColor } from '@/hooks/useThemeColor';



interface SafeImageProps {

  source: { uri: string } | number;

  fallbackSource?: { uri: string } | number;

  style?: any;

  resizeMode?: 'cover' | 'contain' | 'stretch' | 'center';

  onLoad?: () => void;

  onError?: (error: any) => void;

  showLoadingIndicator?: boolean;

  loadingIndicatorColor?: string;

  errorText?: string;

}



const SafeImage: React.FC<SafeImageProps> = ({

  source,

  fallbackSource,

  style,

  resizeMode = 'cover',

  onLoad,

  onError,

  showLoadingIndicator = true,

  loadingIndicatorColor,

  errorText = 'Image not available',

  ...props

}) => {

  const [loading, setLoading] = useState(true);

  const [error, setError] = useState(false);

  const [usingFallback, setUsingFallback] = useState(false);



  const backgroundColor = useThemeColor({}, 'background');

  const textColor = useThemeColor({}, 'text');

  const defaultLoadingColor = useThemeColor({}, 'tint');



  const handleLoad = useCallback(() => {

    console.log('SafeImage render: source =', source, 'fallbackSource =', fallbackSource);

    setLoading(false);

    onLoad?.();

  }, [source, fallbackSource, onLoad]);



  const handleError = useCallback((err: any) => {

    console.log('SafeImage error:', err);

    setLoading(false);

    

    if (!usingFallback && fallbackSource) {

      console.log('Switching to fallback source:', fallbackSource);

      setUsingFallback(true);

      setError(false);

      setLoading(true);

    } else {

      setError(true);

      onError?.(err);

    }

  }, [usingFallback, fallbackSource, onError]);



  const getCurrentSource = () => {

    if (usingFallback && fallbackSource) {

      return fallbackSource;

    }

    return source;

  };



  const renderErrorState = () => (

    <View style={[style, styles.errorContainer, { backgroundColor }]}>

      <Text style={[styles.errorText, { color: textColor }]}>

        {errorText}

      </Text>

    </View>

  );



  const renderLoadingState = () => (

    <View style={styles.loadingOverlay}>

      <ActivityIndicator 

        size="small" 

        color={loadingIndicatorColor || defaultLoadingColor} 

      />

    </View>

  );



  if (error) {

    return renderErrorState();

  }



  return (

    <View style={[style, styles.container]}>

      <FastImage

        style={[style, styles.image]}

        source={getCurrentSource()}

        resizeMode={FastImage.resizeMode[resizeMode]}

        onLoad={handleLoad}

        onError={() => handleError('SafeImage load error')}

        {...props}

      />

      {loading && showLoadingIndicator && renderLoadingState()}

    </View>

  );

};



const styles = StyleSheet.create({

  container: {

    position: 'relative',

    overflow: 'hidden',

  },

  image: {

    width: '100%',

    height: '100%',

  },

  errorContainer: {

    justifyContent: 'center',

    alignItems: 'center',

    padding: 10,

  },

  errorText: {

    fontSize: 12,

    textAlign: 'center',

    opacity: 0.6,

  },

  loadingOverlay: {

    position: 'absolute',

    top: 0,

    left: 0,

    right: 0,

    bottom: 0,

    justifyContent: 'center',

    alignItems: 'center',

    backgroundColor: 'rgba(0, 0, 0, 0.05)',

  },

});



export default SafeImage;

>>>>>>>
