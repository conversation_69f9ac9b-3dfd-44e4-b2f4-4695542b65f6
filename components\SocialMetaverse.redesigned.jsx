import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Alert,
  Modal,
  ScrollView,
  Image,
  Dimensions,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import { useFonts, Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import SafeImage from './SafeImage';

const { width } = Dimensions.get('window');

const SocialMetaverse = ({ userEmail = '<EMAIL>', username = 'Guest User', fullScreen = false }) => {
  // Always call useFonts first to maintain hook order
  const [fontsLoaded] = useFonts({
    <PERSON><PERSON>s_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  const [liveClasses, setLiveClasses] = useState([]);
  const [nftAchievements, setNftAchievements] = useState([]);
  const [avatarModalVisible, setAvatarModalVisible] = useState(false);
  const [classModalVisible, setClassModalVisible] = useState(false);
  const [selectedClass, setSelectedClass] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userAvatar, setUserAvatar] = useState({
    level: 1,
    experience: 0,
    achievements: 0,
    workoutsCompleted: 0,
    appearance: {
      skinColor: '#FFDBAC',
      hairColor: '#8B4513',
      hairStyle: 'short',
      outfit: 'casual',
      accessories: []
    }
  });
  
  const [customizationModalVisible, setCustomizationModalVisible] = useState(false);

  useEffect(() => {
    const initializeComponent = async () => {
      try {
        setLoading(true);
        setError(null);
        
        if (userEmail) {
          await Promise.all([
            initializeMetaverse(),
            loadUserAvatar(),
            generateLiveClasses(),
            loadNFTAchievements()
          ]);
        }
      } catch (err) {
        console.error('Error initializing SocialMetaverse:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    
    initializeComponent();
  }, [userEmail]);

  const initializeMetaverse = async () => {
    // Initialize metaverse features
    console.log('Initializing Social Fitness Metaverse...');
    return Promise.resolve();
  };

  const loadUserAvatar = async () => {
    try {
      if (!userEmail) return;
      
      const avatarKey = `@avatar:${userEmail}`;
      const savedAvatar = await AsyncStorage.getItem(avatarKey);
      
      if (savedAvatar) {
        setUserAvatar(JSON.parse(savedAvatar));
      }
    } catch (error) {
      console.error('Error loading avatar:', error);
    }
  };

  const saveUserAvatar = async (avatarData) => {
    try {
      if (!userEmail) return;
      
      const avatarKey = `@avatar:${userEmail}`;
      await AsyncStorage.setItem(avatarKey, JSON.stringify(avatarData));
      setUserAvatar(avatarData);
    } catch (error) {
      console.error('Error saving avatar:', error);
    }
  };

  const generateLiveClasses = async () => {
    try {
      const classTypes = [
        'HIIT Bootcamp',
        'Yoga Flow',
        'Strength Training',
        'Dance Cardio',
        'Pilates Core',
        'Boxing Workout',
        'Meditation',
        'Stretching Session'
      ];

      const instructors = [
        'Sarah Johnson',
        'Mike Chen',
        'Emma Rodriguez',
        'David Kim',
        'Lisa Thompson',
        'Alex Martinez'
      ];

      const difficulties = ['Beginner', 'Intermediate', 'Advanced'];
      
      const classes = [];
      
      for (let i = 0; i < 8; i++) {
        const startTime = new Date();
        startTime.setHours(startTime.getHours() + Math.floor(Math.random() * 12));
        
        classes.push({
          id: i.toString(),
          title: classTypes[Math.floor(Math.random() * classTypes.length)],
          instructor: instructors[Math.floor(Math.random() * instructors.length)],
          startTime: startTime.toISOString(),
          duration: [30, 45, 60][Math.floor(Math.random() * 3)],
          difficulty: difficulties[Math.floor(Math.random() * difficulties.length)],
          participants: Math.floor(Math.random() * 50) + 10,
          maxParticipants: 100,
          isLive: Math.random() > 0.5,
          thumbnail: `https://images.unsplash.com/photo-${1571019613454 + i}?w=300&h=200&fit=crop&auto=format&q=80`,
        });
      }
      
      setLiveClasses(classes);
    } catch (error) {
      console.error('Error generating live classes:', error);
      setLiveClasses([]);
    }
  };

  const loadNFTAchievements = async () => {
    try {
      if (!userEmail) {
        console.log('No userEmail provided, skipping NFT loading');
        return;
      }
      
      const nftKey = `@nft_achievements:${userEmail}`;
      const savedNFTs = await AsyncStorage.getItem(nftKey);
      
      if (savedNFTs) {
        const parsedNFTs = JSON.parse(savedNFTs);
        setNftAchievements(Array.isArray(parsedNFTs) ? parsedNFTs : []);
      } else {
        // Generate initial NFTs
        const initialNFTs = generateInitialNFTs();
        setNftAchievements(initialNFTs);
        await AsyncStorage.setItem(nftKey, JSON.stringify(initialNFTs));
      }
    } catch (error) {
      console.error('Error loading NFT achievements:', error);
      setNftAchievements([]); // Set empty array on error
    }
  };

  const generateInitialNFTs = () => {
    return [
      {
        id: '1',
        title: 'First Steps',
        description: 'Completed your first workout',
        rarity: 'Common',
        image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=200&h=200&fit=crop&auto=format&q=80',
        earnedAt: new Date().toISOString(),
        value: '0.1 ETH',
      },
      {
        id: '2',
        title: 'Week Warrior',
        description: 'Worked out for 7 consecutive days',
        rarity: 'Rare',
        image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=200&h=200&fit=crop&auto=format&q=80',
        earnedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        value: '0.5 ETH',
      },
    ];
  };

  const joinLiveClass = (classItem) => {
    setSelectedClass(classItem);
    setClassModalVisible(true);
  };

  const confirmJoinClass = () => {
    if (!selectedClass) return;
    
    Alert.alert(
      '🎉 Joined Successfully!',
      `You've joined "${selectedClass?.title || 'Unknown Class'}" with ${selectedClass?.instructor || 'Unknown Instructor'}.\n\nClass starts at ${selectedClass?.startTime ? new Date(selectedClass.startTime).toLocaleTimeString() : 'Unknown Time'}.\n\nYou'll receive a notification 5 minutes before the class begins.`,
      [
        { text: 'Got it!', onPress: () => setClassModalVisible(false) }
      ]
    );

    // Update avatar experience
    const newAvatar = {
      ...userAvatar,
      experience: userAvatar.experience + 10,
    };
    
    if (newAvatar.experience >= newAvatar.level * 100) {
      newAvatar.level += 1;
      newAvatar.experience = 0;
      
      Alert.alert(
        '🎊 Level Up!',
        `Congratulations! Your avatar reached level ${newAvatar.level}!`,
        [{ text: 'Awesome!', style: 'default' }]
      );
    }
    
    saveUserAvatar(newAvatar);
  };

  const viewAvatarDetails = () => {
    setAvatarModalVisible(true);
  };

  const openCustomization = () => {
    setAvatarModalVisible(false);
    setCustomizationModalVisible(true);
  };

  const customizeAvatar = (category, value) => {
    const newAvatar = {
      ...userAvatar,
      appearance: {
        ...userAvatar.appearance,
        [category]: value
      }
    };
    setUserAvatar(newAvatar);
    saveUserAvatar(newAvatar);
  };

  const customizationOptions = {
    skinColor: ['#FFDBAC', '#F1C27D', '#E0AC69', '#C68642', '#8D5524', '#654321'],
    hairColor: ['#000000', '#8B4513', '#D2691E', '#FFD700', '#FF6347', '#9932CC'],
    hairStyle: ['short', 'long', 'curly', 'bald', 'ponytail', 'mohawk'],
    outfit: ['casual', 'sporty', 'formal', 'superhero', 'ninja', 'astronaut'],
    accessories: ['none', 'glasses', 'hat', 'headband', 'earrings', 'necklace']
  };

  const earnNewNFT = async (achievement) => {
    const newNFT = {
      id: Date.now().toString(),
      title: achievement.title,
      description: achievement.description,
      rarity: achievement.rarity,
      image: achievement.image,
      earnedAt: new Date().toISOString(),
      value: achievement.value,
    };

    const updatedNFTs = [...nftAchievements, newNFT];
    setNftAchievements(updatedNFTs);

    const nftKey = `@nft_achievements:${userEmail}`;
    await AsyncStorage.setItem(nftKey, JSON.stringify(updatedNFTs));

    Alert.alert(
      '🏆 New NFT Earned!',
      `You've earned "${newNFT.title}" NFT!\n\nRarity: ${newNFT.rarity}\nValue: ${newNFT.value}`,
      [{ text: 'Amazing!', style: 'default' }]
    );
  };

  const renderLiveClass = ({ item }) => {
    if (!item) return null;
    
    return (
      <TouchableOpacity
        style={styles.classCard}
        onPress={() => joinLiveClass(item)}
      >
        <LinearGradient
          colors={item?.isLive ? ['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.8)'] : ['rgba(102, 126, 234, 0.5)', 'rgba(118, 75, 162, 0.5)']}
          style={styles.classCardGradient}
        >
          <View style={styles.classHeader}>
            <Text style={styles.classTitle}>{item?.title || 'Unknown Class'}</Text>
            {item?.isLive && (
              <View style={styles.liveIndicator}>
                <View style={styles.liveDot} />
                <Text style={styles.liveText}>LIVE</Text>
              </View>
            )}
          </View>
          
          <Text style={styles.instructorName}>with {item?.instructor || 'Unknown Instructor'}</Text>
          
          <View style={styles.classDetails}>
            <Text style={styles.classDetailText}>
              ⏱️ {item?.duration || 30} min • 📊 {item?.difficulty || 'Beginner'}
            </Text>
            <Text style={styles.participantsText}>
              👥 {item?.participants || 0}/{item?.maxParticipants || 100}
            </Text>
          </View>
          
          <Text style={styles.classTime}>
            {item?.isLive ? 'Live Now' : (item?.startTime ? new Date(item.startTime).toLocaleTimeString() : 'Unknown Time')}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  const renderNFTAchievement = ({ item }) => {
    if (!item) return null;
    
    return (
      <View style={styles.nftCard}>
        <LinearGradient
          colors={
            item?.rarity === 'Legendary' ? ['rgba(255, 215, 0, 0.8)', 'rgba(255, 165, 0, 0.8)'] :
            item?.rarity === 'Epic' ? ['rgba(153, 50, 204, 0.8)', 'rgba(138, 43, 226, 0.8)'] :
            item?.rarity === 'Rare' ? ['rgba(65, 105, 225, 0.8)', 'rgba(30, 144, 255, 0.8)'] :
            ['rgba(50, 205, 50, 0.8)', 'rgba(34, 139, 34, 0.8)']
          }
          style={styles.nftCardGradient}
        >
          <SafeImage
            source={{ uri: item?.image || 'https://via.placeholder.com/100' }} 
            style={styles.nftImage} 
            onError={(error) => {
              console.error('Error loading NFT image:', item?.title, error);
            }}
          />
          <Text style={styles.nftTitle}>{item?.title || 'Unknown NFT'}</Text>
          <Text style={styles.nftRarity}>{item?.rarity || 'Common'}</Text>
          <Text style={styles.nftValue}>{item?.value || '0.1 ETH'}</Text>
        </LinearGradient>
      </View>
    );
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.container}>
        <LinearGradient 
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} 
          style={StyleSheet.absoluteFill}
        >
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#ffffff" />
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        </LinearGradient>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.sectionTitle}>🌐 Social Fitness Metaverse</Text>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.loadingText}>Loading metaverse...</Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.sectionTitle}>🌐 Social Fitness Metaverse</Text>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error: {error}</Text>
          <TouchableOpacity 
            style={styles.retryButton}
            onPress={() => {
              setError(null);
              setLoading(true);
            }}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>🌐 Social Fitness Metaverse</Text>
      
      {/* Avatar Status */}
      <TouchableOpacity style={styles.avatarSection} onPress={viewAvatarDetails}>
        <LinearGradient colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.8)']} style={styles.avatarCard}>
          <View style={styles.avatarInfo}>
            <Text style={styles.avatarTitle}>🤖 Your Avatar</Text>
            <Text style={styles.avatarLevel}>Level {userAvatar?.level || 1}</Text>
            <View style={styles.experienceBar}>
              <View 
                style={[
                  styles.experienceFill, 
                  { width: `${((userAvatar?.experience || 0) / ((userAvatar?.level || 1) * 100)) * 100}%` }
                ]} 
              />
            </View>
            <Text style={styles.experienceText}>
              {userAvatar?.experience || 0}/{(userAvatar?.level || 1) * 100} XP
            </Text>
          </View>
          <Text style={styles.avatarStatus}>Ready for training</Text>
        </LinearGradient>
      </TouchableOpacity>

      {/* Live Virtual Classes */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>🎥 Live Virtual Classes</Text>
        <FlatList
          data={liveClasses}
          renderItem={renderLiveClass}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.classesContainer}
        />
      </View>

      {/* NFT Achievements */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>🏆 NFT Achievements</Text>
        <FlatList
          data={nftAchievements}
          renderItem={renderNFTAchievement}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.nftContainer}
        />
        <TouchableOpacity style={styles.viewAllButton}>
          <Text style={styles.viewAllButtonText}>View All Achievements</Text>
        </TouchableOpacity>
      </View>

      {/* Class Details Modal */}
      <Modal
        visible={classModalVisible}
        animationType="slide"
        transparent={false}
        onRequestClose={() => setClassModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Class Details</Text>
            <TouchableOpacity onPress={() => setClassModalVisible(false)}>
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalContent}>
            {selectedClass && (
              <>
                <LinearGradient
                  colors={selectedClass?.isLive ? ['#667eea', '#764ba2'] : ['#667eea', '#764ba2']}
                  style={styles.classPreview}
                >
                  <Text style={styles.previewTitle}>{selectedClass?.title || 'Unknown Class'}</Text>
                  <Text style={styles.previewInstructor}>with {selectedClass?.instructor || 'Unknown Instructor'}</Text>
                </LinearGradient>
                
                <View style={styles.classInfo}>
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>Status</Text>
                    <Text style={styles.infoValue}>{selectedClass?.isLive ? '🔴 Live Now' : '⏰ Upcoming'}</Text>
                  </View>
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>Start Time</Text>
                    <Text style={styles.infoValue}>
                      {selectedClass?.startTime ? new Date(selectedClass.startTime).toLocaleTimeString() : 'Unknown'}
                    </Text>
                  </View>
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>Duration</Text>
                    <Text style={styles.infoValue}>{selectedClass?.duration || 30} minutes</Text>
                  </View>
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>Difficulty</Text>
                    <Text style={styles.infoValue}>{selectedClass?.difficulty || 'Beginner'}</Text>
                  </View>
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>Participants</Text>
                    <Text style={styles.infoValue}>{selectedClass?.participants || 0}/{selectedClass?.maxParticipants || 100}</Text>
                  </View>
                </View>
                
                <TouchableOpacity style={styles.joinButton} onPress={confirmJoinClass}>
                  <LinearGradient
                    colors={['#667eea', '#764ba2']}
                    style={styles.joinButtonGradient}
                  >
                    <Text style={styles.joinButtonText}>
                      {selectedClass?.isLive ? '🔴 Join Live Now' : '⏰ Join When Live'}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </>
            )}
          </ScrollView>
        </View>
      </Modal>

      {/* Avatar Details Modal */}
      <Modal
        visible={avatarModalVisible}
        animationType="slide"
        transparent={false}
        onRequestClose={() => setAvatarModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Avatar Details</Text>
            <TouchableOpacity onPress={() => setAvatarModalVisible(false)}>
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalContent}>
            <LinearGradient
              colors={['#667eea', '#764ba2']}
              style={styles.avatarPreview}
            >
              <Text style={styles.avatarPreviewTitle}>Your Fitness Avatar</Text>
              <Text style={styles.avatarPreviewSubtitle}>Level {userAvatar?.level || 1} Fitness Enthusiast</Text>
            </LinearGradient>
            
            <View style={styles.avatarStats}>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>Level</Text>
                <Text style={styles.statValue}>{userAvatar?.level || 1}</Text>
              </View>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>Experience</Text>
                <Text style={styles.statValue}>{userAvatar?.experience || 0}/{(userAvatar?.level || 1) * 100} XP</Text>
              </View>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>Achievements</Text>
                <Text style={styles.statValue}>{nftAchievements.length}</Text>
              </View>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>Workouts Completed</Text>
                <Text style={styles.statValue}>{userAvatar?.workoutsCompleted || 0}</Text>
              </View>
              <View style={styles.statRow}>
                <Text style={styles.statLabel}>Outfit</Text>
                <Text style={styles.statValue}>{userAvatar?.appearance?.outfit || 'Casual'}</Text>
              </View>
            </View>
            
            <TouchableOpacity style={styles.customizeButton} onPress={openCustomization}>
              <LinearGradient
                colors={['#667eea', '#764ba2']}
                style={styles.customizeButtonGradient}
              >
                <Text style={styles.customizeButtonText}>✨ Customize Avatar</Text>
              </LinearGradient>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </Modal>

      {/* Avatar Customization Modal */}
      <Modal
        visible={customizationModalVisible}
        animationType="slide"
        transparent={false}
        onRequestClose={() => setCustomizationModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <LinearGradient
            colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
            style={styles.customizationBackground}
          />
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Customize Avatar</Text>
            <TouchableOpacity onPress={() => setCustomizationModalVisible(false)}>
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalContent}>
            <View style={styles.avatarPreviewContainer}>
              <LinearGradient
                colors={['#667eea', '#764ba2']}
                style={styles.avatarPreviewCard}
              >
                <Text style={styles.avatarPreviewEmoji}>🤖</Text>
                <Text style={styles.avatarPreviewText}>Level {userAvatar?.level || 1} Avatar</Text>
              </LinearGradient>
            </View>
            
            {Object.keys(customizationOptions).map((category) => (
              <View key={category} style={styles.customizationSection}>
                <Text style={styles.customizationTitle}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View style={styles.optionsContainer}>
                    {customizationOptions[category].map((option) => (
                      <TouchableOpacity
                        key={option}
                        style={[
                          styles.optionButton,
                          userAvatar?.appearance?.[category] === option && styles.selectedOption
                        ]}
                        onPress={() => customizeAvatar(category, option)}
                      >
                        {category === 'skinColor' || category === 'hairColor' ? (
                          <View style={[styles.colorOption, { backgroundColor: option }]} />
                        ) : (
                          <Text style={styles.optionText}>
                            {option.charAt(0).toUpperCase() + option.slice(1)}
                          </Text>
                        )}
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>
              </View>
            ))}

            <TouchableOpacity
              style={styles.saveCustomizationButton}
              onPress={() => {
                setCustomizationModalVisible(false);
                Alert.alert('✨ Avatar Updated!', 'Your avatar customization has been saved successfully!');
              }}
            >
              <LinearGradient 
                colors={['#667eea', '#764ba2']}
                style={styles.saveButtonGradient}
              >
                <Text style={styles.saveButtonText}>💾 Save Changes</Text>
              </LinearGradient>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: hp(2),
  },
  sectionTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(2),
    paddingHorizontal: wp(1),
  },
  subsectionTitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginBottom: hp(1.5),
    paddingHorizontal: wp(1),
  },
  section: {
    marginBottom: hp(3),
  },
  avatarSection: {
    marginBottom: hp(3),
  },
  avatarCard: {
    padding: wp(4),
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  avatarInfo: {
    marginBottom: hp(1),
  },
  avatarTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(0.5),
  },
  avatarLevel: {
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginBottom: hp(1),
  },
  experienceBar: {
    height: hp(0.8),
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 4,
    marginBottom: hp(0.5),
  },
  experienceFill: {
    height: '100%',
    backgroundColor: '#fff',
    borderRadius: 4,
  },
  experienceText: {
    fontSize: wp(3),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.9,
  },
  avatarStatus: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  classesContainer: {
    paddingHorizontal: wp(1),
  },
  classCard: {
    width: wp(70),
    marginRight: wp(3),
  },
  classCardGradient: {
    padding: wp(4),
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  classHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  classTitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    flex: 1,
  },
  liveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: wp(1.5),
    paddingVertical: hp(0.3),
    borderRadius: 8,
  },
  liveDot: {
    width: wp(1.5),
    height: wp(1.5),
    borderRadius: wp(0.75),
    backgroundColor: '#ff4444',
    marginRight: wp(1),
  },
  liveText: {
    fontSize: wp(2.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  instructorName: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.9,
    marginBottom: hp(1),
  },
  classDetails: {
    marginBottom: hp(1),
  },
  classDetailText: {
    fontSize: wp(3),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.8,
    marginBottom: hp(0.3),
  },
  participantsText: {
    fontSize: wp(3),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.8,
  },
  classTime: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  nftContainer: {
    paddingHorizontal: wp(1),
  },
  nftCard: {
    width: wp(30),
    marginRight: wp(3),
  },
  nftCardGradient: {
    padding: wp(3),
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  nftImage: {
    width: wp(15),
    height: wp(15),
    borderRadius: wp(7.5),
    marginBottom: hp(1),
  },
  nftTitle: {
    fontSize: wp(3),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: hp(0.5),
  },
  nftRarity: {
    fontSize: wp(2.5),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.8,
    marginBottom: hp(0.3),
  },
  nftValue: {
    fontSize: wp(2.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  viewAllButton: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingVertical: hp(1),
    paddingHorizontal: wp(4),
    borderRadius: 8,
    alignSelf: 'center',
    marginTop: hp(1.5),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  viewAllButtonText: {
    color: '#fff',
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  modalContent: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: wp(4),
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
    color: '#333',
  },
  closeButton: {
    fontSize: wp(6),
    color: '#666',
  },
  classPreview: {
    margin: wp(4),
    padding: wp(5),
    borderRadius: 12,
    alignItems: 'center',
  },
  previewTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(0.5),
  },
  previewInstructor: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.9,
  },
  classInfo: {
    backgroundColor: '#fff',
    margin: wp(4),
    padding: wp(4),
    borderRadius: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: hp(1),
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoLabel: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#666',
  },
  infoValue: {
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: '#333',
  },
  joinButton: {
    margin: wp(4),
    borderRadius: 12,
    overflow: 'hidden',
  },
  joinButtonGradient: {
    paddingVertical: hp(2),
    alignItems: 'center',
  },
  joinButtonText: {
    color: '#fff',
    fontSize: wp(4),
    fontFamily: 'Poppins_700Bold',
  },
  avatarPreview: {
    margin: wp(4),
    padding: wp(5),
    borderRadius: 12,
    alignItems: 'center',
  },
  avatarPreviewTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(0.5),
  },
  avatarPreviewSubtitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    opacity: 0.9,
  },
  avatarStats: {
    backgroundColor: '#fff',
    margin: wp(4),
    padding: wp(4),
    borderRadius: 12,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: hp(1),
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  statLabel: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#666',
  },
  statValue: {
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: '#333',
  },
  customizeButton: {
    margin: wp(4),
    borderRadius: 12,
  },
  customizeButtonGradient: {
    paddingVertical: hp(1.5),
    borderRadius: 12,
    alignItems: 'center',
  },
  customizeButtonText: {
    color: '#fff',
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
  },
  customizationBackground: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  avatarPreviewContainer: {
    alignItems: 'center',
    marginBottom: hp(3),
  },
  avatarPreviewCard: {
    padding: wp(5),
    borderRadius: 16,
    alignItems: 'center',
  },
  avatarPreviewEmoji: {
    fontSize: wp(15),
    marginBottom: hp(1),
  },
  avatarPreviewText: {
    color: '#fff',
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
  },
  customizationSection: {
    marginBottom: hp(3),
  },
  customizationTitle: {
    color: '#fff',
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
    marginBottom: hp(1.5),
    paddingHorizontal: wp(4),
  },
  optionsContainer: {
    flexDirection: 'row',
    paddingHorizontal: wp(4),
  },
  optionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: wp(4),
    paddingVertical: hp(1.5),
    borderRadius: 12,
    marginRight: wp(3),
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: wp(20),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  selectedOption: {
    backgroundColor: 'rgba(102, 126, 234, 0.3)',
    borderWidth: 1,
    borderColor: '#667eea',
  },
  colorOption: {
    width: wp(7.5),
    height: wp(7.5),
    borderRadius: wp(3.75),
    borderWidth: 2,
    borderColor: '#fff',
  },
  optionText: {
    color: '#fff',
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    textAlign: 'center',
  },
  saveCustomizationButton: {
    margin: wp(4),
    borderRadius: 12,
  },
  saveButtonGradient: {
    paddingVertical: hp(2),
    borderRadius: 12,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(5),
  },
  loadingText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    marginTop: hp(2),
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp(5),
  },
  errorText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#ff4444',
    textAlign: 'center',
    marginBottom: hp(2),
  },
  retryButton: {
    backgroundColor: 'rgba(102, 126, 234, 0.8)',
    paddingHorizontal: wp(5),
    paddingVertical: hp(1.2),
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#667eea',
  },
  retryButtonText: {
    color: '#fff',
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
  },
});

export default SocialMetaverse;