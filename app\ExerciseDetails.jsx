// This file is used in the project. Do NOT delete.
import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Image,
  SafeAreaView,
  Alert,
  Modal,
  TextInput,
  FlatList,
  Linking,
} from 'react-native';
// import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { demoExercises } from '../constants/data';
import SafeImage from '../components/SafeImage';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from '../utils/responsive';
// import { Image as ExpoImage } from 'expo-image';
import { StatusBar } from 'expo-status-bar';
import Animated, { FadeInLeft, FadeInDown } from 'react-native-reanimated';
import { WebView } from 'react-native-webview';
import { handleVideoTutorial, isVideoTutorialEnabled, getVideoUrl } from '../constants/data';

const ExerciseDetails = () => {
    const router = useRouter();
    const params = useLocalSearchParams();
    const { id } = params;
    
    // Video player state
    const [showVideo, setShowVideo] = useState(false);
    const [videoPlayerReady, setVideoPlayerReady] = useState(false);
    
    // First try to find in demoExercises
    let item = demoExercises.find(exercise => exercise.id === id);
    
    // If not found in demoExercises, create item from passed parameters (for favorites)
    if (!item && params.name) {
        console.log('📋 Creating exercise from parameters:', params.name);
        item = {
            id: params.id || 'favorite-' + Date.now(),
            name: params.name,
            bodyPart: params.bodyPart || 'general',
            equipment: params.equipment || 'body weight',
            target: params.target || 'general',
            gifUrl: params.gifUrl || 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&crop=center&auto=format&q=80',
            videoUrl: params.videoUrl,
            instructions: params.instructions ? JSON.parse(params.instructions) : ['Follow proper form', 'Maintain steady breathing'],
            secondaryMuscles: params.secondaryMuscles ? JSON.parse(params.secondaryMuscles) : [],
            videoTutorialEnabled: params.videoTutorialEnabled === 'true',
            // Additional favorite exercise data
            duration: params.duration,
            difficulty: params.difficulty,
            calories: params.calories ? parseInt(params.calories) : 0,
            description: params.description,
            category: params.category,
            muscleGroups: params.muscleGroups ? JSON.parse(params.muscleGroups) : []
        };
        console.log('✅ Exercise created from parameters:', item.name);
    }

    const handleBack = () => {
        router.back();
    };

    // Helper function to convert YouTube URL to embed URL
    const getYouTubeEmbedUrl = (url) => {
        if (!url) return '';
        
        const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
        const match = url.match(regExp);
        
        if (match && match[2].length === 11) {
            const videoId = match[2];
            return `https://www.youtube.com/embed/${videoId}?autoplay=0&controls=1&showinfo=1&rel=0&modestbranding=1`;
        }
        
        return '';
    };

    const handleVideoTutorial = async () => {
        if (!item) {
            Alert.alert('❌ Error', 'Exercise not found.');
            return;
        }

        // Check if video tutorial is enabled for this exercise
        if (!isVideoTutorialEnabled(item)) {
            Alert.alert('❌ Video Tutorial Unavailable', 'Video tutorial is not available for this exercise.');
            return;
        }

        const videoUrl = getVideoUrl(item);
        
        if (!videoUrl) {
            Alert.alert('❌ Error', 'Video URL not available for this exercise.');
            return;
        }

        Alert.alert(
            '📹 Exercise Tutorial',
            `Watch the tutorial for "${item.name}" to learn proper form and technique.`,
            [
                { text: 'Cancel', style: 'cancel' },
                { 
                    text: 'Watch Video', 
                    onPress: async () => {
                        try {
                            console.log('🎥 Opening video tutorial:', videoUrl);
                            const supported = await Linking.canOpenURL(videoUrl);
                            
                            if (supported) {
                                await Linking.openURL(videoUrl);
                            } else {
                                Alert.alert('❌ Error', 'Cannot open video URL. Please check your internet connection.');
                            }
                        } catch (error) {
                            console.error('❌ Error opening video:', error);
                            Alert.alert('❌ Error', 'Could not open video. Please check your internet connection.');
                        }
                    }
                }
            ]
        );
    };

    const hideVideo = () => {
        setShowVideo(false);
        setVideoPlayerReady(false);
    };

    // Helper function to provide instruction tips
    const getInstructionTip = (stepIndex, exerciseName) => {
        const tips = [
            "🎯 Keep your core engaged throughout the movement",
            "🫁 Maintain steady, controlled breathing",
            "⚖️ Focus on balance and proper alignment", 
            "💪 Control the movement - don't rush",
            "🔄 Quality over quantity - perfect your form"
        ];
        
        // Exercise-specific tips
        const exerciseSpecificTips = {
            'push': ["💪 Keep your body in a straight line", "👐 Hand placement is key for proper form"],
            'squat': ["🦵 Keep knees aligned with toes", "🍑 Engage your glutes at the top"],
            'plank': ["⏱️ Hold steady - time under tension matters", "🧘‍♀️ Keep your breathing calm and controlled"],
            'lunge': ["⚖️ Balance is crucial - take your time", "🦵 Step back to starting position with control"],
            'jump': ["🦘 Land softly to protect your joints", "⚡ Use your arms for momentum and balance"]
        };
        
        // Check if exercise name contains specific keywords
        for (const [keyword, specificTips] of Object.entries(exerciseSpecificTips)) {
            if (exerciseName.toLowerCase().includes(keyword)) {
                return specificTips[stepIndex % specificTips.length];
            }
        }
        
        return tips[stepIndex % tips.length];
    };

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* Background */}
      <View style={[styles.backgroundGradient, { backgroundColor: '#1a1a2e' }]} />
      <View style={styles.backgroundCircle} />

      {!item ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Exercise not found</Text>
          <TouchableOpacity style={styles.backToExercisesButton} onPress={handleBack}>
            <Text style={styles.backToExercisesButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          {/* Exercise Image */}
          <View style={styles.imageContainer}>
            <SafeImage
              source={{ uri: item.gifUrl }}
              style={styles.exerciseImage}
            />
          </View>

          {/* Exercise Details */}
          <ScrollView 
            style={styles.content} 
            showsVerticalScrollIndicator={false} 
            contentContainerStyle={styles.scrollContent}
          >
            <Text style={styles.exerciseName}>
              {item.name}
            </Text>
            
            <View style={styles.detailsCard}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Equipment:</Text>
                <Text style={styles.detailValue}>{item?.equipment}</Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Secondary Muscles:</Text>
                <Text style={styles.detailValue}>
                  {Array.isArray(item?.secondaryMuscles) 
                    ? item.secondaryMuscles.join(', ')
                    : 'None specified'
                  }
                </Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Target:</Text>
                <Text style={styles.detailValue}>{item?.target}</Text>
              </View>
            </View>

            <View style={styles.instructionsCard}>
              <Text style={styles.instructionsTitle}>📋 Step-by-Step Instructions</Text>
              <Text style={styles.instructionsSubtitle}>💡 Follow these tips for proper form and maximum effectiveness</Text>
              
              {Array.isArray(item.instructions) ? (
                item.instructions.map((instruction, index) => (
                  <View key={index} style={styles.instructionItem}>
                    <View style={styles.stepIndicator}>
                      <Text style={styles.stepNumber}>{index + 1}</Text>
                    </View>
                    <View style={styles.instructionContent}>
                      <Text style={styles.instructionText}>{instruction}</Text>
                      <Text style={styles.instructionTip}>
                        {getInstructionTip(index, item.name)}
                      </Text>
                    </View>
                  </View>
                ))
              ) : (
                <View style={styles.instructionItem}>
                  <View style={styles.stepIndicator}>
                    <Text style={styles.stepNumber}>1</Text>
                  </View>
                  <View style={styles.instructionContent}>
                    <Text style={styles.instructionText}>Follow proper form and technique</Text>
                    <Text style={styles.instructionTip}>💪 Focus on controlled movements and proper breathing</Text>
                  </View>
                </View>
              )}
            </View>

            {/* Video Button */}
            {item.videoUrl && isVideoTutorialEnabled(item) && (
              <TouchableOpacity 
                style={styles.videoButton} 
                onPress={handleVideoTutorial}
              >
                <View style={[styles.videoButtonGradient, { backgroundColor: '#FF6B35' }]}>
                  <Ionicons
                    name="play-circle"
                    size={24}
                    color="#fff"
                  />
                  <Text style={styles.videoButtonText}>
                    Watch Tutorial Video
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          </ScrollView>
        </>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  backgroundCircle: {
    position: 'absolute',
    width: 300,
    height: 300,
    borderRadius: 150,
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    top: -100,
    right: -100,
  },
  closeWrapper: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 10,
  },
  closeButton: {
    backgroundColor: '#8E2DE2',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    marginTop: 40,
    marginHorizontal: 16,
    borderRadius: 10,
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
  imageContainerWithVideo: {
    marginTop: 6,
  },
  exerciseImage: {
    width: wp(100) - 2,
    height: wp(100) - 150,
    borderRadius: 20,
  },
  // Video Player Styles
  videoContainer: {
    marginTop: 100,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 20,
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
  videoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  videoTitle: {
    fontSize: hp(2.2),
    fontFamily: 'Poppins_700Bold',
    fontWeight: '700',
    color: '#fff',
    flex: 1,
  },
  closeVideoButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 14,
    padding: 2,
  },
  videoPlayerContainer: {
    height: hp(25),
    backgroundColor: '#000',
    position: 'relative',
  },
  webView: {
    flex: 1,
    backgroundColor: '#000',
  },
  videoLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoLoadingText: {
    color: '#fff',
    fontSize: hp(1.8),
    fontFamily: 'Poppins_400Regular',
  },
  content: {
    flex: 1,
    marginTop: 16,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 60,
  },
  exerciseName: {
    fontSize: hp(4),
    fontFamily: 'Poppins_700Bold',
    fontWeight: '700',
    color: '#fff',
    marginBottom: 16,
    textAlign: 'center',
  },
  detailsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: hp(2),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    flex: 1,
  },
  detailValue: {
    fontSize: hp(2),
    fontFamily: 'Poppins_700Bold',
    fontWeight: '700',
    color: '#fff',
    flex: 2,
    textAlign: 'right',
  },
  instructionsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  instructionsTitle: {
    fontSize: hp(3),
    fontFamily: 'Poppins_700Bold',
    fontWeight: '700',
    color: '#fff',
    marginBottom: 16,
  },
  instructionItem: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  instructionNumber: {
    fontSize: hp(1.8),
    fontFamily: 'Poppins_700Bold',
    fontWeight: '700',
    color: '#fff',
    marginRight: 8,
    minWidth: 20,
  },
  instructionText: {
    fontSize: hp(1.8),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    flex: 1,
    lineHeight: 24,
  },
  videoButton: {
    marginTop: 16,
  },
  videoButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
  },
  videoButtonText: {
    fontSize: hp(2),
    fontFamily: 'Poppins_700Bold',
    fontWeight: '700',
    color: '#fff',
    marginLeft: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: hp(3),
    fontFamily: 'Poppins_700Bold',
    fontWeight: '700',
    color: '#fff',
    marginBottom: 20,
  },
  backToExercisesButton: {
    backgroundColor: '#8E2DE2',
    padding: 16,
    borderRadius: 12,
  },
  backToExercisesButtonText: {
    fontSize: hp(2),
    fontFamily: 'Poppins_700Bold',
    fontWeight: '700',
    color: '#fff',
  },
});

export default ExerciseDetails;