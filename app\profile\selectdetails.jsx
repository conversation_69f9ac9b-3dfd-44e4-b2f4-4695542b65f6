import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Alert,
  Platform,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring, 
  withTiming,
  withSequence,
  FadeInUp,
} from 'react-native-reanimated';
import {
  useFonts,
  Poppins_700Bold,
  Poppins_400Regular,
  Poppins_600SemiBold,
} from '@expo-google-fonts/poppins';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AdvancedParticle from '../../components/AdvancedParticle';
import FloatingShape from '../../components/FloatingShape';

const { width, height } = Dimensions.get('window');

export default function SelectDetails() {
  const router = useRouter();
  const [selectedDetails, setSelectedDetails] = useState({
    fitnessLevel: '',
    targetArea: '',
    activityLevel: '',
  });

  // Animation values
  const fadeAnim = useSharedValue(0);
  const slideAnim = useSharedValue(50);
  const scaleAnim = useSharedValue(0.8);
  const buttonScale = useSharedValue(1);

  let [fontsLoaded] = useFonts({
    Poppins_700Bold,
    Poppins_400Regular,
    Poppins_600SemiBold,
  });

  useEffect(() => {
    // Start animations
    fadeAnim.value = withTiming(1, { duration: 1000 });
    slideAnim.value = withTiming(0, { duration: 800 });
    scaleAnim.value = withSpring(1, { friction: 8, tension: 40 });
  }, []);

  const animatedStyles = useAnimatedStyle(() => ({
    opacity: fadeAnim.value,
    transform: [{ translateY: slideAnim.value }]
  }));

  const scaleStyles = useAnimatedStyle(() => ({
    transform: [{ scale: scaleAnim.value }]
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScale.value }]
  }));

  const fitnessLevels = [
    { id: 'beginner', title: 'Beginner', icon: 'star-outline', description: 'New to fitness' },
    { id: 'intermediate', title: 'Intermediate', icon: 'star-half', description: 'Some experience' },
    { id: 'advanced', title: 'Advanced', icon: 'star', description: 'Experienced' },
  ];

  const targetAreas = [
    { id: 'fullbody', title: 'Full Body', icon: 'body-outline', description: 'Complete workout' },
    { id: 'upperbody', title: 'Upper Body', icon: 'fitness-outline', description: 'Arms, chest, back' },
    { id: 'lowerbody', title: 'Lower Body', icon: 'walk-outline', description: 'Legs and glutes' },
    { id: 'core', title: 'Core', icon: 'ellipse-outline', description: 'Abs and core' },
    { id: 'cardio', title: 'Cardio', icon: 'heart-outline', description: 'Cardiovascular' },
  ];

  const activityLevels = [
    { id: 'sedentary', title: 'Sedentary', icon: 'bed-outline', description: 'Little to no exercise' },
    { id: 'light', title: 'Light', icon: 'walk-outline', description: 'Light exercise 1-3 days' },
    { id: 'moderate', title: 'Moderate', icon: 'fitness-outline', description: 'Moderate exercise 3-5 days' },
    { id: 'active', title: 'Active', icon: 'flash-outline', description: 'Hard exercise 6-7 days' },
    { id: 'veryactive', title: 'Very Active', icon: 'flame-outline', description: 'Very hard exercise daily' },
  ];

  const handleSelection = (category, value) => {
    setSelectedDetails(prev => ({
      ...prev,
      [category]: value,
    }));
  };

  const isComplete = () => {
    return selectedDetails.fitnessLevel && selectedDetails.targetArea && selectedDetails.activityLevel;
  };

  const handleContinue = async () => {
    // Animate button press
    buttonScale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );

    if (isComplete()) {
      try {
        // Save the selected details to AsyncStorage
        await AsyncStorage.setItem('selectedDetails', JSON.stringify(selectedDetails));
        router.push('/profile/Module1FitnessLevel');
      } catch (error) {
        Alert.alert('Error', 'Failed to save details. Please try again.');
      }
    } else {
      Alert.alert('Incomplete Selection', 'Please select all fields before continuing');
    }
  };

  if (!fontsLoaded) return null;

  return (
    <View style={{ flex: 1 }}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      <AdvancedParticle style={{ position: 'absolute', width: 24, height: 24, left: width * 0.1, top: 120, zIndex: 0 }} delay={0} speed={1.2} />
      <AdvancedParticle style={{ position: 'absolute', width: 18, height: 18, left: width * 0.8, top: 200, zIndex: 0 }} delay={500} speed={0.8} />
      <AdvancedParticle style={{ position: 'absolute', width: 12, height: 12, left: width * 0.2, top: 500, zIndex: 0 }} delay={1000} speed={1.5} />
      <FloatingShape style={{ position: 'absolute', width: 32, height: 32, backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 16, left: width * 0.85, top: 100, zIndex: 0 }} delay={0} rotationSpeed={0.5} />
      <FloatingShape style={{ position: 'absolute', width: 20, height: 20, backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 10, left: width * 0.05, top: 600, zIndex: 0 }} delay={1000} rotationSpeed={1.2} />
      <View style={{ position: 'absolute', top: 60, right: 30, width: 50, height: 50, borderRadius: 25, backgroundColor: 'rgba(255,255,255,0.1)' }} />
      <View style={{ position: 'absolute', bottom: 80, left: 20, width: 50, height: 50, borderRadius: 25, backgroundColor: 'rgba(255,255,255,0.1)' }} />
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={styles.background}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Animated Background Elements */}
          <View style={styles.circle1} />
          <View style={styles.circle2} />
          <View style={styles.circle3} />

          {/* Back Button */}
          <Animated.View style={[styles.backWrapper, animatedStyles]}>
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <Ionicons name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>
          </Animated.View>

          {/* Main Content */}
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Header */}
            <Animated.View entering={FadeInUp.delay(200)} style={styles.header}>
              <Text style={styles.title}>Select Details</Text>
              <Text style={styles.subtitle}>Let's personalize your fitness journey</Text>
            </Animated.View>

            {/* Progress Indicator */}
            <Animated.View style={[styles.progressContainer, animatedStyles]}>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: '25%' }]} />
              </View>
              <Text style={styles.progressText}>Step 1 of 4</Text>
            </Animated.View>

            {/* Fitness Level Selection */}
            <Animated.View style={[styles.section, animatedStyles]}>
              <Text style={styles.sectionTitle}>Fitness Level</Text>
              <Text style={styles.sectionDescription}>
                Choose your current fitness level to get personalized workouts
              </Text>
              
              <View style={styles.optionsContainer}>
                {fitnessLevels.map((level, index) => (
                  <TouchableOpacity
                    key={level.id}
                    style={[
                      styles.optionItem,
                      selectedDetails.fitnessLevel === level.id && styles.optionItemSelected
                    ]}
                    onPress={() => handleSelection('fitnessLevel', level.id)}
                    activeOpacity={0.8}
                  >
                    <View style={styles.optionIcon}>
                      <Ionicons 
                        name={level.icon} 
                        size={24} 
                        color={selectedDetails.fitnessLevel === level.id ? '#667eea' : 'rgba(255,255,255,0.8)'} 
                      />
                    </View>
                    <View style={styles.optionContent}>
                      <Text style={[
                        styles.optionTitle,
                        selectedDetails.fitnessLevel === level.id && styles.optionTitleSelected
                      ]}>
                        {level.title}
                      </Text>
                      <Text style={[
                        styles.optionDescription,
                        selectedDetails.fitnessLevel === level.id && styles.optionDescriptionSelected
                      ]}>
                        {level.description}
                      </Text>
                    </View>
                    {selectedDetails.fitnessLevel === level.id && (
                      <Ionicons name="checkmark-circle" size={24} color="#667eea" />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </Animated.View>

            {/* Target Area Selection */}
            <Animated.View style={[styles.section, animatedStyles]}>
              <Text style={styles.sectionTitle}>Target Area</Text>
              <Text style={styles.sectionDescription}>
                Select the areas you want to focus on during your workouts
              </Text>
              
              <View style={styles.optionsContainer}>
                {targetAreas.map((area, index) => (
                  <TouchableOpacity
                    key={area.id}
                    style={[
                      styles.optionItem,
                      selectedDetails.targetArea === area.id && styles.optionItemSelected
                    ]}
                    onPress={() => handleSelection('targetArea', area.id)}
                    activeOpacity={0.8}
                  >
                    <View style={styles.optionIcon}>
                      <Ionicons 
                        name={area.icon} 
                        size={24} 
                        color={selectedDetails.targetArea === area.id ? '#667eea' : 'rgba(255,255,255,0.8)'} 
                      />
                    </View>
                    <View style={styles.optionContent}>
                      <Text style={[
                        styles.optionTitle,
                        selectedDetails.targetArea === area.id && styles.optionTitleSelected
                      ]}>
                        {area.title}
                      </Text>
                      <Text style={[
                        styles.optionDescription,
                        selectedDetails.targetArea === area.id && styles.optionDescriptionSelected
                      ]}>
                        {area.description}
                      </Text>
                    </View>
                    {selectedDetails.targetArea === area.id && (
                      <Ionicons name="checkmark-circle" size={24} color="#667eea" />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </Animated.View>

            {/* Activity Level Selection */}
            <Animated.View style={[styles.section, animatedStyles]}>
              <Text style={styles.sectionTitle}>Activity Level</Text>
              <Text style={styles.sectionDescription}>
                How active are you currently?
              </Text>
              
              <View style={styles.optionsContainer}>
                {activityLevels.map((level, index) => (
                  <TouchableOpacity
                    key={level.id}
                    style={[
                      styles.optionItem,
                      selectedDetails.activityLevel === level.id && styles.optionItemSelected
                    ]}
                    onPress={() => handleSelection('activityLevel', level.id)}
                    activeOpacity={0.8}
                  >
                    <View style={styles.optionIcon}>
                      <Ionicons 
                        name={level.icon} 
                        size={24} 
                        color={selectedDetails.activityLevel === level.id ? '#667eea' : 'rgba(255,255,255,0.8)'} 
                      />
                    </View>
                    <View style={styles.optionContent}>
                      <Text style={[
                        styles.optionTitle,
                        selectedDetails.activityLevel === level.id && styles.optionTitleSelected
                      ]}>
                        {level.title}
                      </Text>
                      <Text style={[
                        styles.optionDescription,
                        selectedDetails.activityLevel === level.id && styles.optionDescriptionSelected
                      ]}>
                        {level.description}
                      </Text>
                    </View>
                    {selectedDetails.activityLevel === level.id && (
                      <Ionicons name="checkmark-circle" size={24} color="#667eea" />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </Animated.View>
          </ScrollView>

          {/* Continue Button - always visible at the bottom */}
          <Animated.View entering={FadeInUp.delay(1000)} style={styles.fixedButtonContainer}>
            <TouchableOpacity
              style={styles.button}
              onPress={handleContinue}
              activeOpacity={0.8}
              disabled={!isComplete()}
            >
              <LinearGradient
                colors={isComplete() ? ['#667eea', '#764ba2'] : ['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.2)']}
                style={styles.buttonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={[styles.buttonText, !isComplete() && styles.buttonTextDisabled]}>
                  Continue
                </Text>
                <Ionicons name="arrow-forward" size={20} color={isComplete() ? "#fff" : "rgba(255,255,255,0.5)"} />
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>
        </LinearGradient>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  circle1: {
    position: 'absolute',
    top: -100,
    right: -100,
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  circle2: {
    position: 'absolute',
    bottom: -50,
    left: -50,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
  },
  circle3: {
    position: 'absolute',
    top: '40%',
    right: -30,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.06)',
  },
  backWrapper: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 70 : 50,
    left: 20,
    zIndex: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 120 : 100,
    paddingBottom: 30,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  progressContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 4,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#fff',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 15,
  },
  optionsContainer: {
    gap: 12,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  optionItemSelected: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#667eea',
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 4,
  },
  optionTitleSelected: {
    color: '#2c3e50',
  },
  optionDescription: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  optionDescriptionSelected: {
    color: '#7f8c8d',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  button: {
    width: '100%',
    padding: 16,
    borderRadius: 25,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 30,
    borderRadius: 25,
  },
  buttonText: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginRight: 10,
  },
  buttonTextDisabled: {
    color: 'rgba(255, 255, 255, 0.6)',
  },
  fixedButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 30,
    alignItems: 'center',
    zIndex: 100,
  },
}); 