// This file is used in the project. Do NOT delete.
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  SafeAreaView,
  StatusBar,
  Animated as RNAnimated,
  Dimensions,
  ActivityIndicator,
  Platform,
  TextInput,
  Modal,
  Image
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp } from 'react-native-reanimated';
// import { Poppins_400Regular, Poppins_700Bold, Poppins_600SemiBold, useFonts } from '@expo-google-fonts/poppins';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useUser } from '../context/UserContext';
import SafeImage from '../components/SafeImage';
import { getImageByCategory } from '../constants/remoteImages';

const { width, height } = Dimensions.get('window');

const AdvancedParticle = ({ style }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const speed = 4000 + Math.random() * 2000;
    const delay = Math.random() * 3000;

    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(particleAnim, { toValue: 1, duration: speed, useNativeDriver: true }),
        RNAnimated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = RNAnimated.sequence([
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: speed - 1000, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);

    const timeoutId = setTimeout(() => {
      animation.start();
      RNAnimated.loop(opacitySequence).start();
    }, delay);
    
    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, []);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <RNAnimated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

// Helper to get remote image source by key
const getLocalImage = (imageKey) => {
  try {
    const url = getImageByCategory(imageKey);
    return { uri: url || getImageByCategory('default') };
  } catch (error) {
    console.log('Image loading error:', error);
    return { uri: getImageByCategory('default') };
  }
};

export default function PlansPage() {
  // Always call useFonts first to maintain hook order
  // const [fontsLoaded] = useFonts({
  //   Poppins_400Regular,
  //   Poppins_700Bold,
  //   Poppins_600SemiBold,
  // });
  const fontsLoaded = true;

  const router = useRouter();
  const { currentUser, loading: userLoading } = useUser();
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [completedDays, setCompletedDays] = useState({});
  const [notes, setNotes] = useState({});
  const [calories, setCalories] = useState({});
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [selectedDay, setSelectedDay] = useState(null);
  const [menuVisible, setMenuVisible] = useState(false);
  const [nutritionData, setNutritionData] = useState({});

  // Helper functions - define these early to avoid scope issues
  const isDayCompleted = (planId, day) => {
    return (completedDays[planId] || []).includes(day);
  };
  
  const isNextDay = (planId, day) => {
    const currentCompletedDays = completedDays[planId] || [];
    return day === currentCompletedDays.length + 1;
  };

  useEffect(() => {
    fetchDietPlans();
    loadUserProgress();
  }, []);

  const handleBack = () => {
    router.back();
  };
  
  // Load user progress from AsyncStorage
  const loadUserProgress = async () => {
    try {
      if (!currentUser) return;
      
      const userProgressKey = `@user_progress_${currentUser.uid}`;
      const storedProgress = await AsyncStorage.getItem(userProgressKey);
      
      if (storedProgress) {
        const parsedProgress = JSON.parse(storedProgress);
        setCompletedDays(parsedProgress.completedDays || {});
        setNotes(parsedProgress.notes || {});
        setCalories(parsedProgress.calories || {});
        setNutritionData(parsedProgress.nutritionData || {});
      }
    } catch (error) {
      console.error('Error loading user progress:', error);
    }
  };

  // Save user progress to AsyncStorage
  const saveUserProgress = async () => {
    try {
      if (!currentUser) return;
      
      const userProgressKey = `@user_progress_${currentUser.uid}`;
      const progressData = {
        completedDays,
        notes,
        calories,
        nutritionData
      };
      await AsyncStorage.setItem(userProgressKey, JSON.stringify(progressData));
    } catch (error) {
      console.error('Error saving user progress:', error);
    }
  };

  // Get recommended nutrition values based on plan type
  const getRecommendedValues = (planType) => {
    switch (planType) {
      case 'keto':
        return { protein: 120, carbs: 20, fat: 150 };
      case 'vegan':
        return { protein: 80, carbs: 300, fat: 60 };
      case 'paleo':
        return { protein: 150, carbs: 50, fat: 100 };
      case 'mediterranean':
        return { protein: 100, carbs: 200, fat: 70 };
      case 'fasting':
        return { protein: 100, carbs: 150, fat: 80 };
      case 'lowcarb':
        return { protein: 130, carbs: 50, fat: 120 };
      default:
        return { protein: 100, carbs: 200, fat: 70 };
    }
  };
  
  // Calculate progress percentage for a plan
  const getProgressPercentage = (planId, totalDays) => {
    if (!completedDays[planId] || !totalDays) return 0;
    const completed = completedDays[planId].length;
    return Math.min(Math.round((completed / totalDays) * 100), 100);
  };
  
  // This function is implemented below with async functionality
  
  // Get icon for plan type
  const getPlanIcon = (type) => {
    switch (type) {
      case 'fasting':
        return '⏱️';
      case 'keto':
        return '🥑';
      case 'vegan':
        return '🥦';
      case 'paleo':
        return '🍖';
      case 'mediterranean':
        return '🫒';
      case 'lowcarb':
        return '🥩';
      default:
        return '🍽️';
    }
  };
  
  // Using the getLocalImage function defined above
  
  // Handle creating a custom plan
  const handleCreateCustomPlan = () => {
    if (!currentUser) {
      Alert.alert('Error', 'Please login to create a custom plan');
      router.replace('/user/login');
      return;
    }
    
    // Navigate to the add-plan page
    router.push('/admin/add-plan');
  };

  const fetchDietPlans = async () => {
    try {
      setLoading(true);
      const stored = await AsyncStorage.getItem('@diet_plans');
      if (stored) {
        const parsedPlans = JSON.parse(stored);
        setPlans(parsedPlans);
      } else {
        // Create default plans
        const defaultPlans = [
          {
            id: '1',
            title: 'Intermittent Fasting',
            description: 'A popular eating pattern that cycles between periods of fasting and eating.',
            days: 30,
            imageKey: 'fasting.png',
            type: 'fasting',
            difficulty: 'Beginner',
            duration: '30 days'
          },
          {
            id: '2',
            title: 'Keto Diet',
            description: 'A low-carb, high-fat diet that can help you burn fat more effectively.',
            days: 21,
            imageKey: 'keto.png',
            type: 'keto',
            difficulty: 'Intermediate',
            duration: '21 days'
          },
          {
            id: '3',
            title: 'Low Carb Diet',
            description: 'Reduce carbohydrate intake while maintaining balanced nutrition.',
            days: 28,
            imageKey: 'lowcarb.png',
            type: 'lowcarb',
            difficulty: 'Beginner',
            duration: '28 days'
          },
          {
            id: '4',
            title: 'Vegan Diet',
            description: 'Plant-based nutrition focused on whole foods and sustainability.',
            days: 30,
            imageKey: 'vegan.png',
            type: 'vegan',
            difficulty: 'Beginner',
            duration: '30 days'
          }
        ];
        await AsyncStorage.setItem('@diet_plans', JSON.stringify(defaultPlans));
        setPlans(defaultPlans);
      }
    } catch (error) {
      console.error('Error fetching diet plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartPlan = async (plan) => {
    try {
      if (!currentUser) {
        Alert.alert('Error', 'Please login to start a plan');
        router.replace('/user/login');
        return;
      }

      // Check if plan is already in progress
      const currentProgress = completedDays[plan.id] || [];
      const isInProgress = currentProgress.length > 0;
      
      if (isInProgress) {
        // Plan already started, just select it
        setSelectedPlan(plan);
        const nextDay = currentProgress.length + 1;
        if (nextDay <= plan.days) {
          setSelectedDay(nextDay);
        }
        return;
      }
      
      // Initialize new plan
      Alert.alert(
        'Start New Plan',
        `Are you ready to start the ${plan.title} plan? This is a ${plan.days}-day commitment.`,
        [
          { 
            text: 'Cancel', 
            style: 'cancel' 
          },
          {
            text: 'Start Plan',
            onPress: async () => {
              setSelectedPlan(plan);
              setSelectedDay(1); // Set to day 1
              
              // Record plan start in user data
              try {
                const userPlansKey = `@user_plans_${currentUser.uid}`;
                const existingPlans = await AsyncStorage.getItem(userPlansKey);
                let userPlans = existingPlans ? JSON.parse(existingPlans) : [];
                
                userPlans.push({
                  planId: plan.id,
                  planTitle: plan.title,
                  startedAt: new Date().toISOString(),
                  totalDays: plan.days,
                });
                
                await AsyncStorage.setItem(userPlansKey, JSON.stringify(userPlans));
                
                Alert.alert('Success', `${plan.title} plan started! You're now on Day 1.`);
              } catch (error) {
                console.error('Error recording plan start:', error);
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error starting plan:', error);
      Alert.alert('Error', 'Failed to start plan');
    }
  };

  // Using the getPlanIcon function defined above
  
  const handleMarkDone = async (planId, day) => {
    // Check if this is the next day in sequence
    const currentCompletedDays = completedDays[planId] || [];
    const nextDayToComplete = currentCompletedDays.length + 1;
    
    if (day !== nextDayToComplete) {
      Alert.alert(
        'Cannot Skip Days',
        `You need to complete Day ${nextDayToComplete} first before marking Day ${day} as complete.`,
        [{ text: 'OK', style: 'default' }]
      );
      return;
    }
    
    // Check if user already completed a day today
    const lastCompletionKey = `@last_completion_${currentUser?.uid}`;
    try {
      const lastCompletionData = await AsyncStorage.getItem(lastCompletionKey);
      if (lastCompletionData) {
        const { date, planId: lastPlanId } = JSON.parse(lastCompletionData);
        const lastDate = new Date(date);
        const today = new Date();
        
        // Check if the last completion was today
        if (lastDate.toDateString() === today.toDateString()) {
          Alert.alert(
            'Daily Limit Reached',
            'You can only complete one day per 24-hour period. Come back tomorrow to continue your progress!',
            [{ text: 'OK', style: 'default' }]
          );
          return;
        }
      }
    } catch (error) {
      console.error('Error checking last completion:', error);
    }
    
    // Update state with the newly completed day
    const updatedCompletedDays = {
      ...completedDays,
      [planId]: [...currentCompletedDays, day],
    };
    
    setCompletedDays(updatedCompletedDays);
    
    // Save progress to AsyncStorage
    try {
      if (currentUser) {
        // Save last completion date
        const lastCompletionData = {
          date: new Date().toISOString(),
          planId,
          day
        };
        await AsyncStorage.setItem(lastCompletionKey, JSON.stringify(lastCompletionData));
        
        // Save user progress
        const userProgressKey = `@user_progress_${currentUser.uid}`;
        const progressData = {
          completedDays: updatedCompletedDays,
          notes,
          calories,
          lastUpdated: new Date().toISOString(),
        };
        
        await AsyncStorage.setItem(userProgressKey, JSON.stringify(progressData));
        
        // Also save to plan-performance data for admin analytics
        const performanceKey = '@plan_performance';
        const existingPerformance = await AsyncStorage.getItem(performanceKey);
        let performanceData = existingPerformance ? JSON.parse(existingPerformance) : [];
        
        performanceData.push({
          userId: currentUser.uid,
          planId,
          day,
          completedAt: new Date().toISOString(),
          notes: notes[`${planId}_${day}`] || '',
          calories: calories[`${planId}_${day}`] || '',
        });
        
        await AsyncStorage.setItem(performanceKey, JSON.stringify(performanceData));
      }
    } catch (error) {
      console.error('Error saving progress:', error);
    }
    
    // Show success animation and message
    Alert.alert(
      '✅ Day Completed!',
      `Great job completing Day ${day}! Keep up the good work! Come back tomorrow to continue.`,
      [{ text: 'Continue', style: 'default' }]
    );
  };

  if (!fontsLoaded) {
    return null;
  }

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#0c0c0c' }}>
        <View style={[StyleSheet.absoluteFill, { backgroundColor: '#1a1a2e' }]} />
        <ActivityIndicator size="large" color="#fff" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={[StyleSheet.absoluteFill, { backgroundColor: '#1a1a2e' }]} />
      {[...Array(20)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Fitness Plans</Text>
          <TouchableOpacity 
            style={styles.menuButton}
            onPress={() => setMenuVisible(true)}
          >
            <Ionicons name="ellipsis-vertical" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
        
        {/* Menu Modal */}
        <Modal
          visible={menuVisible}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setMenuVisible(false)}
        >
          <TouchableOpacity 
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setMenuVisible(false)}
          >
            <View style={styles.menuModal}>
              <View style={styles.menuHeader}>
                <Text style={styles.menuTitle}>Menu</Text>
                <TouchableOpacity onPress={() => setMenuVisible(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              <ScrollView style={styles.menuScrollView} showsVerticalScrollIndicator={false}>
              <TouchableOpacity 
                style={styles.menuItem}
                onPress={() => {
                  setMenuVisible(false);
                  router.push('/ai-workout-generator');
                }}
              >
                                  <View style={styles.menuItemContent}>
                  <Ionicons name="fitness" size={24} color="#fff" />
                <Text style={styles.menuItemText}>AI Workout Generator</Text>
                </View>
                <Ionicons name="chevron-forward" size={18} color="rgba(255,255,255,0.5)" />
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.menuItem}
                onPress={() => {
                  setMenuVisible(false);
                  router.push('/nutrition-tracker');
                }}
              >
                                  <View style={styles.menuItemContent}>
                  <Ionicons name="nutrition" size={24} color="#fff" />
                <Text style={styles.menuItemText}>Nutrition Tracker</Text>
                </View>
                <Ionicons name="chevron-forward" size={18} color="rgba(255,255,255,0.5)" />
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.menuItem}
                onPress={() => {
                  setMenuVisible(false);
                  try {
                    router.push('/admin/plan-performance');
                  } catch (error) {
                    console.error('Navigation error:', error);
                    Alert.alert('Error', 'Unable to open Performance Analytics. Please try again.');
                  }
                }}
              >
                                  <View style={styles.menuItemContent}>
                  <Ionicons name="analytics" size={24} color="#fff" />
                <Text style={styles.menuItemText}>Performance Analytics</Text>
                </View>
                <Ionicons name="chevron-forward" size={18} color="rgba(255,255,255,0.5)" />
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.menuItem}
                onPress={() => {
                  setMenuVisible(false);
                  router.push('/ai-coach');
                }}
              >
                                  <View style={styles.menuItemContent}>
                  <Ionicons name="chatbubbles" size={24} color="#fff" />
                <Text style={styles.menuItemText}>AI Coach Chat</Text>
                </View>
                <Ionicons name="chevron-forward" size={18} color="rgba(255,255,255,0.5)" />
              </TouchableOpacity>
              </ScrollView>
            </View>
          </TouchableOpacity>
        </Modal>

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
        >
          {/* Welcome Section */}
          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeTitle}>Your Fitness Journey</Text>
            <Text style={styles.welcomeSubtitle}>Choose a plan that fits your goals and lifestyle</Text>
          </View>

          {/* Quick Stats */}
          <View style={styles.statsSection}>
            <View style={styles.statCard}>
              <Ionicons name="calendar-outline" size={24} color="#667eea" />
              <Text style={styles.statValue}>{plans.length}</Text>
              <Text style={styles.statLabel}>Available Plans</Text>
            </View>
            <View style={styles.statCard}>
              <Ionicons name="checkmark-circle-outline" size={24} color="#4CAF50" />
              <Text style={styles.statValue}>{Object.keys(completedDays).length}</Text>
              <Text style={styles.statLabel}>Active Plans</Text>
            </View>
            <View style={styles.statCard}>
              <Ionicons name="trophy-outline" size={24} color="#FF9800" />
              <Text style={styles.statValue}>{Object.values(completedDays).flat().length}</Text>
              <Text style={styles.statLabel}>Days Completed</Text>
            </View>
          </View>

          {/* Create Custom Plan Button */}
          <TouchableOpacity style={styles.createPlanButton} onPress={handleCreateCustomPlan}>
            <View style={[styles.createPlanGradient, { backgroundColor: '#667eea' }]}>
              <Ionicons name="add-circle-outline" size={24} color="#fff" />
              <Text style={styles.createPlanText}>Create Custom Plan</Text>
            </View>
          </TouchableOpacity>

          {/* Plans Grid */}
          <View style={styles.plansSection}>
            <Text style={styles.sectionTitle}>Available Plans</Text>
            <View style={styles.plansGrid}>
              {plans.map((plan) => {
                const progress = getProgressPercentage(plan.id, plan.days);
                const isActive = completedDays[plan.id] && completedDays[plan.id].length > 0;
                
                return (
                  <TouchableOpacity
                    key={plan.id}
                    style={[styles.planCard, isActive && styles.planCardActive]}
                    onPress={() => handleStartPlan(plan)}
                  >
                    <View style={[styles.planCardGradient, { backgroundColor: 'rgba(255,255,255,0.1)' }]}>
                      {/* Plan Image */}
                      <View style={styles.planImageContainer}>
                        <SafeImage 
                          source={getLocalImage(plan.imageKey || 'diet.png')}
                          style={styles.planImage}
                          resizeMode="cover"
                        />
                        <View style={styles.planImageOverlay} />
                      </View>
                      
                      <View style={styles.planCardHeader}>
                        <View style={styles.planIconContainer}>
                          <Text style={styles.planIcon}>{getPlanIcon(plan.type)}</Text>
                        </View>
                        <View style={styles.planInfo}>
                          <Text style={styles.planTitle}>{plan.title}</Text>
                          <Text style={styles.planDuration}>{plan.duration}</Text>
                        </View>
                        {isActive && (
                          <View style={styles.activeBadge}>
                            <Text style={styles.activeBadgeText}>Active</Text>
                          </View>
                        )}
                      </View>
                    
                      <Text style={styles.planDescription}>{plan.description}</Text>
                      
                      <View style={styles.planStats}>
                        <View style={styles.planStat}>
                          <Ionicons name="time-outline" size={16} color="#aaa" />
                          <Text style={styles.planStatText}>{plan.days} days</Text>
                        </View>
                        <View style={styles.planStat}>
                          <Ionicons name="trending-up-outline" size={16} color="#aaa" />
                          <Text style={styles.planStatText}>{plan.difficulty}</Text>
                        </View>
                      </View>
                      
                      {isActive && (
              <View style={styles.progressContainer}>
                <View style={styles.progressHeader}>
                            <Text style={styles.progressText}>Progress</Text>
                            <Text style={styles.progressPercentage}>{progress}%</Text>
                          </View>
                          <View style={styles.progressBar}>
                            <View 
                              style={[styles.progressFill, { width: `${progress}%` }]} 
                            />
                          </View>
                        </View>
                      )}
                      
                      <TouchableOpacity
                        style={[styles.planActionButton, isActive && styles.planActionButtonActive]}
                        onPress={() => handleStartPlan(plan)}
                      >
                        <Text style={[styles.planActionText, isActive && styles.planActionTextActive]}>
                          {isActive ? 'Continue' : 'Start Plan'}
                    </Text>
                      </TouchableOpacity>
                    </View>
                  </TouchableOpacity>
                );
              })}
                  </View>
                </View>
                
          {/* Selected Plan Section */}
          {selectedPlan && (
            <View style={styles.selectedPlanSection}>
              <View style={styles.selectedPlanHeader}>
                <Text style={styles.selectedPlanTitle}>{selectedPlan.title} Plan</Text>
                <TouchableOpacity onPress={() => setSelectedPlan(null)}>
                  <Ionicons name="close-circle-outline" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.progressOverview}>
                <View style={styles.progressCard}>
                  <Text style={styles.progressCardTitle}>Progress</Text>
                  <Text style={styles.progressCardValue}>
                    {getProgressPercentage(selectedPlan.id, selectedPlan.days)}%
                  </Text>
                  <View style={styles.progressBarLarge}>
                    <View 
                      style={[
                        styles.progressFillLarge, 
                        { width: `${getProgressPercentage(selectedPlan.id, selectedPlan.days)}%` }
                      ]} 
                    />
                  </View>
                </View>
                
                <View style={styles.progressCard}>
                  <Text style={styles.progressCardTitle}>Days Completed</Text>
                  <Text style={styles.progressCardValue}>
                    {(completedDays[selectedPlan.id] || []).length} / {selectedPlan.days}
                  </Text>
                  </View>
                </View>
                
                {/* Days Grid */}
                <View style={styles.daysGrid}>
                  {[...Array(selectedPlan.days)].map((_, index) => {
                    const day = index + 1;
                    const isCompleted = isDayCompleted(selectedPlan.id, day);
                  const isNextDayValue = isNextDay(selectedPlan.id, day);
                  const isAvailable = isCompleted || isNextDayValue;
                    
                    return (
                      <TouchableOpacity
                        key={`day-${day}`}
                        style={[
                          styles.dayBox,
                          isCompleted && styles.dayBoxCompleted,
                          !isAvailable && styles.dayBoxLocked,
                          selectedDay === day && styles.dayBoxSelected,
                        isNextDayValue && styles.dayBoxNext
                        ]}
                        onPress={() => {
                          if (isAvailable || isCompleted) {
                            setSelectedDay(day === selectedDay ? null : day);
                          } else {
                            Alert.alert(
                              'Day Locked',
                              `You need to complete previous days first before accessing Day ${day}.`,
                              [{ text: 'OK', style: 'default' }]
                            );
                          }
                        }}
                      >
                        <Text style={[
                          styles.dayBoxText,
                          isCompleted && styles.dayBoxTextCompleted,
                          !isAvailable && styles.dayBoxTextLocked,
                          selectedDay === day && styles.dayBoxTextSelected,
                        isNextDayValue && styles.dayBoxTextNext
                        ]}>
                          {day}
                        </Text>
                        {isCompleted && (
                          <View style={styles.dayBoxIcon}>
                            <Ionicons name="checkmark-circle" size={14} color="#fff" />
                          </View>
                        )}
                                              {isNextDayValue && (
                          <View style={styles.dayBoxIconNext}>
                            <Ionicons name="arrow-forward-circle" size={14} color="#fff" />
                          </View>
                        )}
                      </TouchableOpacity>
                    );
                  })}
              </View>
              
              {/* Day Details */}
              {selectedDay && (
                <View style={styles.dayDetails}>
                  <View style={styles.dayDetailsHeader}>
                    <Text style={styles.dayDetailsTitle}>Day {selectedDay}</Text>
                    <TouchableOpacity onPress={() => setSelectedDay(null)}>
                      <Ionicons name="close-circle-outline" size={24} color="#fff" />
                    </TouchableOpacity>
                  </View>
                  
                  <Text style={styles.sectionSubtitle}>Daily Nutrition Tracking</Text>
                  
                  <View style={styles.nutritionGrid}>
                    <View style={styles.nutritionItem}>
                      <TextInput
                        style={styles.nutritionInput}
                        placeholder="0"
                        placeholderTextColor="rgba(255,255,255,0.6)"
                        keyboardType="numeric"
                        value={calories[`${selectedPlan.id}_${selectedDay}`] || ''}
                        onChangeText={(text) => {
                          const newCalories = { ...calories, [`${selectedPlan.id}_${selectedDay}`]: text };
                          setCalories(newCalories);
                          saveUserProgress();
                        }}
                      />
                      <Text style={styles.nutritionLabel}>Calories</Text>
                    </View>
                    
                    <View style={styles.nutritionItem}>
                      <TextInput
                        style={styles.nutritionInput}
                        placeholder="0"
                        placeholderTextColor="rgba(255,255,255,0.6)"
                        keyboardType="numeric"
                        value={nutritionData[`${selectedPlan.id}_${selectedDay}_protein`] || ''}
                        onChangeText={(text) => {
                          const newNutritionData = {...nutritionData, [`${selectedPlan.id}_${selectedDay}_protein`]: text};
                          setNutritionData(newNutritionData);
                          saveUserProgress();
                        }}
                      />
                      <Text style={styles.nutritionLabel}>Protein (g)</Text>
                    </View>
                    
                    <View style={styles.nutritionItem}>
                      <TextInput
                        style={styles.nutritionInput}
                        placeholder="0"
                        placeholderTextColor="rgba(255,255,255,0.6)"
                        keyboardType="numeric"
                        value={nutritionData[`${selectedPlan.id}_${selectedDay}_carbs`] || ''}
                        onChangeText={(text) => {
                          const newNutritionData = {...nutritionData, [`${selectedPlan.id}_${selectedDay}_carbs`]: text};
                          setNutritionData(newNutritionData);
                          saveUserProgress();
                        }}
                      />
                      <Text style={styles.nutritionLabel}>Carbs (g)</Text>
                    </View>
                    
                    <View style={styles.nutritionItem}>
                      <TextInput
                        style={styles.nutritionInput}
                        placeholder="0"
                        placeholderTextColor="rgba(255,255,255,0.6)"
                        keyboardType="numeric"
                        value={nutritionData[`${selectedPlan.id}_${selectedDay}_fat`] || ''}
                        onChangeText={(text) => {
                          const newNutritionData = {...nutritionData, [`${selectedPlan.id}_${selectedDay}_fat`]: text};
                          setNutritionData(newNutritionData);
                          saveUserProgress();
                        }}
                      />
                      <Text style={styles.nutritionLabel}>Fat (g)</Text>
                    </View>
                  </View>
                  
                  <View style={styles.notesSection}>
                    <Text style={styles.notesLabel}>Daily Notes</Text>
                  <TextInput
                      style={styles.notesInput}
                      placeholder="How did your day go? Any challenges or achievements?"
                    placeholderTextColor="rgba(255,255,255,0.6)"
                    multiline
                      numberOfLines={4}
                    value={notes[`${selectedPlan.id}_${selectedDay}`] || ''}
                    onChangeText={(text) => {
                      const newNotes = { ...notes, [`${selectedPlan.id}_${selectedDay}`]: text };
                      setNotes(newNotes);
                      saveUserProgress();
                    }}
                  />
                  </View>
                  
                    <TouchableOpacity
                    style={styles.markDoneButton}
                      onPress={() => handleMarkDone(selectedPlan.id, selectedDay)}
                  >
                    <View style={[styles.markDoneGradient, { backgroundColor: '#4CAF50' }]}>
                      <Ionicons name="checkmark-circle-outline" size={20} color="#fff" />
                      <Text style={styles.markDoneText}>Mark Day Complete</Text>
                    </View>
                      </TouchableOpacity>
                    </View>
              )}
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 5,
    zIndex: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight + 10 : 40,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 22,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  menuButton: {
    padding: 8,
  },
  scrollContainer: {
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: height * 0.2,
  },
  emptyTitle: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: 'rgba(255,255,255,0.7)',
    marginTop: 20,
  },
  emptySubtitle: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.5)',
    marginTop: 10,
    textAlign: 'center',
  },
  plansGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginHorizontal: -5,
  },
  planCardContainer: {
    width: '100%',
    paddingHorizontal: 5,
    marginBottom: 20,
  },
  planCard: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  planCardGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 16,
  },
  planImageContainer: {
    position: 'relative',
    height: 120,
    borderRadius: 12,
    marginBottom: 15,
    overflow: 'hidden',
  },
  planImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  planImageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  iconContainer: {
    position: 'absolute',
    top: 15,
    right: 15,
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  planIcon: {
    fontSize: 24,
  },
  planContent: {
    padding: 16,
  },
  planTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 8,
  },
  planDescription: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 10,
    lineHeight: 20,
  },
  progressContainer: {
    marginTop: 10,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255,255,255,0.7)',
  },
  progressPercentage: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: '#4CAF50',
  },
  progressBar: {
    height: 6,
    backgroundColor: 'rgba(255,255,255,0.15)',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 3,
  },
  selectedPlanSection: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  selectedPlanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  selectedPlanTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  notesInput: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    padding: 15,
    color: '#fff',
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
    textAlignVertical: 'top',
    minHeight: 80,
  },
  markDoneButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  markDoneGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  markDoneText: {
    color: '#fff',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 14,
    marginLeft: 8,
  },
  welcomeSection: {
    marginTop: 20,
    marginBottom: 15,
    paddingHorizontal: 20,
  },
  welcomeTitle: {
    fontSize: 28,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 10,
  },
  welcomeSubtitle: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.7)',
  },
  statsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  statCard: {
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginTop: 10,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
  },
  plansSection: {
    marginBottom: 20,
  },
  planCardActive: {
    borderColor: 'rgba(102, 126, 234, 0.5)',
    borderWidth: 2,
  },
  planCard: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
    position: 'relative',
    overflow: 'hidden',
  },
  planCardGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 16,
  },
  planImageContainer: {
    position: 'relative',
    height: 120,
    borderRadius: 12,
    marginBottom: 15,
    overflow: 'hidden',
  },
  planImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  planImageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  planCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  planIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  planInfo: {
    flex: 1,
  },
  planTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 4,
  },
  planDuration: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
  },
  activeBadge: {
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(102, 126, 234, 0.5)',
  },
  activeBadgeText: {
    color: '#667eea',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 12,
  },
  planStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  planStat: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 20,
  },
  planStatText: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
    marginLeft: 6,
  },
  progressOverview: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
  },
  progressCard: {
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  progressCardTitle: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255,255,255,0.7)',
    marginBottom: 5,
  },
  progressCardValue: {
    fontSize: 28,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 10,
  },
  progressBarLarge: {
    height: 10,
    backgroundColor: 'rgba(255,255,255,0.15)',
    borderRadius: 5,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  progressFillLarge: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 5,
  },
  planActionButton: {
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(102, 126, 234, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    minHeight: 44,
    zIndex: 10,
  },
  planActionButtonActive: {
    backgroundColor: 'rgba(102, 126, 234, 0.8)',
    borderColor: 'rgba(102, 126, 234, 1)',
  },
  planActionText: {
    color: '#667eea',
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    textAlign: 'center',
  },
  planActionTextActive: {
    color: '#fff',
  },
  notesSection: {
    marginTop: 15,
    marginBottom: 10,
  },
  notesLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255,255,255,0.7)',
    marginBottom: 5,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  nutritionItem: {
    width: '48%',
    marginBottom: 8,
  },
  nutritionInput: {
    borderColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 8,
    color: '#fff',
    fontFamily: 'Poppins_400Regular',
    backgroundColor: 'rgba(255,255,255,0.05)',
    textAlign: 'center',
    fontSize: 14,
  },
  nutritionLabel: {
    color: 'rgba(255,255,255,0.6)',
    fontFamily: 'Poppins_400Regular',
    fontSize: 11,
    textAlign: 'center',
    marginTop: 4,
  },
  sectionSubtitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginTop: 15,
    marginBottom: 10,
  },
  dayDetails: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 16,
    padding: 12,
    marginTop: 10,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
    maxHeight: 400,
  },
  dayDetailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  dayDetailsTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  input: {
    borderColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 10,
    color: '#fff',
    marginBottom: 10,
    fontFamily: 'Poppins_400Regular',
    backgroundColor: 'rgba(255,255,255,0.05)',
  },
  notesInput: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    padding: 15,
    color: '#fff',
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
    textAlignVertical: 'top',
    minHeight: 80,
  },
  markDayButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    marginTop: 10,
  },
  markDayButtonText: {
    color: '#fff',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 14,
    marginLeft: 8,
  },
  createPlanButton: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
  },
  createPlanGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  createPlanText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    marginLeft: 10,
  },
  plansGrid: {
    paddingHorizontal: 20,
  },
  planCard: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
    position: 'relative',
    overflow: 'hidden',
  },
  planCardGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 16,
  },
  planImageContainer: {
    position: 'relative',
    height: 120,
    borderRadius: 12,
    marginBottom: 15,
    overflow: 'hidden',
  },
  planImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  planImageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  planCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  planIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  planInfo: {
    flex: 1,
  },
  planTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 4,
  },
  planDuration: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
  },
  activeBadge: {
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(102, 126, 234, 0.5)',
  },
  activeBadgeText: {
    color: '#667eea',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 12,
  },
  planStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  planStat: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 20,
  },
  planStatText: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
    marginLeft: 6,
  },
  progressOverview: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
  },
  progressCard: {
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  progressCardTitle: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255,255,255,0.7)',
    marginBottom: 5,
  },
  progressCardValue: {
    fontSize: 28,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 10,
  },
  progressBarLarge: {
    height: 10,
    backgroundColor: 'rgba(255,255,255,0.15)',
    borderRadius: 5,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  progressFillLarge: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 5,
  },
  planActionButton: {
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(102, 126, 234, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    minHeight: 44,
    zIndex: 10,
  },
  planActionButtonActive: {
    backgroundColor: 'rgba(102, 126, 234, 0.8)',
    borderColor: 'rgba(102, 126, 234, 1)',
  },
  planActionText: {
    color: '#667eea',
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    textAlign: 'center',
  },
  planActionTextActive: {
    color: '#fff',
  },
  notesSection: {
    marginTop: 15,
    marginBottom: 10,
  },
  notesLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255,255,255,0.7)',
    marginBottom: 5,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  nutritionItem: {
    width: '48%',
    marginBottom: 8,
  },
  nutritionInput: {
    borderColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 8,
    color: '#fff',
    fontFamily: 'Poppins_400Regular',
    backgroundColor: 'rgba(255,255,255,0.05)',
    textAlign: 'center',
    fontSize: 14,
  },
  nutritionLabel: {
    color: 'rgba(255,255,255,0.6)',
    fontFamily: 'Poppins_400Regular',
    fontSize: 11,
    textAlign: 'center',
    marginTop: 4,
  },
  sectionSubtitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginTop: 15,
    marginBottom: 10,
  },
  dayDetailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  dayDetailsTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  // Menu Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingTop: 100,
  },
  menuModal: {
    backgroundColor: '#1a1a2e',
    width: '75%',
    height: 'auto',
    maxHeight: '70%',
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    paddingTop: 15,
    paddingBottom: 25,
    shadowColor: '#000',
    shadowOffset: { width: -2, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 15,
    marginRight: 0,
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.08)',
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
    marginLeft: 12,
  },
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  menuTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  menuScrollView: {
    maxHeight: 400,
  },
  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  dayBox: {
    width: 35,
    height: 35,
    borderRadius: 18,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 3,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  dayBoxCompleted: {
    backgroundColor: '#4CAF50',
    borderColor: '#388E3C',
  },
  dayBoxSelected: {
    borderColor: '#fff',
    borderWidth: 2,
    backgroundColor: 'rgba(255,255,255,0.15)',
  },
  dayBoxText: {
    color: 'rgba(255,255,255,0.8)',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 12,
  },
  dayBoxTextCompleted: {
    color: '#fff',
  },
  dayBoxTextSelected: {
    color: '#fff',
  },
  dayBoxIcon: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    borderRadius: 10,
    padding: 1,
    overflow: 'hidden',
  },
  dayBoxIconNext: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    borderRadius: 10,
    padding: 1,
    overflow: 'hidden',
  },
  dayBoxLocked: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderColor: 'rgba(255,255,255,0.1)',
    opacity: 0.7,
  },
  dayBoxNext: {
    backgroundColor: 'rgba(33, 150, 243, 0.3)',
    borderColor: 'rgba(33, 150, 243, 0.5)',
  },
  dayBoxTextLocked: {
    color: 'rgba(255,255,255,0.4)',
  },
  dayBoxTextNext: {
    color: '#fff',
    fontWeight: 'bold',
  },
});