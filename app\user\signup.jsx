import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Platform,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { Poppins_400Regular, Poppins_700Bold, Poppins_600SemiBold, useFonts } from '@expo-google-fonts/poppins';
import { Animated as RNAnimated } from 'react-native';
import Animated, { SlideInUp } from 'react-native-reanimated';
import * as Notifications from 'expo-notifications';
// Temporarily disable Firebase imports
// import { savePushTokenToFirebase } from '../../firebaseConfig';

// Mock Firebase function
const savePushTokenToFirebase = () => Promise.resolve();
import { useUser } from '../../context/UserContext';

const { width, height } = Dimensions.get('window');

const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;
  const scaleAnim = useRef(new RNAnimated.Value(0)).current;
  const rotationAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.parallel([
          RNAnimated.timing(particleAnim, {
            toValue: 1,
            duration: (4000 + Math.random() * 3000) * speed,
            useNativeDriver: true,
          }),
          RNAnimated.timing(opacityAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
          RNAnimated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 40,
            useNativeDriver: true,
          }),
        ]),
        RNAnimated.parallel([
          RNAnimated.timing(opacityAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
          RNAnimated.timing(scaleAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      ])
    );
    RNAnimated.loop(
      RNAnimated.timing(rotationAnim, {
        toValue: 1,
        duration: 8000 + Math.random() * 4000,
        useNativeDriver: true,
      })
    ).start();
    setTimeout(() => animation.start(), delay);
  }, []);

  return (
    <RNAnimated.View
      style={[
        style,
        {
          opacity: opacityAnim,
          transform: [
            {
              translateY: particleAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -height * 1.5],
              }),
            },
            {
              scale: scaleAnim,
            },
            {
              rotate: rotationAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '360deg'],
              }),
            },
          ],
        },
      ]}
    />
  );
};

const FloatingShape = ({ style, delay = 0, rotationSpeed = 1 }) => {
  const floatAnim = useRef(new RNAnimated.Value(0)).current;
  const rotationAnim = useRef(new RNAnimated.Value(0)).current;
  const scaleAnim = useRef(new RNAnimated.Value(0.8)).current;

  useEffect(() => {
    RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(floatAnim, {
          toValue: 1,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
        RNAnimated.timing(floatAnim, {
          toValue: 0,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
    RNAnimated.loop(
      RNAnimated.timing(rotationAnim, {
        toValue: 1,
        duration: 15000 * rotationSpeed,
        useNativeDriver: true,
      })
    ).start();
    RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 2000,
          useNativeDriver: true,
        }),
        RNAnimated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  return (
    <RNAnimated.View
      style={[
        style,
        {
          transform: [
            {
              translateY: floatAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -30],
              }),
            },
            {
              rotate: rotationAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '360deg'],
              }),
            },
            {
              scale: scaleAnim,
            },
          ],
        },
      ]}
    />
  );
};

const Shimmer = ({ children, style }) => {
  const shimmerAnim = useRef(new RNAnimated.Value(0)).current;
  useEffect(() => {
    RNAnimated.loop(
      RNAnimated.timing(shimmerAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: false,
      })
    ).start();
  }, []);
  const shimmerOpacity = shimmerAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.3, 0.8, 0.3],
  });
  return (
    <View style={[style, { overflow: 'hidden' }]}>
      {children}
      <RNAnimated.View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          opacity: shimmerOpacity,
          transform: [
            {
              translateX: shimmerAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [-width, width],
              }),
            },
          ],
        }}
      />
    </View>
  );
};

const GradientBackButton = ({ onPress, style }) => (
  <TouchableOpacity onPress={onPress} style={[styles.backButton, style]}>
    <LinearGradient
      colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
      style={styles.backButtonGradient}
    >
      <Ionicons name="arrow-back" size={24} color="#ffffff" />
    </LinearGradient>
  </TouchableOpacity>
);

function getPasswordStrength(password) {
  let score = 0;
  if (password.length >= 8) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[0-9]/.test(password)) score++;
  if (/[^A-Za-z0-9]/.test(password)) score++;
  return score;
}

export default function UserSignup() {
  const router = useRouter();
  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_700Bold,
    Poppins_600SemiBold,
  });
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [nameError, setNameError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [emailUnique, setEmailUnique] = useState(true);

  const fadeAnim = useRef(new RNAnimated.Value(0)).current;
  const slideAnim = useRef(new RNAnimated.Value(50)).current;
  const buttonScale = useRef(new RNAnimated.Value(1)).current;
  const backScale = useRef(new RNAnimated.Value(1)).current;
  const formScale = useRef(new RNAnimated.Value(0.9)).current;
  const pulseAnim = useRef(new RNAnimated.Value(1)).current;

  const { registerUser, logoutUser } = useUser();

  useEffect(() => {
    // Main entrance animations
    RNAnimated.stagger(200, [
      RNAnimated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      RNAnimated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      RNAnimated.spring(formScale, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
    
    // Logo pulse animation
    RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1500,
          useNativeDriver: true,
        }),
        RNAnimated.timing(pulseAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const validateName = (name) => name.trim().length >= 2;
  const validateEmail = (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  const validatePassword = (password) => getPasswordStrength(password) >= 4;
  const validateConfirmPassword = (confirmPassword) => confirmPassword === password;

  const handleNameChange = (text) => {
    setName(text);
    setNameError('');
    setErrorMessage('');
  };
  const handleEmailChange = async (text) => {
    setEmail(text);
    setEmailError('');
    setErrorMessage('');
    if (validateEmail(text)) {
      const usersData = await AsyncStorage.getItem('@FitnessApp:users');
      const users = usersData ? JSON.parse(usersData) : [];
      setEmailUnique(!users.find(u => u.email === text.trim().toLowerCase()));
    } else {
      setEmailUnique(true);
    }
  };
  const handlePasswordChange = (text) => {
    setPassword(text);
    setPasswordError('');
    setErrorMessage('');
  };
  const handleConfirmPasswordChange = (text) => {
    setConfirmPassword(text);
    setConfirmPasswordError('');
    setErrorMessage('');
  };

  const handleSignup = async () => {
    try {
      // Always logout first to clear any previous user data
      if (logoutUser) await logoutUser();
      // Now register the new user
      const user = await registerUser({ name, email, password });
      if (user) {
        // Navigate to profile setup or home
        router.replace('/profile/selectdetails_new '); //selectdetails_new   // router.replace('/profile/fitness-level');
      }
    } catch (error) {
      Alert.alert('Signup Error', error.message || 'Failed to sign up.');
    }
  };

  const handleBackPress = () => {
    RNAnimated.sequence([
      RNAnimated.timing(backScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      RNAnimated.spring(backScale, {
        toValue: 1,
        friction: 3,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start(() => {
      router.push('/user/login');
    });
  };

  const handleGoogleSignup = async () => {
    try {
      setIsLoading(true);
      setErrorMessage('');
      
      // Animate button press
      RNAnimated.sequence([
        RNAnimated.timing(buttonScale, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        RNAnimated.spring(buttonScale, {
          toValue: 1,
          friction: 3,
          tension: 40,
          useNativeDriver: true,
        }),
      ]).start();
      
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const googleUser = {
        email: '<EMAIL>',
        name: 'Google User',
        password: 'google123',
        joinDate: new Date().toISOString(),
        lastLogin: new Date().toISOString(),
        provider: 'google'
      };
      
      const usersData = await AsyncStorage.getItem('@FitnessApp:users');
      const users = usersData ? JSON.parse(usersData) : [];
      
      const existingUser = users.find(u => u.email === googleUser.email);
      if (!existingUser) {
        users.push(googleUser);
        await AsyncStorage.setItem('@FitnessApp:users', JSON.stringify(users));
      } else {
        // Update last login time for existing user
        const updatedUsers = users.map(u => 
          u.email === googleUser.email ? {...u, lastLogin: new Date().toISOString()} : u
        );
        await AsyncStorage.setItem('@FitnessApp:users', JSON.stringify(updatedUsers));
      }
      
      await AsyncStorage.setItem('@FitnessApp:currentUser', JSON.stringify(googleUser));
      router.push('/profile/selectdetails');
    } catch (error) {
      console.error('Google sign-up error:', error);
      setErrorMessage('Google sign-up failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ffffff" />
      </View>
    );
  }

  const passwordStrength = getPasswordStrength(password);

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      <LinearGradient
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      />
      <AdvancedParticle style={[styles.particle, { left: wp(10), top: hp(20) }]} delay={0} speed={1.2} />
      <AdvancedParticle style={[styles.particle, { left: wp(80), top: hp(30) }]} delay={500} speed={0.8} />
      <AdvancedParticle style={[styles.particle, { left: wp(20), top: hp(70) }]} delay={1000} speed={1.5} />
      <FloatingShape style={[styles.floatingShape, { left: wp(85), top: hp(15) }]} delay={0} rotationSpeed={0.5} />
      <FloatingShape style={[styles.floatingShape, { left: wp(5), top: hp(80) }]} delay={1000} rotationSpeed={1.2} />
      <View style={[styles.glowCircle, { top: hp(10), right: wp(10) }]} />
      <View style={[styles.glowCircle, { bottom: hp(20), left: wp(5) }]} />
      <RNAnimated.View style={[styles.backButtonContainer, { transform: [{ scale: backScale }] }]}>
        <GradientBackButton onPress={handleBackPress} />
      </RNAnimated.View>
      <RNAnimated.View 
        style={[
          styles.content, 
          { 
            opacity: fadeAnim, 
            transform: [
              { translateY: slideAnim }, 
              { scale: formScale }
            ] 
          }
        ]}
      >
        <View style={styles.header}>
          <RNAnimated.View style={[styles.logoContainer, { transform: [{ scale: pulseAnim }] }]}>
            <LinearGradient
              colors={['rgba(102, 126, 234, 0.3)', 'rgba(118, 75, 162, 0.2)', 'rgba(240, 147, 251, 0.1)']}
              style={styles.logoGradient}
            >
              <Ionicons name="fitness" size={hp(8)} color="#fff" />
            </LinearGradient>
          </RNAnimated.View>
          <Text style={styles.title}>Create Account</Text>
          <Text style={styles.subtitle}>Sign up to start your fitness journey</Text>
        </View>
        
        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Name</Text>
            <View style={[styles.inputWrapper, nameError ? styles.inputError : null]}>
              <Ionicons name="person-outline" size={20} color="#ffffff" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Enter your name"
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                value={name}
                onChangeText={handleNameChange}
                autoCapitalize="words"
              />
              {name && validateName(name) && !nameError ? (
                <Ionicons name="checkmark-circle" size={20} color="#4cd137" style={{ marginLeft: 8 }} />
              ) : null}
            </View>
            {nameError ? <Text style={styles.errorText}>{nameError}</Text> : null}
          </View>
          
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Email</Text>
            <View style={[styles.inputWrapper, emailError ? styles.inputError : null]}>
              <Ionicons name="mail-outline" size={20} color="#ffffff" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Enter your email"
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                value={email}
                onChangeText={handleEmailChange}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {email && validateEmail(email) && emailUnique && !emailError ? (
                <Ionicons name="checkmark-circle" size={20} color="#4cd137" style={{ marginLeft: 8 }} />
              ) : null}
            </View>
            {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
          </View>
          
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Password</Text>
            <View style={[styles.inputWrapper, passwordError ? styles.inputError : null]}>
              <Ionicons name="lock-closed-outline" size={20} color="#ffffff" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Create a password"
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                value={password}
                onChangeText={handlePasswordChange}
                secureTextEntry={!showPassword}
              />
              <TouchableOpacity onPress={() => setShowPassword(!showPassword)} style={styles.eyeIcon}>
                <Ionicons name={showPassword ? "eye-off-outline" : "eye-outline"} size={20} color="#ffffff" />
              </TouchableOpacity>
              {password && validatePassword(password) && !passwordError ? (
                <Ionicons name="checkmark-circle" size={20} color="#4cd137" style={{ marginLeft: 8 }} />
              ) : null}
            </View>
            
            <View style={styles.passwordStrengthBarContainer}>
              <View style={[styles.passwordStrengthBar, { backgroundColor: passwordStrength >= 1 ? '#ff7675' : '#dfe6e9', width: '20%' }]} />
              <View style={[styles.passwordStrengthBar, { backgroundColor: passwordStrength >= 2 ? '#fdcb6e' : '#dfe6e9', width: '20%' }]} />
              <View style={[styles.passwordStrengthBar, { backgroundColor: passwordStrength >= 3 ? '#00b894' : '#dfe6e9', width: '20%' }]} />
              <View style={[styles.passwordStrengthBar, { backgroundColor: passwordStrength >= 4 ? '#0984e3' : '#dfe6e9', width: '20%' }]} />
              <View style={[styles.passwordStrengthBar, { backgroundColor: passwordStrength >= 5 ? '#6c5ce7' : '#dfe6e9', width: '20%' }]} />
            </View>
            
            {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
          </View>
          
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Confirm Password</Text>
            <View style={[styles.inputWrapper, confirmPasswordError ? styles.inputError : null]}>
              <Ionicons name="lock-closed-outline" size={20} color="#ffffff" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Confirm your password"
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                value={confirmPassword}
                onChangeText={handleConfirmPasswordChange}
                secureTextEntry={!showConfirmPassword}
              />
              <TouchableOpacity onPress={() => setShowConfirmPassword(!showConfirmPassword)} style={styles.eyeIcon}>
                <Ionicons name={showConfirmPassword ? "eye-off-outline" : "eye-outline"} size={20} color="#ffffff" />
              </TouchableOpacity>
              {confirmPassword && validateConfirmPassword(confirmPassword) && !confirmPasswordError ? (
                <Ionicons name="checkmark-circle" size={20} color="#4cd137" style={{ marginLeft: 8 }} />
              ) : null}
            </View>
            {confirmPasswordError ? <Text style={styles.errorText}>{confirmPasswordError}</Text> : null}
          </View>
          
          {errorMessage ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorMessage}>{errorMessage}</Text>
            </View>
          ) : null}
          
          <RNAnimated.View style={{ transform: [{ scale: buttonScale }] }}>
            <Shimmer style={styles.signupButtonContainer}>
              <TouchableOpacity style={styles.signupButton} onPress={handleSignup} disabled={isLoading}>
                <LinearGradient colors={['#667eea', '#764ba2']} style={styles.signupButtonGradient}>
                  {isLoading ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <Text style={styles.signupButtonText}>Create Account</Text>
                  )}
                </LinearGradient>
              </TouchableOpacity>
            </Shimmer>
          </RNAnimated.View>
          
          <View style={styles.signinContainer}>
            <Text style={styles.signinText}>Already have an account? </Text>
            <TouchableOpacity onPress={() => router.push('/user/login')}>
              <Text style={styles.signinLink}>Sign in</Text>
            </TouchableOpacity>
          </View>
        </View>
      </RNAnimated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#0c0c0c' },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#0c0c0c' },
  background: { position: 'absolute', left: 0, right: 0, top: 0, bottom: 0 },
  particle: { position: 'absolute', width: 4, height: 4, backgroundColor: 'rgba(255, 255, 255, 0.3)', borderRadius: 2 },
  floatingShape: { position: 'absolute', width: 20, height: 20, backgroundColor: 'rgba(255, 255, 255, 0.1)', borderRadius: 10 },
  glowCircle: { position: 'absolute', width: 100, height: 100, borderRadius: 50, backgroundColor: 'rgba(102, 126, 234, 0.1)', shadowColor: '#667eea', shadowOffset: { width: 0, height: 0 }, shadowOpacity: 0.5, shadowRadius: 20, elevation: 10 },
  backButtonContainer: { position: 'absolute', top: hp(5), left: wp(5), zIndex: 10 },
  backButton: { width: 50, height: 50, borderRadius: 25, overflow: 'hidden' },
  backButtonGradient: { flex: 1, justifyContent: 'center', alignItems: 'center', borderRadius: 25 },
  content: { 
    flex: 1, 
    justifyContent: 'center', 
    paddingHorizontal: wp(6), 
    paddingTop: Platform.OS === 'ios' ? hp(10) : hp(5),
    paddingBottom: Platform.OS === 'ios' ? hp(5) : hp(3),
  },
  logoContainer: {
    width: Platform.OS === 'ios' ? wp(25) : wp(20),
    height: Platform.OS === 'ios' ? wp(25) : wp(20),
    borderRadius: Platform.OS === 'ios' ? wp(12.5) : wp(10),
    overflow: 'hidden',
    marginBottom: hp(2),
    alignSelf: 'center',
  },
  logoGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: { 
    alignItems: 'center', 
    marginBottom: Platform.OS === 'ios' ? hp(4) : hp(2) 
  },
  title: { 
    fontSize: Platform.OS === 'ios' ? wp(8) : wp(7), 
    fontFamily: 'Poppins_700Bold', 
    color: '#ffffff', 
    textAlign: 'center', 
    marginBottom: hp(0.5) 
  },
  subtitle: { 
    fontSize: Platform.OS === 'ios' ? wp(4) : wp(3.5), 
    fontFamily: 'Poppins_400Regular', 
    color: 'rgba(255, 255, 255, 0.7)', 
    textAlign: 'center' 
  },
  form: { width: '100%' },
  inputContainer: { marginBottom: Platform.OS === 'ios' ? hp(3) : hp(2) },
  inputLabel: { 
    fontSize: Platform.OS === 'ios' ? wp(4) : wp(3.5), 
    fontFamily: 'Poppins_600SemiBold', 
    color: '#ffffff', 
    marginBottom: hp(0.5) 
  },
  inputWrapper: { 
    flexDirection: 'row', 
    alignItems: 'center', 
    backgroundColor: 'rgba(255, 255, 255, 0.1)', 
    borderRadius: 12, 
    paddingHorizontal: wp(3), 
    paddingVertical: Platform.OS === 'ios' ? hp(2) : hp(1), 
    borderWidth: 1, 
    borderColor: 'rgba(255, 255, 255, 0.2)' 
  },
  inputError: { 
    borderColor: '#ff6b6b' 
  },
  inputIcon: { 
    marginRight: wp(3) 
  },
  input: { 
    flex: 1, 
    fontSize: Platform.OS === 'ios' ? wp(4) : wp(3.5), 
    fontFamily: 'Poppins_400Regular', 
    color: '#ffffff',
    paddingVertical: Platform.OS === 'ios' ? hp(1.2) : hp(0.8),
    height: Platform.OS === 'ios' ? hp(5) : hp(4.5),
  },
  eyeIcon: { padding: wp(1) },
  errorText: { fontSize: wp(3.5), fontFamily: 'Poppins_400Regular', color: '#ff6b6b', marginTop: hp(1) },
  errorContainer: { backgroundColor: 'rgba(255, 107, 107, 0.1)', borderRadius: 8, padding: wp(3), marginBottom: hp(3), borderWidth: 1, borderColor: 'rgba(255, 107, 107, 0.3)' },
  errorMessage: { fontSize: wp(3.5), fontFamily: 'Poppins_400Regular', color: '#ff6b6b', textAlign: 'center' },
  signupButtonContainer: { 
    borderRadius: 12, 
    marginBottom: Platform.OS === 'ios' ? hp(3) : hp(2), 
    overflow: 'hidden' 
  },
  signupButton: { width: '100%' },
  signupButtonGradient: { 
    paddingVertical: Platform.OS === 'ios' ? hp(2.5) : hp(2), 
    alignItems: 'center', 
    borderRadius: 12 
  },
  signupButtonText: { 
    fontSize: Platform.OS === 'ios' ? wp(4.5) : wp(4), 
    fontFamily: 'Poppins_600SemiBold', 
    color: '#ffffff' 
  },
  signinContainer: { 
    flexDirection: 'row', 
    justifyContent: 'center', 
    alignItems: 'center',
    marginTop: hp(4),
    paddingVertical: hp(1),
  },
  signinText: { 
    fontSize: wp(3.8), 
    fontFamily: 'Poppins_400Regular', 
    color: 'rgba(255, 255, 255, 0.7)' 
  },
  signinLink: { 
    fontSize: wp(3.8), 
    fontFamily: 'Poppins_600SemiBold', 
    color: '#667eea',
    marginLeft: wp(1),
    textDecorationLine: 'underline',
  },
  dividerContainer: { flexDirection: 'row', alignItems: 'center', marginBottom: hp(4), marginTop: hp(2) },
  dividerLine: { flex: 1, height: 1, backgroundColor: 'rgba(255, 255, 255, 0.2)' },
  dividerText: { fontSize: wp(3.5), fontFamily: 'Poppins_400Regular', color: 'rgba(255, 255, 255, 0.5)', marginHorizontal: wp(4) },
  googleButtonContainer: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: hp(4),
  },
  googleButton: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  googleButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(2.5),
  },
  googleIconContainer: {
    marginRight: wp(3),
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    width: wp(8),
    height: wp(8),
    borderRadius: wp(4),
    justifyContent: 'center',
    alignItems: 'center',
  },
  googleButtonText: { 
    fontSize: wp(4), 
    fontFamily: 'Poppins_600SemiBold', 
    color: '#ffffff',
  },
  passwordStrengthBarContainer: { flexDirection: 'row', marginTop: 6, marginBottom: 2 },
  passwordStrengthBar: { height: 5, borderRadius: 2, marginRight: 2 },
  fixedButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 30,
    alignItems: 'center',
    zIndex: 100,
  },
  button: {
    width: '80%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(2.5),
  },
  buttonText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: '#ffffff',
  },
  buttonTextDisabled: {
    color: 'rgba(255,255,255,0.5)',
  },
}); 