import React, { useState, useCallback } from 'react';
import {
  View,
  Image,
  StyleSheet,
  ActivityIndicator,
  Text,
  TouchableOpacity,
  ViewStyle,
  ImageStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface MediaRendererProps {
  type: 'image' | 'video' | 'youtube';
  source: string;
  fallbackSource?: string;
  style?: ViewStyle;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'center';
  onPress?: () => void;
}

const MediaRenderer: React.FC<MediaRendererProps> = ({
  type,
  source,
  fallbackSource,
  style,
  resizeMode = 'cover',
  onPress,
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [currentSource, setCurrentSource] = useState(source);

  const handleImageLoad = useCallback(() => {
    setLoading(false);
    setError(false);
  }, []);

  const handleImageError = useCallback(() => {
    setLoading(false);
    if (fallbackSource && currentSource !== fallbackSource) {
      setCurrentSource(fallbackSource);
      setError(false);
    } else {
      setError(true);
    }
  }, [fallbackSource, currentSource]);

  const renderImage = () => (
    <View style={[styles.container, style]}>
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#667eea" />
        </View>
      )}
      {error ? (
        <View style={[styles.errorContainer, style]}>
          <Ionicons name="image-outline" size={40} color="#666" />
          <Text style={styles.errorText}>Image not available</Text>
        </View>
      ) : (
        <Image
          source={{ uri: currentSource }}
          style={[styles.image, style]}
          resizeMode={resizeMode}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      )}
    </View>
  );

  const renderVideo = () => (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.videoContainer}>
        <Image
          source={{ uri: currentSource }}
          style={[styles.image, style]}
          resizeMode={resizeMode}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
        <View style={styles.playButton}>
          <Ionicons name="play" size={30} color="#fff" />
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderYoutube = () => (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.videoContainer}>
        <Image
          source={{ uri: currentSource }}
          style={[styles.image, style]}
          resizeMode={resizeMode}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
        <View style={styles.playButton}>
          <Ionicons name="logo-youtube" size={30} color="#FF0000" />
        </View>
      </View>
    </TouchableOpacity>
  );

  switch (type) {
    case 'video':
      return renderVideo();
    case 'youtube':
      return renderYoutube();
    case 'image':
    default:
      return renderImage();
  }
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    backgroundColor: '#f0f0f0',
    borderRadius: 12,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    marginTop: 8,
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  videoContainer: {
    position: 'relative',
    width: '100%',
    height: '100%',
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -15 }, { translateY: -15 }],
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MediaRenderer;