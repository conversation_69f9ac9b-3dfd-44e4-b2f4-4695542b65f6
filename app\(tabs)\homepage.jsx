<<<<<<<
<<<<<<<



import React from 'react';















import { ScrollView, StyleSheet, View, Text } from 'react-native';















import { ThemedView } from '@/components/ThemedView';















import { ThemedText } from '@/components/ThemedText';















import ImageSlider from '@/components/ImageSlider';















import MediaRenderer from '@/components/MediaRenderer';















import { useThemeColor } from '@/hooks/useThemeColor';































const Homepage = () => {















  const backgroundColor = useThemeColor({}, 'background');















  const textColor = useThemeColor({}, 'text');































  // Sample slider data with proper image URLs















  const sliderImages = [















    {















      id: '1',















      type: 'image',















      source: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',















      title: 'Strength Training',















      description: 'Build muscle and increase strength',















      fallbackSource: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400&h=300&fit=crop'















    },















    {















      id: '2',















      type: 'image',















      source: 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop',















      title: 'Cardio Workout',















      description: 'Improve cardiovascular health',















      fallbackSource: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=400&h=300&fit=crop'















    },















    {















      id: '3',















      type: 'image',















      source: 'https://images.unsplash.com/photo-1506629905607-d5b4e2e0e4b5?w=400&h=300&fit=crop',















      title: 'Yoga & Flexibility',















      description: 'Enhance flexibility and mindfulness',















      fallbackSource: 'https://images.unsplash.com/photo-1588286840104-8957b019727f?w=400&h=300&fit=crop'















    },















    {















      id: '4',















      type: 'youtube',















      source: 'https://www.youtube.com/watch?v=tWjBnQX3if0',















      title: '5 Minute Push Ups',















      description: 'Quick push-up workout routine',















    },















  ];































  // Featured workout data















  const featuredWorkouts = [















    {















      id: '1',















      type: 'image',















      source: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop',















      title: 'Upper Body',















    },















    {















      id: '2',















      type: 'image',















      source: 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=300&h=200&fit=crop',















      title: 'Lower Body',















    },















    {















      id: '3',















      type: 'image',















      source: 'https://images.unsplash.com/photo-1506629905607-d5b4e2e0e4b5?w=300&h=200&fit=crop',















      title: 'Full Body',















    },















  ];































  const handleSliderItemPress = (item, index) => {















    console.log('Slider item pressed:', item.title, 'at index:', index);















    // Handle navigation or modal opening here















  };































  const handleWorkoutPress = (workout) => {















    console.log('Workout pressed:', workout.title);















    // Handle workout selection here















  };































  return (















    <ScrollView style={[styles.container, { backgroundColor }]}>















      <ThemedView style={styles.content}>















        {/* Header */}















        <View style={styles.header}>















          <ThemedText type="title" style={styles.welcomeText}>















            Welcome to My Fitness















          </ThemedText>















          <ThemedText style={styles.subtitleText}>















            Your journey to better health starts here















          </ThemedText>















        </View>































        {/* Image Slider */}















        <View style={styles.section}>















          <ThemedText type="subtitle" style={styles.sectionTitle}>















            Featured Workouts















          </ThemedText>















          <ImageSlider















            data={sliderImages}















            height={220}















            autoPlay={true}















            autoPlayInterval={4000}















            showPagination={true}















            showTitles={true}















            onItemPress={handleSliderItemPress}















          />















        </View>































        {/* Quick Access Workouts */}















        <View style={styles.section}>















          <ThemedText type="subtitle" style={styles.sectionTitle}>















            Quick Workouts















          </ThemedText>















          <View style={styles.workoutGrid}>















            {featuredWorkouts.map((workout) => (















              <View key={workout.id} style={styles.workoutCard}>















                <MediaRenderer















                  type={workout.type}















                  source={workout.source}















                  style={styles.workoutImage}















                  resizeMode="cover"















                />















                <View style={styles.workoutInfo}>















                  <ThemedText style={styles.workoutTitle}>















                    {workout.title}















                  </ThemedText>















                </View>















              </View>















            ))}















          </View>















        </View>































        {/* Daily Motivation */}















        <View style={styles.section}>















          <ThemedText type="subtitle" style={styles.sectionTitle}>















            Daily Motivation















          </ThemedText>















          <View style={styles.motivationCard}>















            <MediaRenderer















              type="image"















              source="https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=400&h=200&fit=crop"















              style={styles.motivationImage}















              resizeMode="cover"















            />















            <View style={styles.motivationContent}>















              <ThemedText style={styles.motivationText}>















                "The only bad workout is the one that didn't happen."















              </ThemedText>















              <ThemedText style={styles.motivationAuthor}>















                - Unknown















              </ThemedText>















            </View>















          </View>















        </View>















      </ThemedView>















    </ScrollView>















  );















};































const styles = StyleSheet.create({















  container: {















    flex: 1,















  },















  content: {















    padding: 16,















  },















  header: {















    marginBottom: 24,















    alignItems: 'center',















  },















  welcomeText: {















    marginBottom: 8,















    textAlign: 'center',















  },















  subtitleText: {















    textAlign: 'center',















    opacity: 0.7,















  },















  section: {















    marginBottom: 32,















  },















  sectionTitle: {















    marginBottom: 16,















    fontWeight: '600',















  },















  workoutGrid: {















    flexDirection: 'row',















    flexWrap: 'wrap',















    justifyContent: 'space-between',















  },















  workoutCard: {















    width: '48%',















    marginBottom: 16,















    borderRadius: 12,















    overflow: 'hidden',















    elevation: 2,















    shadowColor: '#000',















    shadowOffset: { width: 0, height: 2 },















    shadowOpacity: 0.1,















    shadowRadius: 4,















  },















  workoutImage: {















    width: '100%',















    height: 120,















  },















  workoutInfo: {















    padding: 12,















  },















  workoutTitle: {















    fontWeight: '500',















    textAlign: 'center',















  },















  motivationCard: {















    borderRadius: 12,















    overflow: 'hidden',















    elevation: 2,















    shadowColor: '#000',















    shadowOffset: { width: 0, height: 2 },















    shadowOpacity: 0.1,















    shadowRadius: 4,















  },















  motivationImage: {















    width: '100%',















    height: 150,















  },















  motivationContent: {















    padding: 16,















  },















  motivationText: {















    fontSize: 16,















    fontStyle: 'italic',















    textAlign: 'center',















    marginBottom: 8,















  },















  motivationAuthor: {















    textAlign: 'center',















    opacity: 0.7,















  },















});































export default Homepage;















=======



import React from 'react';







import { ScrollView, StyleSheet, View, Text } from 'react-native';







import { ThemedView } from '@/components/ThemedView';







import { ThemedText } from '@/components/ThemedText';







import ImageSlider from '@/components/ImageSlider';







import MediaRenderer from '@/components/MediaRenderer';







import { useThemeColor } from '@/hooks/useThemeColor';















const Homepage = () => {







  const backgroundColor = useThemeColor({}, 'background');







  const textColor = useThemeColor({}, 'text');















  // Sample slider data with proper image URLs







  const sliderImages = [







    {







      id: '1',







      type: 'image',







      source: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',







      title: 'Strength Training',







      description: 'Build muscle and increase strength',







      fallbackSource: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400&h=300&fit=crop'







    },







    {







      id: '2',







      type: 'image',







      source: 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop',







      title: 'Cardio Workout',







      description: 'Improve cardiovascular health',







      fallbackSource: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=400&h=300&fit=crop'







    },







    {







      id: '3',







      type: 'image',







      source: 'https://images.unsplash.com/photo-1506629905607-d5b4e2e0e4b5?w=400&h=300&fit=crop',







      title: 'Yoga & Flexibility',







      description: 'Enhance flexibility and mindfulness',







      fallbackSource: 'https://images.unsplash.com/photo-1588286840104-8957b019727f?w=400&h=300&fit=crop'







    },







    {







      id: '4',







      type: 'youtube',







      source: 'https://www.youtube.com/watch?v=tWjBnQX3if0',







      title: '5 Minute Push Ups',







      description: 'Quick push-up workout routine',







    },







  ];















  // Featured workout data







  const featuredWorkouts = [







    {







      id: '1',







      type: 'image',







      source: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop',







      title: 'Upper Body',







    },







    {







      id: '2',







      type: 'image',







      source: 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=300&h=200&fit=crop',







      title: 'Lower Body',







    },







    {







      id: '3',







      type: 'image',







      source: 'https://images.unsplash.com/photo-1506629905607-d5b4e2e0e4b5?w=300&h=200&fit=crop',







      title: 'Full Body',







    },







  ];















  const handleSliderItemPress = (item, index) => {







    console.log('Slider item pressed:', item.title, 'at index:', index);







    // Handle navigation or modal opening here







  };















  const handleWorkoutPress = (workout) => {







    console.log('Workout pressed:', workout.title);







    // Handle workout selection here







  };















  return (







    <ScrollView style={[styles.container, { backgroundColor }]}>







      <ThemedView style={styles.content}>







        {/* Header */}







        <View style={styles.header}>







          <ThemedText type="title" style={styles.welcomeText}>







            Welcome to My Fitness







          </ThemedText>







          <ThemedText style={styles.subtitleText}>







            Your journey to better health starts here







          </ThemedText>







        </View>















        {/* Image Slider */}







        <View style={styles.section}>







          <ThemedText type="subtitle" style={styles.sectionTitle}>







            Featured Workouts







          </ThemedText>







          <ImageSlider







            data={sliderImages}







            height={220}







            autoPlay={true}







            autoPlayInterval={4000}







            showPagination={true}







            showTitles={true}







            onItemPress={handleSliderItemPress}







          />







        </View>















        {/* Quick Access Workouts */}







        <View style={styles.section}>







          <ThemedText type="subtitle" style={styles.sectionTitle}>







            Quick Workouts







          </ThemedText>







          <View style={styles.workoutGrid}>







            {featuredWorkouts.map((workout) => (







              <View key={workout.id} style={styles.workoutCard}>







                <MediaRenderer







                  type={workout.type}







                  source={workout.source}







                  style={styles.workoutImage}







                  resizeMode="cover"







                />







                <View style={styles.workoutInfo}>







                  <ThemedText style={styles.workoutTitle}>







                    {workout.title}







                  </ThemedText>







                </View>







              </View>







            ))}







          </View>







        </View>















        {/* Daily Motivation */}







        <View style={styles.section}>







          <ThemedText type="subtitle" style={styles.sectionTitle}>







            Daily Motivation







          </ThemedText>







          <View style={styles.motivationCard}>







            <MediaRenderer







              type="image"







              source="https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=400&h=200&fit=crop"







              style={styles.motivationImage}







              resizeMode="cover"







            />







            <View style={styles.motivationContent}>







              <ThemedText style={styles.motivationText}>







                "The only bad workout is the one that didn't happen."







              </ThemedText>







              <ThemedText style={styles.motivationAuthor}>







                - Unknown







              </ThemedText>







            </View>







          </View>







        </View>







      </ThemedView>







    </ScrollView>







  );







};















const styles = StyleSheet.create({







  container: {







    flex: 1,







  },







  content: {







    padding: 16,







  },







  header: {







    marginBottom: 24,







    alignItems: 'center',







  },







  welcomeText: {







    marginBottom: 8,







    textAlign: 'center',







  },







  subtitleText: {







    textAlign: 'center',







    opacity: 0.7,







  },







  section: {







    marginBottom: 32,







  },







  sectionTitle: {







    marginBottom: 16,







    fontWeight: '600',







  },







  workoutGrid: {







    flexDirection: 'row',







    flexWrap: 'wrap',







    justifyContent: 'space-between',







  },







  workoutCard: {







    width: '48%',







    marginBottom: 16,







    borderRadius: 12,







    overflow: 'hidden',







    elevation: 2,







    shadowColor: '#000',







    shadowOffset: { width: 0, height: 2 },







    shadowOpacity: 0.1,







    shadowRadius: 4,







  },







  workoutImage: {







    width: '100%',







    height: 120,







  },







  workoutInfo: {







    padding: 12,







  },







  workoutTitle: {







    fontWeight: '500',







    textAlign: 'center',







  },







  motivationCard: {







    borderRadius: 12,







    overflow: 'hidden',







    elevation: 2,







    shadowColor: '#000',







    shadowOffset: { width: 0, height: 2 },







    shadowOpacity: 0.1,







    shadowRadius: 4,







  },







  motivationImage: {







    width: '100%',







    height: 150,







  },







  motivationContent: {







    padding: 16,







  },







  motivationText: {







    fontSize: 16,







    fontStyle: 'italic',







    textAlign: 'center',







    marginBottom: 8,







  },







  motivationAuthor: {







    textAlign: 'center',







    opacity: 0.7,







  },







});















export default Homepage;







>>>>>>>



=======
import React from 'react';

import { ScrollView, StyleSheet, View, Text } from 'react-native';

import { ThemedView } from '@/components/ThemedView';

import { ThemedText } from '@/components/ThemedText';

import ImageSlider from '@/components/ImageSlider';

import MediaRenderer from '@/components/MediaRenderer';

import { useThemeColor } from '@/hooks/useThemeColor';



const Homepage = () => {

  const backgroundColor = useThemeColor({}, 'background');

  const textColor = useThemeColor({}, 'text');



  // Sample slider data with proper image URLs

  const sliderImages = [

    {

      id: '1',

      type: 'image',

      source: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',

      title: 'Strength Training',

      description: 'Build muscle and increase strength',

      fallbackSource: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400&h=300&fit=crop'

    },

    {

      id: '2',

      type: 'image',

      source: 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop',

      title: 'Cardio Workout',

      description: 'Improve cardiovascular health',

      fallbackSource: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=400&h=300&fit=crop'

    },

    {

      id: '3',

      type: 'image',

      source: 'https://images.unsplash.com/photo-1506629905607-d5b4e2e0e4b5?w=400&h=300&fit=crop',

      title: 'Yoga & Flexibility',

      description: 'Enhance flexibility and mindfulness',

      fallbackSource: 'https://images.unsplash.com/photo-1588286840104-8957b019727f?w=400&h=300&fit=crop'

    },

    {

      id: '4',

      type: 'youtube',

      source: 'https://www.youtube.com/watch?v=tWjBnQX3if0',

      title: '5 Minute Push Ups',

      description: 'Quick push-up workout routine',

    },

  ];



  // Featured workout data

  const featuredWorkouts = [

    {

      id: '1',

      type: 'image',

      source: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop',

      title: 'Upper Body',

    },

    {

      id: '2',

      type: 'image',

      source: 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=300&h=200&fit=crop',

      title: 'Lower Body',

    },

    {

      id: '3',

      type: 'image',

      source: 'https://images.unsplash.com/photo-1506629905607-d5b4e2e0e4b5?w=300&h=200&fit=crop',

      title: 'Full Body',

    },

  ];



  const handleSliderItemPress = (item, index) => {

    console.log('Slider item pressed:', item.title, 'at index:', index);

    // Handle navigation or modal opening here

  };



  const handleWorkoutPress = (workout) => {

    console.log('Workout pressed:', workout.title);

    // Handle workout selection here

  };



  return (

    <ScrollView style={[styles.container, { backgroundColor }]}>

      <ThemedView style={styles.content}>

        {/* Header */}

        <View style={styles.header}>

          <ThemedText type="title" style={styles.welcomeText}>

            Welcome to My Fitness

          </ThemedText>

          <ThemedText style={styles.subtitleText}>

            Your journey to better health starts here

          </ThemedText>

        </View>



        {/* Image Slider */}

        <View style={styles.section}>

          <ThemedText type="subtitle" style={styles.sectionTitle}>

            Featured Workouts

          </ThemedText>

          <ImageSlider

            data={sliderImages}

            height={220}

            autoPlay={true}

            autoPlayInterval={4000}

            showPagination={true}

            showTitles={true}

            onItemPress={handleSliderItemPress}

          />

        </View>



        {/* Quick Access Workouts */}

        <View style={styles.section}>

          <ThemedText type="subtitle" style={styles.sectionTitle}>

            Quick Workouts

          </ThemedText>

          <View style={styles.workoutGrid}>

            {featuredWorkouts.map((workout) => (

              <View key={workout.id} style={styles.workoutCard}>

                <MediaRenderer

                  type={workout.type}

                  source={workout.source}

                  style={styles.workoutImage}

                  resizeMode="cover"

                />

                <View style={styles.workoutInfo}>

                  <ThemedText style={styles.workoutTitle}>

                    {workout.title}

                  </ThemedText>

                </View>

              </View>

            ))}

          </View>

        </View>



        {/* Daily Motivation */}

        <View style={styles.section}>

          <ThemedText type="subtitle" style={styles.sectionTitle}>

            Daily Motivation

          </ThemedText>

          <View style={styles.motivationCard}>

            <MediaRenderer

              type="image"

              source="https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=400&h=200&fit=crop"

              style={styles.motivationImage}

              resizeMode="cover"

            />

            <View style={styles.motivationContent}>

              <ThemedText style={styles.motivationText}>

                "The only bad workout is the one that didn't happen."

              </ThemedText>

              <ThemedText style={styles.motivationAuthor}>

                - Unknown

              </ThemedText>

            </View>

          </View>

        </View>

      </ThemedView>

    </ScrollView>

  );

};



const styles = StyleSheet.create({

  container: {

    flex: 1,

  },

  content: {

    padding: 16,

  },

  header: {

    marginBottom: 24,

    alignItems: 'center',

  },

  welcomeText: {

    marginBottom: 8,

    textAlign: 'center',

  },

  subtitleText: {

    textAlign: 'center',

    opacity: 0.7,

  },

  section: {

    marginBottom: 32,

  },

  sectionTitle: {

    marginBottom: 16,

    fontWeight: '600',

  },

  workoutGrid: {

    flexDirection: 'row',

    flexWrap: 'wrap',

    justifyContent: 'space-between',

  },

  workoutCard: {

    width: '48%',

    marginBottom: 16,

    borderRadius: 12,

    overflow: 'hidden',

    elevation: 2,

    shadowColor: '#000',

    shadowOffset: { width: 0, height: 2 },

    shadowOpacity: 0.1,

    shadowRadius: 4,

  },

  workoutImage: {

    width: '100%',

    height: 120,

  },

  workoutInfo: {

    padding: 12,

  },

  workoutTitle: {

    fontWeight: '500',

    textAlign: 'center',

  },

  motivationCard: {

    borderRadius: 12,

    overflow: 'hidden',

    elevation: 2,

    shadowColor: '#000',

    shadowOffset: { width: 0, height: 2 },

    shadowOpacity: 0.1,

    shadowRadius: 4,

  },

  motivationImage: {

    width: '100%',

    height: 150,

  },

  motivationContent: {

    padding: 16,

  },

  motivationText: {

    fontSize: 16,

    fontStyle: 'italic',

    textAlign: 'center',

    marginBottom: 8,

  },

  motivationAuthor: {

    textAlign: 'center',

    opacity: 0.7,

  },

});



export default Homepage;

>>>>>>>
