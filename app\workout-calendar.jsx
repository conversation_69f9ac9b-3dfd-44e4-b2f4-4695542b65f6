import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  SafeAreaView,
  StatusBar,
  Animated as RNAnimated,
  Dimensions,
  Platform,
  Modal,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { Poppins_400Regular, Poppins_700Bold, Poppins_600SemiBold, useFonts } from '@expo-google-fonts/poppins';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useUser } from '../context/UserContext';

const { width, height } = Dimensions.get('window');

// Advanced Particle component
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const animSpeed = 4000 * speed + Math.random() * 2000;
    const animDelay = Math.random() * 3000 + delay;
    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(particleAnim, { toValue: 1, duration: animSpeed, useNativeDriver: true }),
        RNAnimated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = RNAnimated.sequence([
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: animSpeed - 1000, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);
    
    const timeoutId = setTimeout(() => {
      animation.start();
      RNAnimated.loop(opacitySequence).start();
    }, animDelay);

    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, [delay, speed]);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <RNAnimated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

export default function WorkoutCalendar() {
  const router = useRouter();
  const { currentUser } = useUser();
  const [workoutDays, setWorkoutDays] = useState([]);
  const [streaks, setStreaks] = useState([]);
  const [selectedDay, setSelectedDay] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [showAddWorkoutModal, setShowAddWorkoutModal] = useState(false);
  const [newWorkout, setNewWorkout] = useState({
    type: '',
    duration: '',
    calories: '',
    notes: ''
  });

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_700Bold,
    Poppins_600SemiBold,
  });

  useEffect(() => {
    fetchWorkoutDays();
  }, [currentUser]);

  const handleBack = () => {
    router.back();
  };

  const fetchWorkoutDays = async () => {
    try {
      let historyStr = null;
      if (currentUser?.email) {
        historyStr = await AsyncStorage.getItem(`@workout_history:${currentUser.email}`);
      }
      
      if (!historyStr) {
        const sampleWorkouts = [];
        const today = new Date();
        
        // Create some sample workouts for the past 30 days
        for (let i = 0; i < 30; i++) {
          const date = new Date(today);
          date.setDate(date.getDate() - i);
          const dateStr = date.toISOString().split('T')[0];
          
          if (Math.random() > 0.7) { // 30% chance of having a workout
            sampleWorkouts.push({
              date: dateStr,
              type: ['Cardio', 'Strength', 'Yoga', 'HIIT'][Math.floor(Math.random() * 4)],
              duration: Math.floor(Math.random() * 60) + 30,
              calories: Math.floor(Math.random() * 400) + 200,
              notes: 'Sample workout'
            });
          }
        }
        
        if (currentUser?.email) {
          await AsyncStorage.setItem(`@workout_history:${currentUser.email}`, JSON.stringify(sampleWorkouts));
        }
        setWorkoutDays(sampleWorkouts);
      } else {
        setWorkoutDays(JSON.parse(historyStr));
      }
      
      // Calculate streaks
      calculateStreaks(JSON.parse(historyStr) || []);
    } catch (error) {
      console.error('Error fetching workout days:', error);
    }
  };

  const calculateStreaks = (workouts) => {
    const today = new Date();
    const streakDays = [];
    let currentStreak = 0;
    
    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      const hasWorkout = workouts.some(w => w.date === dateStr);
      if (hasWorkout) {
        currentStreak++;
        streakDays.push(dateStr);
      } else {
        break;
      }
    }
    
    setStreaks(streakDays);
  };

  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year, month) => {
    return new Date(year, month, 1).getDay();
  };

  const goToPreviousMonth = () => {
    setCurrentMonth(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentMonth(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));
  };

  const addWorkout = async (workout) => {
    try {
      const today = new Date().toISOString().split('T')[0];
      const newWorkoutDay = {
        date: today,
        ...workout,
        timestamp: new Date().toISOString()
      };
      
      const updatedWorkouts = [...workoutDays, newWorkoutDay];
      setWorkoutDays(updatedWorkouts);
      
      if (currentUser?.email) {
        await AsyncStorage.setItem(`@workout_history:${currentUser.email}`, JSON.stringify(updatedWorkouts));
      }
      
      calculateStreaks(updatedWorkouts);
      setShowAddWorkoutModal(false);
      setNewWorkout({ type: '', duration: '', calories: '', notes: '' });
      Alert.alert('Success', 'Workout added successfully!');
    } catch (error) {
      console.error('Error adding workout:', error);
      Alert.alert('Error', 'Failed to add workout');
    }
  };

  const getWorkoutForDay = (day) => {
    const dateStr = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day).toISOString().split('T')[0];
    return workoutDays.find(w => w.date === dateStr);
  };

  const isStreak = (day) => {
    const dateStr = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day).toISOString().split('T')[0];
    return streaks.includes(dateStr);
  };

  const isToday = (day) => {
    const today = new Date();
    return day === today.getDate() && 
           currentMonth.getMonth() === today.getMonth() && 
           currentMonth.getFullYear() === today.getFullYear();
  };

  const renderCalendar = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDay = getFirstDayOfMonth(year, month);
    
    const calendar = [];
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      calendar.push(<View key={`empty-${i}`} style={styles.calendarDay} />);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const workout = getWorkoutForDay(day);
      const hasWorkout = !!workout;
      const isStreakDay = isStreak(day);
      const isTodayDay = isToday(day);

      calendar.push(
        <TouchableOpacity
          key={day}
          style={[
            styles.calendarDay,
            hasWorkout && styles.calendarDayWithWorkout,
            isStreakDay && styles.calendarDayStreak,
            isTodayDay && styles.calendarDayToday
          ]}
          onPress={() => {
            if (hasWorkout) {
              setSelectedDay(workout);
              setModalVisible(true);
            }
          }}
        >
          <Text style={[
            styles.calendarDayText,
            hasWorkout && styles.calendarDayTextWithWorkout,
            isTodayDay && styles.calendarDayTextToday
          ]}>
            {day}
          </Text>
          {hasWorkout && (
            <View style={styles.workoutIndicator}>
              <Ionicons name="fitness" size={8} color="#fff" />
            </View>
          )}
        </TouchableOpacity>
      );
    }

    return (
      <View style={styles.calendarContainer}>
        <View style={styles.calendarHeader}>
          <TouchableOpacity onPress={goToPreviousMonth} style={styles.calendarNavButton}>
            <Ionicons name="chevron-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.calendarTitle}>
            {monthNames[month]} {year}
          </Text>
          <TouchableOpacity onPress={goToNextMonth} style={styles.calendarNavButton}>
            <Ionicons name="chevron-forward" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.calendarDaysHeader}>
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <Text key={day} style={styles.calendarDaysHeaderText}>{day}</Text>
          ))}
        </View>
        
        <View style={styles.calendarGrid}>
          {calendar}
        </View>
      </View>
    );
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      {[...Array(10)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Workout Calendar</Text>
          <TouchableOpacity onPress={() => setShowAddWorkoutModal(true)} style={styles.addButton}>
            <Ionicons name="add" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
        >
          {/* Stats Summary */}
          <Animated.View entering={FadeInUp.delay(100)} style={styles.statsCard}>
            <Text style={styles.statsTitle}>This Month's Progress</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{workoutDays.length}</Text>
                <Text style={styles.statLabel}>Workouts</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{streaks.length}</Text>
                <Text style={styles.statLabel}>Day Streak</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>
                  {workoutDays.reduce((sum, w) => sum + (w.calories || 0), 0)}
                </Text>
                <Text style={styles.statLabel}>Calories</Text>
              </View>
            </View>
          </Animated.View>

          {/* Calendar */}
          <Animated.View entering={FadeInUp.delay(200)} style={styles.calendarCard}>
            {renderCalendar()}
          </Animated.View>
        </ScrollView>

        {/* Workout Details Modal */}
        <Modal
          visible={modalVisible}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Workout Details</Text>
                <TouchableOpacity onPress={() => setModalVisible(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              
              {selectedDay && (
                <View style={styles.workoutDetails}>
                  <Text style={styles.workoutDate}>{selectedDay.date}</Text>
                  <Text style={styles.workoutType}>{selectedDay.type}</Text>
                  <Text style={styles.workoutDuration}>Duration: {selectedDay.duration} minutes</Text>
                  <Text style={styles.workoutCalories}>Calories: {selectedDay.calories}</Text>
                  {selectedDay.notes && (
                    <Text style={styles.workoutNotes}>Notes: {selectedDay.notes}</Text>
                  )}
                </View>
              )}
            </View>
          </View>
        </Modal>

        {/* Add Workout Modal */}
        <Modal
          visible={showAddWorkoutModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowAddWorkoutModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Add Workout</Text>
                <TouchableOpacity onPress={() => setShowAddWorkoutModal(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              
              <TextInput
                style={styles.input}
                placeholder="Workout Type (e.g., Cardio, Strength)"
                placeholderTextColor="rgba(255,255,255,0.5)"
                value={newWorkout.type}
                onChangeText={(text) => setNewWorkout({...newWorkout, type: text})}
              />
              
              <TextInput
                style={styles.input}
                placeholder="Duration (minutes)"
                placeholderTextColor="rgba(255,255,255,0.5)"
                value={newWorkout.duration}
                onChangeText={(text) => setNewWorkout({...newWorkout, duration: text})}
                keyboardType="numeric"
              />
              
              <TextInput
                style={styles.input}
                placeholder="Calories Burned"
                placeholderTextColor="rgba(255,255,255,0.5)"
                value={newWorkout.calories}
                onChangeText={(text) => setNewWorkout({...newWorkout, calories: text})}
                keyboardType="numeric"
              />
              
              <TextInput
                style={[styles.input, styles.notesInput]}
                placeholder="Notes (optional)"
                placeholderTextColor="rgba(255,255,255,0.5)"
                value={newWorkout.notes}
                onChangeText={(text) => setNewWorkout({...newWorkout, notes: text})}
                multiline
              />
              
              <TouchableOpacity 
                style={styles.addWorkoutButton}
                onPress={() => addWorkout(newWorkout)}
              >
                <Text style={styles.addWorkoutButtonText}>Add Workout</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0c0c0c',
  },
  loadingText: {
    color: '#fff',
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(102, 126, 234, 0.6)',
    borderRadius: 2,
    zIndex: 0,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'android' ? 25 : 10,
    paddingBottom: 15,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContainer: {
    padding: 20,
  },
  statsCard: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  statsTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 15,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#667eea',
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
    marginTop: 4,
  },
  calendarCard: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  calendarContainer: {
    marginBottom: 20,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  calendarNavButton: {
    padding: 8,
  },
  calendarTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  calendarDaysHeader: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  calendarDaysHeaderText: {
    flex: 1,
    textAlign: 'center',
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255,255,255,0.6)',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  calendarDay: {
    width: '14.28%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  calendarDayText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
  },
  calendarDayWithWorkout: {
    backgroundColor: 'rgba(102,126,234,0.2)',
    borderColor: '#667eea',
  },
  calendarDayTextWithWorkout: {
    color: '#fff',
    fontFamily: 'Poppins_600SemiBold',
  },
  calendarDayStreak: {
    backgroundColor: 'rgba(76,175,80,0.2)',
    borderColor: '#4CAF50',
  },
  calendarDayToday: {
    backgroundColor: 'rgba(255,193,7,0.2)',
    borderColor: '#FFC107',
  },
  calendarDayTextToday: {
    color: '#FFC107',
    fontFamily: 'Poppins_700Bold',
  },
  workoutIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#1a1a2e',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxWidth: 400,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  workoutDetails: {
    marginBottom: 20,
  },
  workoutDate: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#667eea',
    marginBottom: 10,
  },
  workoutType: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 8,
  },
  workoutDuration: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 4,
  },
  workoutCalories: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 4,
  },
  workoutNotes: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.6)',
    marginTop: 8,
  },
  input: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  notesInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  addWorkoutButton: {
    backgroundColor: '#667eea',
    borderRadius: 12,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  addWorkoutButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
  },
}); 