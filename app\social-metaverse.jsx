// This file is used in the project. Do NOT delete.
import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  Alert,
  Animated,
  SafeAreaView,
  Modal,
  TextInput,
  FlatList,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import {
  useFonts,
  Poppins_700Bold,
  Poppins_400Regular,
  Poppins_600SemiBold,
} from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SafeImage from '../components/SafeImage';

const { width, height } = Dimensions.get('window');

import { getImageByCategory } from '../constants/remoteImages';

const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

// Utility function to get reliable image sources
const getReliableImageSource = (imageKey) => {
  const url = getImageByCategory(imageKey);
  return { uri: url || getImageByCategory('default') };
};

const GlowCircle = ({ size, color, position, animatedValue }) => {
    const scale = animatedValue.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: [1, 1.2, 1],
    });
  
    const opacity = animatedValue.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: [0.5, 1, 0.5],
    });
  
    return (
      <Animated.View
        style={[
          styles.glowCircle,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            backgroundColor: color,
            ...position,
            transform: [{ scale }],
            opacity,
          },
        ]}
      />
    );
  };
  
  const Particle = ({ animatedValue }) => {
    const screenHeight = Dimensions.get('window').height;
    const screenWidth = Dimensions.get('window').width;
  
    const top = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [Math.random() * screenHeight, Math.random() * screenHeight - 200],
    });
  
    const left = useMemo(() => Math.random() * screenWidth, []);
    const opacity = useMemo(() => Math.random() * 0.5 + 0.2, []);
    const size = useMemo(() => Math.random() * 3 + 1, []);
  
    return (
      <Animated.View
        style={[
          styles.particle,
          {
            top,
            left,
            opacity,
            width: size,
            height: size,
          },
        ]}
      />
    );
  };

export default function SocialMetaverse() {
  // Always call useFonts first to maintain hook order
  const [fontsLoaded] = useFonts({
    Poppins_700Bold,
    Poppins_400Regular,
    Poppins_600SemiBold,
  });

  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [communities, setCommunities] = useState([]);
  const [showCreatePost, setShowCreatePost] = useState(false);
  const [showFindFriends, setShowFindFriends] = useState(false);
  const [showChallenges, setShowChallenges] = useState(false);
  const [showEvents, setShowEvents] = useState(false);
  const [newPost, setNewPost] = useState('');
  const [userStats, setUserStats] = useState({
    followers: 1247,
    following: 892,
    posts: 156,
    achievements: 23,
  });

  const animatedValue = useMemo(() => new Animated.Value(0), []);
  const particleAnimation = useMemo(() => new Animated.Value(0), []);

  useEffect(() => {
    Animated.loop(
        Animated.sequence([
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 4000,
            useNativeDriver: false,
          }),
          Animated.timing(animatedValue, {
            toValue: 0,
            duration: 4000,
            useNativeDriver: false,
          }),
        ]),
      ).start();
  
      Animated.loop(
        Animated.timing(particleAnimation, {
          toValue: 1,
          duration: 60000,
          useNativeDriver: false,
        }),
      ).start();

    // Load communities with robust image handling
    const mockCommunities = [
      {
        id: 1,
        name: 'Fitness Warriors',
        members: 15420,
        category: 'strength',
        image: { uri: getReliableImageSource('strength') },
        description: 'Dedicated to strength training and muscle building',
        isJoined: true,
        logoText: '💪',
      },
      {
        id: 2,
        name: 'Cardio Kings',
        members: 8920,
        category: 'cardio',
        image: { uri: getReliableImageSource('cardio') },
        description: 'Running, cycling, and endurance training',
        isJoined: false,
        logoText: '🏃',
      },
      {
        id: 3,
        name: 'Yoga Masters',
        members: 12340,
        category: 'wellness',
        image: { uri: getReliableImageSource('wellness') },
        description: 'Mindfulness and flexibility training',
        isJoined: true,
        logoText: '🧘',
      },
      {
        id: 4,
        name: 'CrossFit Elite',
        members: 5670,
        category: 'strength',
        image: getReliableImageSource('strength'),
        description: 'High-intensity functional movements',
        isJoined: false,
        logoText: '🏋️',
      },
      {
        id: 5,
        name: 'Nutrition Experts',
        members: 7890,
        category: 'wellness',
        image: getReliableImageSource('wellness'),
        description: 'Healthy eating and nutrition guidance',
        isJoined: true,
        logoText: '🥗',
      },
      {
        id: 6,
        name: 'Running Club',
        members: 11230,
        category: 'cardio',
        image: getReliableImageSource('cardio'),
        description: 'Marathon training and running tips',
        isJoined: false,
        logoText: '🏃‍♂️',
      },
    ];
    setCommunities(mockCommunities);
  }, []);

  const categories = [
    { id: 'all', name: 'All', icon: 'people' },
    { id: 'strength', name: 'Strength', icon: 'barbell' },
    { id: 'cardio', name: 'Cardio', icon: 'fitness' },
    { id: 'wellness', name: 'Wellness', icon: 'leaf' },
  ];

  const filteredCommunities = selectedCategory === 'all' 
    ? communities 
    : communities.filter(community => community.category === selectedCategory);

  const particles = useMemo(() => Array.from({ length: 100 }).map((_, i) => <Particle key={i} animatedValue={particleAnimation} />), [particleAnimation]);

  // Quick Action Handlers
  const handleCreatePost = () => {
    setShowCreatePost(true);
  };

  const handleFindFriends = () => {
    setShowFindFriends(true);
  };

  const handleChallenges = () => {
    setShowChallenges(true);
  };

  const handleEvents = () => {
    setShowEvents(true);
  };

  const handleJoinCommunity = (community) => {
    const updatedCommunities = communities.map(c => 
      c.id === community.id ? { ...c, isJoined: !c.isJoined } : c
    );
    setCommunities(updatedCommunities);
    
    if (community.isJoined) {
      Alert.alert('Left Community', `You left ${community.name}`);
    } else {
      Alert.alert('Joined Community', `Welcome to ${community.name}!`);
    }
  };

  const handleSubmitPost = () => {
    if (newPost.trim()) {
      Alert.alert('Post Created', 'Your fitness post has been shared!');
      setNewPost('');
      setShowCreatePost(false);
    } else {
      Alert.alert('Error', 'Please enter some content for your post.');
    }
  };

  if (!fontsLoaded) return null;

  return (
    <SafeAreaView style={styles.container}>
       <AnimatedLinearGradient
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
        style={StyleSheet.absoluteFill}
      />
        {particles}
        <GlowCircle
        size={400}
        color="#6a0dad"
        position={{ top: -150, left: -150 }}
        animatedValue={animatedValue}
      />
      <GlowCircle
        size={300}
        color="#0077ff"
        position={{ bottom: -100, right: -100 }}
        animatedValue={animatedValue}
      />
       <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Social Metaverse</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* User Stats */}
        <View style={styles.statsContainer}>
            <View style={styles.statsCard}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{userStats.followers.toLocaleString()}</Text>
              <Text style={styles.statLabel}>Followers</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{userStats.following.toLocaleString()}</Text>
              <Text style={styles.statLabel}>Following</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{userStats.posts}</Text>
              <Text style={styles.statLabel}>Posts</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{userStats.achievements}</Text>
              <Text style={styles.statLabel}>Achievements</Text>
            </View>
          </View>
        </View>

        {/* Category Filter */}
        <View style={styles.categoryContainer}>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoryScroll}
          >
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryButton,
                  selectedCategory === category.id && styles.categoryButtonActive
                ]}
                onPress={() => setSelectedCategory(category.id)}
              >
                
                  <Ionicons 
                    name={category.icon} 
                    size={20} 
                    color={selectedCategory === category.id ? '#fff' : '#667eea'} 
                  />
                  <Text style={[
                    styles.categoryText,
                    selectedCategory === category.id && styles.categoryTextActive
                  ]}>
                    {category.name}
                  </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <View style={styles.actionsGrid}>
            <TouchableOpacity
              style={styles.actionCard}
              onPress={handleCreatePost}
            >
              <LinearGradient
                colors={['#667eea', '#764ba2']}
                style={styles.actionGradient}
              >
                <Ionicons name="add-circle-outline" size={32} color="#fff" />
                <Text style={styles.actionText}>Create Post</Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={handleFindFriends}
            >
              <LinearGradient
                colors={['#f093fb', '#f5576c']}
                style={styles.actionGradient}
              >
                <Ionicons name="people-outline" size={32} color="#fff" />
                <Text style={styles.actionText}>Find Friends</Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={handleChallenges}
            >
              <LinearGradient
                colors={['#4CAF50', '#45a049']}
                style={styles.actionGradient}
              >
                <Ionicons name="trophy-outline" size={32} color="#fff" />
                <Text style={styles.actionText}>Challenges</Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={handleEvents}
            >
              <LinearGradient
                colors={['#FF9800', '#F57C00']}
                style={styles.actionGradient}
              >
                <Ionicons name="calendar-outline" size={32} color="#fff" />
                <Text style={styles.actionText}>Events</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>

        {/* Communities */}
        <View style={styles.communitiesContainer}>
          <Text style={styles.sectionTitle}>Fitness Communities</Text>
          
          {filteredCommunities.map((community) => (
            <TouchableOpacity
              key={community.id}
              style={styles.communityCard}
              onPress={() => Alert.alert(community.name, community.description)}
            >
              <LinearGradient
                colors={['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']}
                style={styles.communityGradient}
              >
                <View style={styles.communityHeader}>
                  <View style={styles.communityImageContainer}>
                    <SafeImage 
                      source={community.image} 
                      style={styles.communityImage}
                    />
                    <Text style={styles.communityLogoText}>{community.logoText}</Text>
                  </View>
                  <View style={styles.communityInfo}>
                    <Text style={styles.communityName}>{community.name}</Text>
                    <Text style={styles.communityMembers}>
                      {community.members.toLocaleString()} members
                    </Text>
                    <Text style={styles.communityDescription}>
                      {community.description}
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={[
                      styles.joinButton,
                      community.isJoined && styles.joinedButton
                    ]}
                    onPress={() => handleJoinCommunity(community)}
                  >
                    <LinearGradient
                      colors={community.isJoined ? ['#4CAF50', '#45a049'] : ['#667eea', '#764ba2']}
                      style={styles.joinButtonGradient}
                    >
                      <Text style={styles.joinButtonText}>
                        {community.isJoined ? 'Joined' : 'Join'}
                      </Text>
                    </LinearGradient>
                  </TouchableOpacity>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {/* Create Post Modal */}
      <Modal
        visible={showCreatePost}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCreatePost(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Create Post</Text>
              <TouchableOpacity onPress={() => setShowCreatePost(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.postInput}
              placeholder="Share your fitness journey..."
              placeholderTextColor="#ccc"
              multiline
              value={newPost}
              onChangeText={setNewPost}
            />
            <TouchableOpacity style={styles.submitButton} onPress={handleSubmitPost}>
              <Text style={styles.submitButtonText}>Share Post</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Find Friends Modal */}
      <Modal
        visible={showFindFriends}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowFindFriends(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Find Friends</Text>
              <TouchableOpacity onPress={() => setShowFindFriends(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            <Text style={styles.modalContent}>
              Discover fitness friends near you and around the world!
            </Text>
            <TouchableOpacity style={styles.submitButton} onPress={() => setShowFindFriends(false)}>
              <Text style={styles.submitButtonText}>Explore</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Challenges Modal */}
      <Modal
        visible={showChallenges}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowChallenges(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Fitness Challenges</Text>
              <TouchableOpacity onPress={() => setShowChallenges(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            <Text style={styles.modalContent}>
              Join exciting fitness challenges and compete with friends!
            </Text>
            <TouchableOpacity style={styles.submitButton} onPress={() => setShowChallenges(false)}>
              <Text style={styles.submitButtonText}>View Challenges</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Events Modal */}
      <Modal
        visible={showEvents}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowEvents(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Fitness Events</Text>
              <TouchableOpacity onPress={() => setShowEvents(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            <Text style={styles.modalContent}>
              Browse upcoming fitness events and workshops!
            </Text>
            <TouchableOpacity style={styles.submitButton} onPress={() => setShowEvents(false)}>
              <Text style={styles.submitButtonText}>Browse Events</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f0f2f5',
      },
      header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: wp('4%'),
        paddingVertical: hp('2%'),
        paddingTop: hp('5%'),
        backgroundColor: 'transparent',
      },
  backButton: {
    padding: 10,
    marginLeft:-10
  },
  headerTitle: {
    color: '#fff',
    fontSize: wp('5%'),
    fontFamily: 'Poppins_700Bold',
  },
  headerRight: {
    width: 24, // to balance the back button
  },
  content: {
    flex: 1,
  },
  statsContainer: {
    paddingHorizontal: wp('4%'),
    marginTop: hp('2%'),
    marginBottom: hp('1%'),
    
  },
  statsCard: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    borderRadius: 20,
    paddingVertical: hp('2.5%'),
    paddingHorizontal: wp('2%'),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: wp('5%'),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  statLabel: {
    fontSize: wp('3.5%'),
    fontFamily: 'Poppins_400Regular',
    color: '#ddd',
    marginTop: 5,
  },
  statDivider: {
    width: 1,
    height: '60%',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  categoryContainer: {
    marginTop: hp('2%'),
    marginBottom: hp('3%'),
    paddingLeft: wp('4%'),
  },
  categoryScroll: {
    paddingRight: wp('4%'),
  },
  categoryButton: {
    marginRight: 15,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth:1,
    borderColor:'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryButtonActive: {
    backgroundColor: '#667eea',
  },
  categoryGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  categoryText: {
    marginLeft: 10,
    fontFamily: 'Poppins_600SemiBold',
    fontSize: wp('3.5%'),
    color: '#fff',
  },
  categoryTextActive: {
    color: '#fff',
  },
  communitiesContainer: {
    paddingHorizontal: wp('4%'),
  },
  sectionTitle: {
    fontSize: wp('5%'),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp('2%'),
  },
  communityCard: {
    borderRadius: 20,
    marginBottom: hp('2%'),
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  communityGradient: {
    padding: wp('4%'),
  },
  communityHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  communityImageContainer: {
    position: 'relative',
    marginRight: 15,
  },
  communityImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: '#667eea',
  },
  communityLogoText: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    fontSize: 20,
    color: '#fff',
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 10,
    padding: 2,
  },
  communityInfo: {
    flex: 1,
  },
  communityName: {
    fontSize: wp('4.5%'),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  communityMembers: {
    fontSize: wp('3.5%'),
    fontFamily: 'Poppins_400Regular',
    color: '#ddd',
    marginTop: 2,
  },
  communityDescription: {
    fontSize: wp('3.5%'),
    fontFamily: 'Poppins_400Regular',
    color: '#ccc',
    marginTop: 8,
  },
  joinButton: {
    borderRadius: 20,
    alignSelf: 'flex-start',
    marginLeft:10
  },
  joinedButton: {
    backgroundColor: '#4CAF50',
  },
  joinButtonGradient: {
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 20,
  },
  joinButtonText: {
    color: '#fff',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: wp('3.5%'),
  },
  actionsContainer: {
    paddingHorizontal: wp('4%'),
    marginTop: hp('2%'),
    marginBottom: hp('4%'),
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: '48%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: wp('4%'),
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: hp('2%'),
    overflow: 'hidden',
  },
  actionGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    padding: wp('4%'),
  },
  actionIcon: {
    marginBottom: 10,
  },
  actionText: {
    color: '#fff',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: wp('4%'),
    textAlign: 'center',
    marginTop: 8,
  },
  glowCircle: {
    position: 'absolute',
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: wp('80%'),
    backgroundColor: '#1a1a2e',
    borderRadius: 20,
    padding: wp('5%'),
    alignItems: 'center',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: hp('2%'),
  },
  modalTitle: {
    fontSize: wp('5%'),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  modalContent: {
    fontSize: wp('4%'),
    fontFamily: 'Poppins_400Regular',
    color: '#ccc',
    textAlign: 'center',
    marginBottom: hp('3%'),
  },
  postInput: {
    width: '100%',
    height: hp('20%'),
    backgroundColor: '#2a2a40',
    borderRadius: 15,
    padding: wp('4%'),
    color: '#fff',
    fontSize: wp('4%'),
    fontFamily: 'Poppins_400Regular',
    textAlignVertical: 'top',
    marginBottom: hp('2%'),
  },
  submitButton: {
    width: '100%',
    backgroundColor: '#667eea',
    borderRadius: 20,
    paddingVertical: hp('2%'),
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: wp('4.5%'),
  },
});