// This file is used in the project (imported by app/ai-workout-generator.jsx and ai-coach.jsx). Do NOT delete.
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Linking,
  Dimensions,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInLeft } from 'react-native-reanimated';
import YoutubePlayer from 'react-native-youtube-iframe';
import { handleVideoTutorial, isVideoTutorialEnabled } from '../constants/data';
import { useFonts, Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';

const { width } = Dimensions.get('window');

export default function AIWorkoutGenerator({ onClose, onWorkoutGenerated, userProfile }) {
  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });
  
  // Selection states
  const [selectedGoal, setSelectedGoal] = useState('');
  const [selectedDuration, setSelectedDuration] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('');
  
  // Workout states
  const [generatedWorkout, setGeneratedWorkout] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  
  // Flow control states
  const [currentScreen, setCurrentScreen] = useState('selection'); // 'selection', 'generated', 'summary', 'exercise', 'progress', 'completed', 'video'
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [exerciseStartTime, setExerciseStartTime] = useState(null);
  const [workoutProgress, setWorkoutProgress] = useState([]);
  const [isExerciseActive, setIsExerciseActive] = useState(false);

  // Video player states
  const [currentVideoUrl, setCurrentVideoUrl] = useState('');
  const [currentExerciseName, setCurrentExerciseName] = useState('');
  const [currentExerciseData, setCurrentExerciseData] = useState(null);
  const [videoPlayerReady, setVideoPlayerReady] = useState(false);
  const [previousScreen, setPreviousScreen] = useState('generated');

  // Helper function to extract YouTube video ID from URL
  const getYouTubeVideoId = (url) => {
    if (!url) return '';
    
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    
    if (match && match[2].length === 11) {
      return match[2];
    }
    
    return '';
  };
  
  // Import demo exercises from constants
  const { demoExercises } = require('../constants/data');
  
  // Ensure demoExercises is available
  useEffect(() => {
    if (!demoExercises || demoExercises.length === 0) {
      console.error('Warning: Demo exercises data is not available');
    } else {
      console.log(`Loaded ${demoExercises.length} exercise options`);
    }
  }, []);
  
  // Function to generate a workout based on user selections
  const generateWorkout = () => {
    // Validate all selections are made
    if (!selectedGoal || !selectedDuration || !selectedLevel) {
      const missing = [];
      if (!selectedGoal) missing.push('Goal');
      if (!selectedDuration) missing.push('Duration');
      if (!selectedLevel) missing.push('Fitness Level');
      
      Alert.alert(
        'Missing Selections',
        `Please select the following: ${missing.join(', ')}`,
        [{ text: 'OK', style: 'default' }]
      );
      return;
    }
    
    // Set generating state
    setIsGenerating(true);
    
    // Log the user's selections
    console.log('Generating workout with:', {
      goal: selectedGoal,
      duration: selectedDuration,
      level: selectedLevel
    });
    
    // Simulate API call delay
    setTimeout(() => {
      try {
        // Filter exercises based on user's level
        let difficultyMultiplier = 1;
        switch(selectedLevel) {
          case 'beginner':
            difficultyMultiplier = 1;
            break;
          case 'intermediate':
            difficultyMultiplier = 1.5;
            break;
          case 'advanced':
            difficultyMultiplier = 2;
            break;
          default:
            difficultyMultiplier = 1;
        }
        
        // Determine number of exercises based on duration
        const durationInMinutes = parseInt(selectedDuration);
        const exercisesCount = Math.max(Math.floor(durationInMinutes / 5), 3); // At least 3 exercises
        
        // Filter exercises based on goal
        let targetMuscles = [];
        switch(selectedGoal) {
          case 'weight_loss':
            targetMuscles = ['cardio', 'fullbody', 'legs', 'core'];
            break;
          case 'muscle_gain':
            targetMuscles = ['chest', 'back', 'shoulder', 'lowerarms', 'lowerlegs'];
            break;
          case 'endurance':
            targetMuscles = ['cardio', 'legs', 'fullbody'];
            break;
          case 'flexibility':
            targetMuscles = ['lowerlegs', 'back', 'shoulder'];
            break;
          case 'general':
            targetMuscles = ['fullbody', 'core', 'chest', 'back', 'legs', 'cardio'];
            break;
          default:
            targetMuscles = ['fullbody', 'core', 'chest', 'back', 'legs'];
        }
        
        // Ensure we have demo exercises
        if (!demoExercises || demoExercises.length === 0) {
          throw new Error('No exercise data available');
        }
        
        // Filter exercises that match the target muscles
        let filteredExercises = demoExercises.filter(exercise => 
          exercise.bodyPart && targetMuscles.includes(exercise.bodyPart)
        );
        
        // If not enough exercises, use all available exercises
        if (filteredExercises.length < exercisesCount) {
          filteredExercises = demoExercises;
        }
        
        // Shuffle the exercises to get random selection
        const shuffledExercises = [...filteredExercises].sort(() => 0.5 - Math.random());
        
        // Select the required number of exercises
        const selectedExercises = shuffledExercises.slice(0, exercisesCount);
        
        // Calculate sets and reps based on level
        const exercises = selectedExercises.map(exercise => {
          const sets = Math.floor(3 * difficultyMultiplier);
          const reps = Math.floor(10 * difficultyMultiplier);
          const restTime = Math.floor(60 / difficultyMultiplier);
          
          return {
            ...exercise,
            sets,
            reps,
            restTime,
            completed: false
          };
        });
        
        // Create the workout object
        const workout = {
          id: `workout-${Date.now()}`,
          title: `${selectedGoal.replace('_', ' ').charAt(0).toUpperCase() + selectedGoal.replace('_', ' ').slice(1)} Workout`,
          goal: selectedGoal,
          level: selectedLevel,
          duration: selectedDuration,
          exercises,
          createdAt: new Date().toISOString()
        };
        
        console.log('Workout generated successfully:', workout.title);
        
        // Update state with the generated workout
        setGeneratedWorkout(workout);
        setCurrentScreen('generated');
        
        // Call the onWorkoutGenerated callback if provided
        if (onWorkoutGenerated) {
          onWorkoutGenerated(workout);
        }
        
      } catch (error) {
        console.error('Error generating workout:', error);
        Alert.alert('Error', 'Failed to generate workout. Please try again.');
      } finally {
        setIsGenerating(false);
      }
    }, 1500); // 1.5 second delay to simulate processing
  };

  const fitnessGoals = [
    { id: 'weight_loss', title: '🔥 Weight Loss', description: 'Burn calories and lose weight' },
    { id: 'muscle_gain', title: '💪 Muscle Gain', description: 'Build strength and muscle mass' },
    { id: 'endurance', title: '🏃 Endurance', description: 'Improve cardiovascular fitness' },
    { id: 'flexibility', title: '🤸 Flexibility', description: 'Increase mobility and flexibility' },
    { id: 'general', title: '⚡ General Fitness', description: 'Overall health and wellness' },
  ];

  const durations = [
    { id: '15', title: '15 min', description: 'Quick workout' },
    { id: '30', title: '30 min', description: 'Standard session' },
    { id: '45', title: '45 min', description: 'Extended workout' },
    { id: '60', title: '60 min', description: 'Full session' },
  ];

  const levels = [
    { id: 'beginner', title: '🟢 Beginner', description: 'New to fitness' },
    { id: 'intermediate', title: '🟡 Intermediate', description: 'Some experience' },
    { id: 'advanced', title: '🔴 Advanced', description: 'Experienced athlete' },
  ];

  // Show font loading state
  if (!fontsLoaded) {
    return (
      <View style={styles.container}>
        <LinearGradient 
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} 
          style={styles.backgroundGradient}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Background Gradient */}
      <LinearGradient
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Animated Background Elements */}
        <View style={styles.circle1} />
        <View style={styles.circle2} />
        <View style={styles.circle3} />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onClose}>
            <Ionicons name="close" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>AI Workout Generator</Text>
          <View style={styles.headerRight} />
        </View>

        {/* Content */}
        <ScrollView 
          style={styles.scrollView} 
          contentContainerStyle={styles.content}
          showsVerticalScrollIndicator={false}
        >
          {currentScreen === 'selection' && (
            <View style={styles.selectionContainer}>
              <Text style={styles.sectionTitle}>🎯 Select Your Goal</Text>
              <View style={styles.goalsContainer}>
                {fitnessGoals.map((goal) => (
                  <TouchableOpacity
                    key={goal.id}
                    style={[
                      styles.goalCard,
                      selectedGoal === goal.id && styles.selectedGoalCard
                    ]}
                    onPress={() => setSelectedGoal(goal.id)}
                  >
                    <LinearGradient
                      colors={selectedGoal === goal.id 
                        ? ['rgba(102, 126, 234, 0.2)', 'rgba(118, 75, 162, 0.2)'] 
                        : ['rgba(255, 255, 255, 0.05)', 'rgba(255, 255, 255, 0.02)']
                      }
                      style={styles.goalGradient}
                    >
                      <Text style={styles.goalTitle}>{goal.title}</Text>
                      <Text style={styles.goalDescription}>{goal.description}</Text>
                    </LinearGradient>
                  </TouchableOpacity>
                ))}
              </View>

              <Text style={styles.sectionTitle}>⏱️ Workout Duration</Text>
              <View style={styles.durationContainer}>
                {durations.map((duration) => (
                  <TouchableOpacity
                    key={duration.id}
                    style={[
                      styles.durationCard,
                      selectedDuration === duration.id && styles.selectedDurationCard
                    ]}
                    onPress={() => setSelectedDuration(duration.id)}
                  >
                    <LinearGradient
                      colors={selectedDuration === duration.id 
                        ? ['rgba(102, 126, 234, 0.2)', 'rgba(118, 75, 162, 0.2)'] 
                        : ['rgba(255, 255, 255, 0.05)', 'rgba(255, 255, 255, 0.02)']
                      }
                      style={styles.durationGradient}
                    >
                      <Text style={styles.durationTitle}>{duration.title}</Text>
                      <Text style={styles.durationDescription}>{duration.description}</Text>
                    </LinearGradient>
                  </TouchableOpacity>
                ))}
              </View>

              <Text style={styles.sectionTitle}>📊 Fitness Level</Text>
              <View style={styles.levelContainer}>
                {levels.map((level) => (
                  <TouchableOpacity
                    key={level.id}
                    style={[
                      styles.optionCard,
                      selectedLevel === level.id && styles.selectedCard
                    ]}
                    onPress={() => {
                      console.log('💪 Level selected:', level.id);
                      setSelectedLevel(level.id);
                    }}
                    activeOpacity={0.8}
                  >
                    <Text style={styles.optionTitle}>{level.title}</Text>
                    <Text style={styles.optionDescription}>{level.description}</Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Generate Button */}
              <TouchableOpacity 
                style={[
                  styles.generateButton, 
                  isGenerating && styles.generatingButton,
                  (!selectedGoal || !selectedDuration || !selectedLevel) && styles.disabledButton
                ]}
                onPress={() => {
                  console.log('🎯 Generate button touched!');
                  if (!isGenerating) {
                    generateWorkout();
                  }
                }}
                disabled={isGenerating}
                activeOpacity={isGenerating ? 1 : 0.8}
              >
                <LinearGradient 
                  colors={['#667eea', '#764ba2']}
                  style={styles.generateButtonGradient}
                >
                  <Text style={styles.generateButtonText}>
                    {isGenerating ? '🤖 Generating AI Workout...' : 
                     (!selectedGoal || !selectedDuration || !selectedLevel) ? 
                     `⚠️ Select ${!selectedGoal ? 'Goal' : !selectedDuration ? 'Duration' : 'Level'}` : 
                     '✨ Generate AI Workout'}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          )}
          
          {/* Generated Workout Screen */}
          {currentScreen === 'generated' && generatedWorkout && (
            <Animated.View 
              style={styles.generatedContainer}
              entering={FadeInLeft.duration(500)}
            >
              <View style={styles.workoutHeader}>
                <Text style={styles.workoutTitle}>{generatedWorkout.title}</Text>
                <View style={styles.workoutMetaContainer}>
                  <View style={styles.workoutMetaItem}>
                    <Ionicons name="time-outline" size={20} color="#fff" />
                    <Text style={styles.workoutMetaText}>{generatedWorkout.duration} min</Text>
                  </View>
                  <View style={styles.workoutMetaItem}>
                    <Ionicons name="fitness-outline" size={20} color="#fff" />
                    <Text style={styles.workoutMetaText}>
                      {generatedWorkout.level.charAt(0).toUpperCase() + generatedWorkout.level.slice(1)}
                    </Text>
                  </View>
                  <View style={styles.workoutMetaItem}>
                    <Ionicons name="flame-outline" size={20} color="#fff" />
                    <Text style={styles.workoutMetaText}>
                      {generatedWorkout.exercises.length} exercises
                    </Text>
                  </View>
                </View>
              </View>
              
              <Text style={styles.sectionTitle}>Your Workout Plan</Text>
              
              {generatedWorkout.exercises.map((exercise, index) => (
                <TouchableOpacity 
                  key={exercise.id || index}
                  style={styles.exerciseCard}
                  onPress={() => {
                    setCurrentExerciseData(exercise);
                    setCurrentExerciseIndex(index);
                    setCurrentVideoUrl(exercise.videoUrl || '');
                    setCurrentExerciseName(exercise.name);
                    setPreviousScreen('generated');
                    setCurrentScreen('video');
                  }}
                >
                  <View style={styles.exerciseCardContent}>
                    <View style={styles.exerciseImageContainer}>
                      {exercise.imageUrl ? (
                        <View style={styles.exerciseImageWrapper}>
                          <LinearGradient
                            colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
                            style={StyleSheet.absoluteFill}
                          />
                          <Text style={styles.exerciseNumber}>{index + 1}</Text>
                        </View>
                      ) : (
                        <View style={styles.exerciseImagePlaceholder}>
                          <Text style={styles.exerciseNumber}>{index + 1}</Text>
                        </View>
                      )}
                    </View>
                    <View style={styles.exerciseInfo}>
                      <Text style={styles.exerciseName}>{exercise.name}</Text>
                      <Text style={styles.exerciseTarget}>{exercise.target}</Text>
                      <View style={styles.exerciseDetails}>
                        <Text style={styles.exerciseDetail}>
                          <Text style={styles.exerciseDetailBold}>{exercise.sets}</Text> sets
                        </Text>
                        <Text style={styles.exerciseDetail}>
                          <Text style={styles.exerciseDetailBold}>{exercise.reps}</Text> reps
                        </Text>
                        <Text style={styles.exerciseDetail}>
                          <Text style={styles.exerciseDetailBold}>{exercise.restTime}</Text>s rest
                        </Text>
                      </View>
                    </View>
                    <View style={styles.exerciseActions}>
                      <Ionicons name="play-circle-outline" size={28} color="#667eea" />
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
              
              <View style={styles.workoutActions}>
                <TouchableOpacity 
                  style={styles.startWorkoutButton}
                  onPress={() => {
                    // Start the workout
                    setCurrentExerciseIndex(0);
                    setCurrentScreen('exercise');
                    setWorkoutProgress([]);
                  }}
                >
                  <LinearGradient 
                    colors={['#667eea', '#764ba2']}
                    style={styles.startWorkoutButtonGradient}
                  >
                    <Text style={styles.startWorkoutButtonText}>Start Workout</Text>
                  </LinearGradient>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.regenerateButton}
                  onPress={() => {
                    setCurrentScreen('selection');
                  }}
                >
                  <Text style={styles.regenerateButtonText}>Generate Another</Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          )}
          
          {/* Video Tutorial Screen */}
          {currentScreen === 'video' && currentVideoUrl && (
            <View style={styles.videoContainer}>
              <View style={styles.videoHeader}>
                <TouchableOpacity 
                  style={styles.videoBackButton}
                  onPress={() => {
                    setCurrentScreen(previousScreen);
                  }}
                >
                  <Ionicons name="arrow-back" size={24} color="#fff" />
                </TouchableOpacity>
                <Text style={styles.videoTitle}>{currentExerciseName}</Text>
                <View style={styles.videoHeaderRight} />
              </View>
              
              <View style={styles.videoPlayerContainer}>
                <YoutubePlayer
                  height={width * 0.6}
                  play={false}
                  videoId={getYouTubeVideoId(currentVideoUrl)}
                  onReady={() => setVideoPlayerReady(true)}
                />
              </View>
              
              {currentExerciseData && (
                <View style={styles.exerciseInstructions}>
                  <Text style={styles.instructionsTitle}>Instructions</Text>
                  {currentExerciseData.instructions.map((instruction, index) => (
                    <View key={index} style={styles.instructionItem}>
                      <View style={styles.instructionNumber}>
                        <Text style={styles.instructionNumberText}>{index + 1}</Text>
                      </View>
                      <Text style={styles.instructionText}>{instruction}</Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          )}
        </ScrollView>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  backgroundGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  circle1: {
    position: 'absolute',
    width: 300,
    height: 300,
    borderRadius: 150,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    top: -50,
    right: -50,
  },
  circle2: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    bottom: 100,
    left: -50,
  },
  circle3: {
    position: 'absolute',
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    bottom: -50,
    right: 50,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: wp(6),
    paddingTop: Platform.OS === 'ios' ? hp(6) : hp(4),
    paddingBottom: hp(2),
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    textAlign: 'center',
  },
  headerRight: {
    width: 44,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: wp(6),
    paddingBottom: hp(4),
  },
  selectionContainer: {
    marginTop: hp(1),
  },
  sectionTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(1.5),
  },
  goalsContainer: {
    marginBottom: hp(2.5),
  },
  goalCard: {
    borderRadius: 12,
    marginBottom: hp(1),
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  selectedGoalCard: {
    borderColor: '#667eea',
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
  },
  goalGradient: {
    padding: wp(4),
  },
  goalTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginBottom: hp(0.5),
  },
  goalDescription: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  durationContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: hp(2.5),
  },
  durationCard: {
    width: '48%',
    borderRadius: 12,
    marginBottom: hp(1),
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  selectedDurationCard: {
    borderColor: '#667eea',
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
  },
  durationGradient: {
    padding: wp(4),
  },
  durationTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginBottom: hp(0.5),
  },
  durationDescription: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  levelContainer: {
    marginBottom: hp(3),
  },
  optionCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: wp(4),
    marginBottom: hp(1),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  selectedCard: {
    borderColor: '#667eea',
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
  },
  generateButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginTop: hp(1),
  },
  generatingButton: {
    opacity: 0.8,
  },
  disabledButton: {
    opacity: 0.6,
  },
  generateButtonGradient: {
    paddingVertical: hp(2),
    alignItems: 'center',
    justifyContent: 'center',
  },
  generateButtonText: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#ffffff',
    fontFamily: 'Poppins_400Regular',
  },
  // Generated workout styles
  generatedContainer: {
    marginTop: hp(1),
  },
  workoutHeader: {
    marginBottom: hp(2),
  },
  workoutTitle: {
    fontSize: wp(6),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(1),
  },
  workoutMetaContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  workoutMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: wp(4),
    marginBottom: hp(1),
  },
  workoutMetaText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    marginLeft: wp(1),
  },
  exerciseCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    marginBottom: hp(1.5),
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  exerciseCardContent: {
    flexDirection: 'row',
    padding: wp(3),
  },
  exerciseImageContainer: {
    width: wp(15),
    height: wp(15),
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: wp(3),
  },
  exerciseImageWrapper: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  exerciseImagePlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  exerciseNumber: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  exerciseInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  exerciseName: {
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginBottom: hp(0.2),
  },
  exerciseTarget: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: hp(0.5),
  },
  exerciseDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  exerciseDetail: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.6)',
    marginRight: wp(3),
  },
  exerciseDetailBold: {
    fontFamily: 'Poppins_600SemiBold',
    color: '#667eea',
  },
  exerciseActions: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: wp(2),
  },
  workoutActions: {
    marginTop: hp(2),
    marginBottom: hp(4),
  },
  startWorkoutButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: hp(1.5),
  },
  startWorkoutButtonGradient: {
    paddingVertical: hp(2),
    alignItems: 'center',
    justifyContent: 'center',
  },
  startWorkoutButtonText: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  regenerateButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(1.5),
  },
  regenerateButtonText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    textDecorationLine: 'underline',
  },
  // Video tutorial styles
  videoContainer: {
    flex: 1,
  },
  videoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: hp(2),
  },
  videoBackButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  videoTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    flex: 1,
    textAlign: 'center',
  },
  videoHeaderRight: {
    width: 44,
  },
  videoPlayerContainer: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: hp(2),
  },
  exerciseInstructions: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: wp(4),
    marginBottom: hp(2),
  },
  instructionsTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginBottom: hp(1.5),
  },
  instructionItem: {
    flexDirection: 'row',
    marginBottom: hp(1),
  },
  instructionNumber: {
    width: wp(6),
    height: wp(6),
    borderRadius: wp(3),
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(3),
    marginTop: hp(0.2),
  },
  instructionNumberText: {
    fontSize: wp(3),
    fontFamily: 'Poppins_600SemiBold',
    color: '#667eea',
  },
  instructionText: {
    flex: 1,
    fontSize: wp(3.8),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: wp(5.5),
  },
  optionTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginBottom: hp(0.5),
  },
  optionDescription: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  generateButton: {
    height: hp(7),
    borderRadius: hp(3.5),
    overflow: 'hidden',
    marginBottom: hp(2),
  },
  generatingButton: {
    opacity: 0.8,
  },
  disabledButton: {
    opacity: 0.5,
  },
  generateButtonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  generateButtonText: {
    color: '#fff',
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    marginTop: hp(2),
  },
});