import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { Poppins_400Regular, Poppins_700Bold, Poppins_600SemiBold, useFonts } from '@expo-google-fonts/poppins';

const { width, height } = Dimensions.get('window');

const GradientBackButton = ({ onPress, style }) => (
  <TouchableOpacity onPress={onPress} style={[styles.backButton, style]}>
    <LinearGradient
      colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
      style={styles.backButtonGradient}
    >
      <Ionicons name="arrow-back" size={24} color="#ffffff" />
    </LinearGradient>
  </TouchableOpacity>
);

export default function ForgotPassword() {
  const router = useRouter();
  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_700Bold,
    Poppins_600SemiBold,
  });
  
  // Form state
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [nameError, setNameError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  
  // Animation values
  const backScale = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const formScale = useRef(new Animated.Value(0.9)).current;
  const buttonScale = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Main entrance animations
    Animated.stagger(200, [
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(formScale, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
    
    // Logo pulse animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const handleBackPress = () => {
    Animated.sequence([
      Animated.timing(backScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.spring(backScale, {
        toValue: 1,
        friction: 3,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start(() => {
      router.push('/user/login');
    });
  };
  
  const validateName = (name) => name.trim().length >= 2;
  const validateEmail = (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  const validatePassword = (password) => password.length >= 8;
  const validateConfirmPassword = (confirmPassword) => confirmPassword === newPassword;

  const handleUpdatePassword = async () => {
    // Reset all errors
    setNameError('');
    setEmailError('');
    setPasswordError('');
    setConfirmPasswordError('');
    setError('');
    setMessage('');
    
    let hasError = false;
    
    // Validate name
    if (!name.trim()) {
      setNameError('Name is required');
      hasError = true;
    } else if (!validateName(name)) {
      setNameError('Name must be at least 2 characters');
      hasError = true;
    }
    
    // Validate email
    if (!email.trim()) {
      setEmailError('Email is required');
      hasError = true;
    } else if (!validateEmail(email.trim())) {
      setEmailError('Please enter a valid email address');
      hasError = true;
    }
    
    // Validate new password
    if (!newPassword.trim()) {
      setPasswordError('New password is required');
      hasError = true;
    } else if (!validatePassword(newPassword.trim())) {
      setPasswordError('Password must be at least 8 characters');
      hasError = true;
    }
    
    // Validate confirm password
    if (!confirmPassword.trim()) {
      setConfirmPasswordError('Please confirm your new password');
      hasError = true;
    } else if (!validateConfirmPassword(confirmPassword.trim())) {
      setConfirmPasswordError('Passwords do not match');
      hasError = true;
    }
    
    if (hasError) return;
    
    setIsLoading(true);
    
    try {
      // Animate button press
      Animated.sequence([
        Animated.timing(buttonScale, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.spring(buttonScale, {
          toValue: 1,
          friction: 3,
          tension: 40,
          useNativeDriver: true,
        }),
      ]).start();
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Check if the user exists in AsyncStorage
      const usersData = await AsyncStorage.getItem('@FitnessApp:users');
      const users = usersData ? JSON.parse(usersData) : [];
      
      // First check if the email exists
      const userWithEmail = users.find(user => 
        user.email.toLowerCase() === email.toLowerCase().trim()
      );
      
      if (!userWithEmail) {
        setError('No account found with this email address.');
        setIsLoading(false);
        return;
      }
      
      // Then check if the name matches
      if (userWithEmail.name.toLowerCase() !== name.toLowerCase().trim()) {
        setError('The name does not match our records for this email.');
        setIsLoading(false);
        return;
      }
      
      const userIndex = users.findIndex(user => user.email === userWithEmail.email);
      
      // Update the user's password
      users[userIndex].password = newPassword.trim();
      await AsyncStorage.setItem('@FitnessApp:users', JSON.stringify(users));
      
      // Show success message
      setMessage('Password has been updated successfully!');
      
      // Clear form fields
      setNewPassword('');
      setConfirmPassword('');
      
      // Redirect to login after 2 seconds
      setTimeout(() => {
        // Add a login hint to AsyncStorage so the login page can show a message
        AsyncStorage.setItem('@FitnessApp:passwordReset', 'true');
        router.push('/user/login');
      }, 2000);
      
    } catch (error) {
      console.error('Error updating password:', error);
      setError('An error occurred. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ffffff" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      <LinearGradient
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      />
      
      {/* Animated background elements */}
      <View style={[styles.glowCircle, { top: hp(10), right: wp(10) }]} />
      <View style={[styles.glowCircle, { bottom: hp(20), left: wp(5) }]} />
      
      <Animated.View style={[styles.backButtonContainer, { transform: [{ scale: backScale }] }]}>
        <GradientBackButton onPress={handleBackPress} />
      </Animated.View>
      
      <Animated.View 
        style={[
          styles.content, 
          { 
            opacity: fadeAnim, 
            transform: [
              { translateY: slideAnim }, 
              { scale: formScale }
            ] 
          }
        ]}
      >
        <View style={styles.header}>
          <Animated.View style={[styles.logoContainer, { transform: [{ scale: pulseAnim }] }]}>
            <LinearGradient
              colors={['rgba(102, 126, 234, 0.3)', 'rgba(118, 75, 162, 0.2)', 'rgba(240, 147, 251, 0.1)']}
              style={styles.logoGradient}
            >
              <Ionicons name="fitness" size={hp(8)} color="#fff" />
            </LinearGradient>
          </Animated.View>
          <Text style={styles.title}>Reset Password</Text>
          <Text style={styles.subtitle}>Enter your details to reset your password</Text>
        </View>
        
        <View style={styles.form}>
          {/* Name Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Name</Text>
            <View style={[styles.inputWrapper, nameError ? styles.inputError : null]}>
              <Ionicons name="person-outline" size={20} color="#ffffff" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Enter your name"
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                value={name}
                onChangeText={setName}
                autoCapitalize="words"
              />
              {name && validateName(name) && !nameError ? (
                <Ionicons name="checkmark-circle" size={20} color="#4cd137" style={{ marginLeft: 8 }} />
              ) : null}
            </View>
            {nameError ? <Text style={styles.errorText}>{nameError}</Text> : null}
          </View>
          
          {/* Email Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Email</Text>
            <View style={[styles.inputWrapper, emailError ? styles.inputError : null]}>
              <Ionicons name="mail-outline" size={20} color="#ffffff" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Enter your email"
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {email && validateEmail(email) && !emailError ? (
                <Ionicons name="checkmark-circle" size={20} color="#4cd137" style={{ marginLeft: 8 }} />
              ) : null}
            </View>
            {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
          </View>
          
          {/* New Password Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>New Password</Text>
            <View style={[styles.inputWrapper, passwordError ? styles.inputError : null]}>
              <Ionicons name="lock-closed-outline" size={20} color="#ffffff" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Create a new password"
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                value={newPassword}
                onChangeText={setNewPassword}
                secureTextEntry={!showNewPassword}
              />
              <TouchableOpacity onPress={() => setShowNewPassword(!showNewPassword)} style={styles.eyeIcon}>
                <Ionicons name={showNewPassword ? "eye-off-outline" : "eye-outline"} size={20} color="#ffffff" />
              </TouchableOpacity>
              {newPassword && validatePassword(newPassword) && !passwordError ? (
                <Ionicons name="checkmark-circle" size={20} color="#4cd137" style={{ marginLeft: 8 }} />
              ) : null}
            </View>
            {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
          </View>
          
          {/* Confirm Password Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Confirm Password</Text>
            <View style={[styles.inputWrapper, confirmPasswordError ? styles.inputError : null]}>
              <Ionicons name="lock-closed-outline" size={20} color="#ffffff" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Confirm your new password"
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry={!showConfirmPassword}
              />
              <TouchableOpacity onPress={() => setShowConfirmPassword(!showConfirmPassword)} style={styles.eyeIcon}>
                <Ionicons name={showConfirmPassword ? "eye-off-outline" : "eye-outline"} size={20} color="#ffffff" />
              </TouchableOpacity>
              {confirmPassword && validateConfirmPassword(confirmPassword) && !confirmPasswordError ? (
                <Ionicons name="checkmark-circle" size={20} color="#4cd137" style={{ marginLeft: 8 }} />
              ) : null}
            </View>
            {confirmPasswordError ? <Text style={styles.errorText}>{confirmPasswordError}</Text> : null}
          </View>
          
          {/* Error and Success Messages */}
          {error ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorMessage}>{error}</Text>
            </View>
          ) : null}
          
          {message ? (
            <View style={styles.successContainer}>
              <Text style={styles.successMessage}>{message}</Text>
            </View>
          ) : null}
          
          {/* Update Password Button */}
          <Animated.View style={{ transform: [{ scale: buttonScale }] }}>
            <TouchableOpacity 
              style={styles.resetButton} 
              onPress={handleUpdatePassword} 
              disabled={isLoading}
            >
              <LinearGradient 
                colors={['#667eea', '#764ba2']} 
                style={styles.resetButtonGradient}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <Text style={styles.resetButtonText}>Update Password</Text>
                )}
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { 
    flex: 1, 
    backgroundColor: '#0c0c0c' 
  },
  loadingContainer: { 
    flex: 1, 
    justifyContent: 'center', 
    alignItems: 'center', 
    backgroundColor: '#0c0c0c' 
  },
  background: { 
    position: 'absolute', 
    left: 0, 
    right: 0, 
    top: 0, 
    bottom: 0 
  },
  glowCircle: { 
    position: 'absolute', 
    width: 100, 
    height: 100, 
    borderRadius: 50, 
    backgroundColor: 'rgba(102, 126, 234, 0.1)', 
    shadowColor: '#667eea', 
    shadowOffset: { width: 0, height: 0 }, 
    shadowOpacity: 0.5, 
    shadowRadius: 20, 
    elevation: 10 
  },
  backButtonContainer: { 
    position: 'absolute', 
    top: hp(5), 
    left: wp(5), 
    zIndex: 10 
  },
  backButton: { 
    width: 50, 
    height: 50, 
    borderRadius: 25, 
    overflow: 'hidden' 
  },
  backButtonGradient: { 
    flex: 1, 
    justifyContent: 'center', 
    alignItems: 'center', 
    borderRadius: 25 
  },
  content: { 
    flex: 1, 
    justifyContent: 'center', 
    paddingHorizontal: wp(6), 
    paddingTop: Platform.OS === 'ios' ? hp(5) : hp(3),
    paddingBottom: Platform.OS === 'ios' ? hp(5) : hp(3)
  },
  header: {
    alignItems: 'center',
    marginBottom: Platform.OS === 'ios' ? hp(4) : hp(2)
  },
  form: {
    width: '100%'
  },
  logoContainer: {
    width: Platform.OS === 'ios' ? wp(25) : wp(20),
    height: Platform.OS === 'ios' ? wp(25) : wp(20),
    borderRadius: Platform.OS === 'ios' ? wp(12.5) : wp(10),
    overflow: 'hidden',
    marginBottom: hp(2),
    alignSelf: 'center',
  },
  logoGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: { 
    fontSize: Platform.OS === 'ios' ? wp(7) : wp(6), 
    fontFamily: 'Poppins_700Bold', 
    color: '#ffffff', 
    textAlign: 'center', 
    marginBottom: hp(0.5) 
  },
  subtitle: { 
    fontSize: Platform.OS === 'ios' ? wp(4) : wp(3.5), 
    fontFamily: 'Poppins_400Regular', 
    color: 'rgba(255, 255, 255, 0.7)', 
    textAlign: 'center'
  },
  inputContainer: { 
    marginBottom: Platform.OS === 'ios' ? hp(2.5) : hp(1.8) 
  },
  inputLabel: { 
    fontSize: Platform.OS === 'ios' ? wp(4) : wp(3.5), 
    fontFamily: 'Poppins_600SemiBold', 
    color: '#ffffff', 
    marginBottom: hp(0.5) 
  },
  inputWrapper: { 
    flexDirection: 'row', 
    alignItems: 'center', 
    backgroundColor: 'rgba(255, 255, 255, 0.1)', 
    borderRadius: 12, 
    paddingHorizontal: wp(3), 
    paddingVertical: Platform.OS === 'ios' ? hp(2) : hp(1), 
    borderWidth: 1, 
    borderColor: 'rgba(255, 255, 255, 0.2)' 
  },
  inputError: {
    borderColor: '#ff6b6b'
  },
  inputIcon: { 
    marginRight: wp(3) 
  },
  input: { 
    flex: 1, 
    fontSize: Platform.OS === 'ios' ? wp(4) : wp(3.5), 
    fontFamily: 'Poppins_400Regular', 
    color: '#ffffff',
    paddingVertical: Platform.OS === 'ios' ? hp(1.2) : hp(0.8),
    height: Platform.OS === 'ios' ? hp(5) : hp(4.5),
  },
  eyeIcon: {
    padding: wp(2),
  },
  errorText: { 
    fontSize: wp(3.5), 
    fontFamily: 'Poppins_400Regular', 
    color: '#ff6b6b', 
    marginTop: hp(0.5)
  },
  errorContainer: {
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
    borderRadius: 8,
    padding: Platform.OS === 'ios' ? hp(1.5) : hp(1),
    marginBottom: Platform.OS === 'ios' ? hp(2) : hp(1.5),
  },
  errorMessage: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#ff6b6b',
    textAlign: 'center',
  },
  successContainer: {
    backgroundColor: 'rgba(76, 209, 55, 0.1)',
    borderRadius: 8,
    padding: Platform.OS === 'ios' ? hp(1.5) : hp(1),
    marginBottom: Platform.OS === 'ios' ? hp(2) : hp(1.5),
  },
  successMessage: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#4cd137',
    textAlign: 'center',
  },
  resetButton: { 
    width: '100%', 
    borderRadius: 12, 
    overflow: 'hidden', 
    marginTop: hp(2) 
  },
  resetButtonGradient: { 
    paddingVertical: Platform.OS === 'ios' ? hp(2.5) : hp(2), 
    alignItems: 'center', 
    borderRadius: 12 
  },
  resetButtonText: { 
    fontSize: Platform.OS === 'ios' ? wp(4.5) : wp(4), 
    fontFamily: 'Poppins_600SemiBold', 
    color: '#ffffff' 
  },
});
