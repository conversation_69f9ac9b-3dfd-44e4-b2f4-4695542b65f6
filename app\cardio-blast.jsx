import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  Animated,
  Platform,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import SafeImage from '../components/SafeImage';
import { getImageByCategory } from '../constants/remoteImages';

const { width, height } = Dimensions.get('window');

// Advanced Particle component for background effects
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, {
          toValue: 1,
          duration: (4000 + Math.random() * 3000) * speed,
          useNativeDriver: true,
        }),
        Animated.timing(particleAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    );
    
    const timeoutId = setTimeout(() => {
      animation.start();
    }, delay);
    
    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, []);

  const top = particleAnim.interpolate({ 
    inputRange: [0, 1], 
    outputRange: [height, -20] 
  });
  
  return (
    <Animated.View 
      style={[
        style, 
        { 
          transform: [{ translateY: top }], 
          opacity: opacityAnim 
        }
      ]} 
    />
  );
};

// Video Player Component with sophisticated design
const VideoPlayer = ({ videoUrl, title, duration, style }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  const handlePlayPress = () => {
    setIsLoading(true);
    // Simulate video loading
    setTimeout(() => {
      setIsLoading(false);
      setIsPlaying(true);
    }, 1000);
  };

  const handlePressIn = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.8,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  return (
    <Animated.View 
      style={[
        styles.videoCard,
        style,
        {
          transform: [{ scale: scaleAnim }],
          opacity: opacityAnim,
        }
      ]}
    >
      <TouchableOpacity
        style={styles.videoTouchable}
        onPress={handlePlayPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        <LinearGradient
          colors={['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']}
          style={styles.videoGradient}
        >
          <SafeImage 
            source={{ uri: getImageByCategory('cardio') }}
            style={styles.videoThumbnail}
            resizeMode="cover"
          />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.8)']}
            style={styles.videoOverlay}
          >
            <View style={styles.videoInfo}>
              <View style={styles.videoHeader}>
                <View style={styles.videoIconContainer}>
                  <Ionicons name="play-circle" size={24} color="#fff" />
                </View>
                <View style={styles.videoMeta}>
                  <Text style={styles.videoTitle}>{title}</Text>
                  <Text style={styles.videoDuration}>{duration}</Text>
                </View>
              </View>
              
              <View style={styles.videoControls}>
                <TouchableOpacity 
                  style={styles.playButton}
                  onPress={handlePlayPress}
                >
                  <LinearGradient
                    colors={['#FF9800', '#F57C00']}
                    style={styles.playButtonGradient}
                  >
                    <Ionicons 
                      name={isPlaying ? "pause" : "play"} 
                      size={20} 
                      color="#fff" 
                    />
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
          
          {isLoading && (
            <View style={styles.loadingOverlay}>
              <LinearGradient
                colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)']}
                style={styles.loadingGradient}
              >
                <Ionicons name="refresh" size={24} color="#fff" />
                <Text style={styles.loadingText}>Loading...</Text>
              </LinearGradient>
            </View>
          )}
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Image Slider with improved design
const ImageSlider = ({ images }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef(null);

  const handleScroll = (event) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / width);
    setCurrentIndex(index);
  };

  return (
    <View style={styles.sliderContainer}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        {images.map((image, index) => (
          <View key={index} style={styles.slide}>
            <SafeImage 
              source={image.uri} 
              style={styles.slideImage} 
              resizeMode="cover"
            />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.8)']}
              style={styles.slideOverlay}
            >
              <Text style={styles.slideTitle}>{image.title}</Text>
              <Text style={styles.slideSubtitle}>{image.subtitle}</Text>
            </LinearGradient>
          </View>
        ))}
      </ScrollView>
      <View style={styles.pagination}>
        {images.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              { backgroundColor: index === currentIndex ? '#fff' : 'rgba(255,255,255,0.5)' }
            ]}
          />
        ))}
      </View>
    </View>
  );
};

export default function CardioBlast() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  const cardioImages = [
    { 
      uri: getImageByCategory('cardio'), 
      title: 'High Intensity Cardio',
      subtitle: 'Burn calories and boost endurance'
    },
    { 
      uri: getImageByCategory('cardio'), 
      title: 'Running Workouts',
      subtitle: 'Improve your running performance'
    },
    { 
      uri: getImageByCategory('cardio'), 
      title: 'Cardio Blast',
      subtitle: 'Maximum intensity training'
    },
    { 
      uri: getImageByCategory('exercise'), 
      title: 'HIIT Training',
      subtitle: 'High-intensity interval training'
    },
  ];

  const cardioVideos = [
    {
      title: 'HIIT Cardio Blast',
      duration: '20:00',
      thumbnail: getImageByCategory('cardio'),
      videoUrl: 'https://example.com/hiit-cardio.mp4'
    },
    {
      title: 'Running Intervals',
      duration: '30:15',
      thumbnail: getImageByCategory('cardio'),
      videoUrl: 'https://example.com/running-intervals.mp4'
    },
    {
      title: 'Tabata Training',
      duration: '15:30',
      thumbnail: getImageByCategory('cardio'),
      videoUrl: 'https://example.com/tabata.mp4'
    }
  ];

  const cardioTips = [
    {
      title: 'Warm Up Properly',
      description: 'Start with 5-10 minutes of light cardio to prepare your body.',
      icon: 'thermometer',
      color: '#FF6B6B'
    },
    {
      title: 'Interval Training',
      description: 'Alternate between high and low intensity for maximum results.',
      icon: 'speedometer',
      color: '#4ECDC4'
    },
    {
      title: 'Stay Hydrated',
      description: 'Drink water before, during, and after your cardio session.',
      icon: 'water',
      color: '#667eea'
    },
    {
      title: 'Monitor Heart Rate',
      description: 'Keep your heart rate in the target zone for optimal benefits.',
      icon: 'heart',
      color: '#FFD166'
    }
  ];

  const cardioWorkouts = [
    {
      title: 'Quick HIIT Blast',
      duration: '20 min',
      difficulty: 'Intermediate',
      intensity: 'High',
      exercises: ['Jumping Jacks', 'Burpees', 'Mountain Climbers', 'High Knees'],
      image: { uri: getImageByCategory('cardio') }
    },
    {
      title: 'Steady State Cardio',
      duration: '45 min',
      difficulty: 'Beginner',
      intensity: 'Moderate',
      exercises: ['Jogging', 'Cycling', 'Elliptical', 'Rowing'],
      image: { uri: getImageByCategory('cardio') }
    },
    {
      title: 'Tabata Training',
      duration: '15 min',
      difficulty: 'Advanced',
      intensity: 'Very High',
      exercises: ['Sprint Intervals', 'Box Jumps', 'Plank Jacks', 'Squat Jumps'],
      image: { uri: getImageByCategory('cardio') }
    }
  ];

  if (!fontsLoaded) return null;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />
      <LinearGradient
        colors={['#4ECDC4', '#44A08D', '#96CEB4']}
        style={StyleSheet.absoluteFill}
      />
      
      {/* Background Particles */}
      {[...Array(15)].map((_, i) => (
        <AdvancedParticle 
          key={i} 
          style={[
            styles.particle, 
            { 
              left: `${Math.random() * 100}%`, 
              width: Math.random() * 4 + 2, 
              height: Math.random() * 4 + 2 
            }
          ]} 
          delay={Math.random() * 3000}
          speed={Math.random() * 2 + 0.5}
        />
      ))}
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Cardio Blast</Text>
        <TouchableOpacity style={styles.shareButton}>
          <Ionicons name="share-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Image Slider */}
      <ImageSlider images={cardioImages} />

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {['overview', 'workouts', 'tips', 'videos'].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[styles.tab, activeTab === tab && styles.activeTab]}
            onPress={() => setActiveTab(tab)}
          >
            <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'overview' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Boost Your Heart Health</Text>
            <Text style={styles.description}>
              Cardio workouts improve cardiovascular health, burn calories, and boost endurance. 
              They strengthen your heart and lungs while helping you maintain a healthy weight.
            </Text>
            
            <View style={styles.benefitsContainer}>
              <View style={styles.benefitCard}>
                <Ionicons name="heart" size={24} color="#FF6B6B" />
                <Text style={styles.benefitTitle}>Heart Health</Text>
                <Text style={styles.benefitText}>Strengthen your cardiovascular system</Text>
              </View>
              <View style={styles.benefitCard}>
                <Ionicons name="flame" size={24} color="#FFD166" />
                <Text style={styles.benefitTitle}>Calorie Burn</Text>
                <Text style={styles.benefitText}>Burn calories efficiently</Text>
              </View>
              <View style={styles.benefitCard}>
                <Ionicons name="trending-up" size={24} color="#4ECDC4" />
                <Text style={styles.benefitTitle}>Endurance</Text>
                <Text style={styles.benefitText}>Build stamina and endurance</Text>
              </View>
            </View>
          </View>
        )}

        {activeTab === 'workouts' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Cardio Workouts</Text>
            <Text style={styles.description}>
              Choose a cardio workout that matches your fitness level and goals.
            </Text>
            
            {cardioWorkouts.map((workout, index) => (
              <TouchableOpacity key={index} style={styles.workoutCard}>
                <LinearGradient
                  colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
                  style={styles.workoutCardGradient}
                >
                  <SafeImage 
                    source={workout.image} 
                    style={styles.workoutImage}
                    resizeMode="cover"
                  />
                  <View style={styles.workoutInfo}>
                    <Text style={styles.workoutTitle}>{workout.title}</Text>
                    <View style={styles.workoutMeta}>
                      <View style={styles.metaItem}>
                        <Ionicons name="time-outline" size={16} color="#FFD166" />
                        <Text style={styles.metaText}>{workout.duration}</Text>
                      </View>
                      <View style={styles.metaItem}>
                        <Ionicons name="fitness-outline" size={16} color="#FF6B6B" />
                        <Text style={styles.metaText}>{workout.difficulty}</Text>
                      </View>
                      <View style={styles.metaItem}>
                        <Ionicons name="speedometer-outline" size={16} color="#4ECDC4" />
                        <Text style={styles.metaText}>{workout.intensity}</Text>
                      </View>
                    </View>
                    <View style={styles.exercisesList}>
                      {workout.exercises.map((exercise, idx) => (
                        <Text key={idx} style={styles.exerciseText}>• {exercise}</Text>
                      ))}
                    </View>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {activeTab === 'tips' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Cardio Training Tips</Text>
            <Text style={styles.description}>
              Follow these essential tips for effective cardio training.
            </Text>
            
            {cardioTips.map((tip, index) => (
              <View key={index} style={styles.tipCard}>
                <View style={[styles.tipIcon, { backgroundColor: tip.color }]}>
                  <Ionicons name={tip.icon} size={24} color="#fff" />
                </View>
                <View style={styles.tipContent}>
                  <Text style={styles.tipTitle}>{tip.title}</Text>
                  <Text style={styles.tipDescription}>{tip.description}</Text>
                </View>
              </View>
            ))}
          </View>
        )}

        {activeTab === 'videos' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Cardio Training Videos</Text>
            <Text style={styles.description}>
              Follow along with these expert cardio training videos.
            </Text>
            
            {cardioVideos.map((video, index) => (
              <VideoPlayer
                key={index}
                videoUrl={video.videoUrl}
                title={video.title}
                duration={video.duration}
                style={styles.videoItem}
              />
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(5),
    paddingTop: hp(2),
    paddingBottom: hp(1),
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: wp(2),
    borderRadius: wp(2),
  },
  headerTitle: {
    fontSize: wp(7),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  shareButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: wp(2),
    borderRadius: wp(2),
  },
  sliderContainer: {
    height: hp(30),
    position: 'relative',
  },
  slide: {
    width: width,
    height: hp(30),
    position: 'relative',
  },
  slideImage: {
    width: '100%',
    height: '100%',
  },
  slideOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: hp(10),
    justifyContent: 'flex-end',
    paddingBottom: hp(2),
    paddingHorizontal: wp(5),
  },
  slideTitle: {
    fontSize: wp(6),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  slideSubtitle: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
    marginTop: hp(0.5),
  },
  pagination: {
    position: 'absolute',
    bottom: hp(1),
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    width: wp(2),
    height: wp(2),
    borderRadius: wp(1),
    marginHorizontal: wp(1),
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,0.1)',
    marginHorizontal: wp(5),
    borderRadius: wp(3),
    padding: wp(1),
  },
  tab: {
    flex: 1,
    paddingVertical: hp(1.5),
    alignItems: 'center',
    borderRadius: wp(2),
  },
  activeTab: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  tabText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255,255,255,0.7)',
  },
  activeTabText: {
    color: '#fff',
  },
  content: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: wp(10),
    borderTopRightRadius: wp(10),
    marginTop: hp(2),
  },
  section: {
    padding: wp(5),
  },
  sectionTitle: {
    fontSize: wp(7),
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: hp(1),
  },
  description: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    lineHeight: hp(2.5),
    marginBottom: hp(1.5),
  },
  benefitsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  benefitCard: {
    width: wp(40),
    backgroundColor: '#fff',
    borderRadius: wp(4),
    padding: wp(4),
    marginVertical: hp(0.5),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  benefitTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginTop: hp(1),
    textAlign: 'center',
  },
  benefitText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: hp(0.5),
  },
  workoutCard: {
    marginBottom: hp(1.5),
    borderRadius: wp(4),
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  workoutCardGradient: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: wp(4),
  },
  workoutImage: {
    width: '100%',
    height: hp(20),
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  workoutInfo: {
    flex: 1,
  },
  workoutTitle: {
    fontSize: wp(5.5),
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: hp(0.8),
  },
  workoutMeta: {
    flexDirection: 'row',
    marginBottom: hp(1),
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: wp(4),
  },
  metaText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    marginLeft: wp(1),
  },
  exercisesList: {
    marginTop: hp(0.8),
  },
  exerciseText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
    marginLeft: wp(2),
  },
  tipCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: wp(4),
    padding: wp(4),
    marginBottom: hp(1),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  tipIcon: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(4),
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginBottom: hp(0.5),
  },
  tipDescription: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    lineHeight: hp(2.5),
  },
  videoContainer: {
    width: '100%',
    height: hp(30),
    position: 'relative',
    marginBottom: hp(1.5),
  },
  videoThumbnail: {
    width: '100%',
    height: '100%',
  },
  videoOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: hp(10),
    justifyContent: 'space-between',
    paddingBottom: hp(2),
    paddingHorizontal: wp(5),
  },
  playButton: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: wp(2),
    borderRadius: wp(3),
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    zIndex: 1,
  },
  videoItem: {
    width: '100%',
    height: hp(30),
    marginBottom: hp(1.5),
  },
  videoCard: {
    width: '100%',
    height: hp(30),
    borderRadius: wp(4),
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: hp(0.5) },
    shadowOpacity: 0.1,
    shadowRadius: wp(2),
    elevation: 3,
  },
  videoTouchable: {
    flex: 1,
  },
  videoGradient: {
    flex: 1,
    borderRadius: wp(4),
  },
  videoInfo: {
    position: 'absolute',
    top: hp(2),
    left: wp(4),
    right: wp(4),
    zIndex: 1,
  },
  videoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(0.5),
  },
  videoIconContainer: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: wp(2),
    borderRadius: wp(3),
    marginRight: wp(3),
  },
  videoMeta: {
    flex: 1,
  },
  videoTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(0.3),
  },
  videoDuration: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
  },
  videoControls: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  playButtonGradient: {
    padding: wp(2),
    borderRadius: wp(3),
  },
  loadingGradient: {
    padding: wp(4),
    borderRadius: wp(4),
    alignItems: 'center',
  },
  loadingText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    marginTop: hp(0.5),
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 5,
  },
}); 