// import { Poppins_400Regular, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import { Ionicons } from '@expo/vector-icons';
// import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useRef, useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  Animated,
  Dimensions,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from '../../utils/responsive';
import { StatusBar } from 'expo-status-bar';

const { width, height } = Dimensions.get('window');

// Add AdvancedParticle and FloatingShape components from user login
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animSpeed = 4000 * speed + Math.random() * 2000;
    const animDelay = Math.random() * 3000 + delay;
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, { toValue: 1, duration: animSpeed, useNativeDriver: true }),
        Animated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = Animated.sequence([
      Animated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 1, duration: animSpeed - 1000, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);
    
    const timeoutId = setTimeout(() => {
      animation.start();
      Animated.loop(opacitySequence).start();
    }, animDelay);

    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, [delay, speed]);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <Animated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

const FloatingShape = ({ style, delay = 0, rotationSpeed = 1 }) => {
  const floatAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
        Animated.timing(floatAnim, {
          toValue: 0,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
    Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 15000 * rotationSpeed,
        useNativeDriver: true,
      })
    ).start();
    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [
            {
              translateY: (floatAnim && floatAnim.interpolate) ? 
                floatAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -30],
                }) : 0,
            },
            {
              rotate: (rotationAnim && rotationAnim.interpolate) ? 
                rotationAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg'],
                }) : '0deg',
            },
            {
              scale: scaleAnim || 1,
            },
          ],
        },
      ]}
    />
  );
};

export default function AdminLogin() {
  const router = useRouter();

  // const [fontsLoaded] = useFonts({
  //   Poppins_400Regular,
  //   Poppins_700Bold,
  // });
  const fontsLoaded = true; // Temporary fix

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const buttonScale = useRef(new Animated.Value(1)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;
  const shimmerAnim = useRef(new Animated.Value(0)).current;
  const backScale = useRef(new Animated.Value(1)).current;
  const inputScale = useRef(new Animated.Value(0.95)).current;

  useEffect(() => {
    // Main entrance animations
    Animated.stagger(200, [
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 1200,
        useNativeDriver: true,
      }),
      Animated.spring(inputScale, {
        toValue: 1,
        friction: 6,
        tension: 50,
        delay: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Continuous animations
    Animated.loop(
      Animated.sequence([
        Animated.timing(rotationAnim, {
          toValue: 1,
          duration: 25000,
          useNativeDriver: true,
        }),
        Animated.timing(rotationAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    ).start();

    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 2500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2500,
          useNativeDriver: true,
        }),
      ])
    ).start();

    Animated.loop(
      Animated.sequence([
        Animated.timing(glowAnim, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnim, {
          toValue: 0,
          duration: 3000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    Animated.loop(
      Animated.timing(shimmerAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      })
    ).start();
  }, []);

  const animateButton = useCallback(() => {
    Animated.sequence([
      Animated.timing(buttonScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.spring(buttonScale, {
        toValue: 1,
        friction: 3,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start(() => {
      const trimmedEmail = email.trim().toLowerCase();
      const trimmedPassword = password.trim();
      if (!trimmedEmail || !trimmedPassword) {
        setErrorMessage('Please fill in all fields');
      }
       else if (trimmedEmail === '<EMAIL>' && trimmedPassword === 'admin123') 
        {
        setErrorMessage('');
        (async () => {
          try {
            await AsyncStorage.setItem('@FitnessApp:adminLoggedIn', 'true');
            await AsyncStorage.removeItem('@current_user');
            setTimeout(() => {
              router.push('/admin/dashboard');
            }, 100);
          } catch (error) {
            router.push('/admin/dashboard');
          }
        })();
      } else {
        setErrorMessage('Invalid admin credentials');
      }
    });
  }, [email, password, router, buttonScale]);

  const handleBackPress = useCallback(() => {
    Animated.sequence([
      Animated.timing(backScale, {
        toValue: 0.95,
        duration: 100,
      useNativeDriver: true,
      }),
      Animated.spring(backScale, {
        toValue: 1,
        friction: 3,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start(() => {
      backScale.setValue(1);
      router.push('/role');
    });
  }, [router, backScale]);

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
          style={StyleSheet.absoluteFill}
        />
        <ActivityIndicator size="large" color="#667eea" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* Professional Animated Background */}
      <Animated.View style={[styles.backgroundContainer, { opacity: fadeAnim }]}>
        <LinearGradient
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
          style={styles.background}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
        {/* Animated Particles and Floating Shapes from user login */}
        <AdvancedParticle
          style={[styles.particle, { left: wp(10), top: hp(20) }]}
          delay={0}
          speed={1.2}
        />
        <AdvancedParticle
          style={[styles.particle, { left: wp(80), top: hp(30) }]}
          delay={500}
          speed={0.8}
        />
        <AdvancedParticle
          style={[styles.particle, { left: wp(20), top: hp(70) }]}
          delay={1000}
          speed={1.5}
        />
        <FloatingShape
          style={[styles.floatingShape, { left: wp(85), top: hp(15) }]}
          delay={0}
          rotationSpeed={0.5}
        />
        <FloatingShape
          style={[styles.floatingShape, { left: wp(5), top: hp(80) }]}
          delay={1000}
          rotationSpeed={1.2}
        />
        <View style={[styles.glowCircle, { top: hp(10), right: wp(10) }]} />
        <View style={[styles.glowCircle, { bottom: hp(20), left: wp(5) }]} />
        
        <Animated.View
          style={[
            styles.gradientOverlay,
            {
              opacity: glowAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.4, 0.8],
              }),
            },
          ]}
        >
          <LinearGradient
            colors={['rgba(102, 126, 234, 0.3)', 'rgba(118, 75, 162, 0.2)', 'rgba(240, 147, 251, 0.1)', 'transparent']}
            style={styles.overlayGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </Animated.View>

        {/* Animated background circles */}
        <Animated.View
          style={[
            styles.circle1,
            {
              transform: [
                {
                  rotate: rotationAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0deg', '360deg'],
                  }),
                },
              ],
              opacity: glowAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.1, 0.3],
              }),
            },
          ]}
        />
        <Animated.View
          style={[
            styles.circle2,
            {
              transform: [
                {
                  rotate: rotationAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['360deg', '0deg'],
                  }),
                },
              ],
              opacity: glowAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.08, 0.25],
              }),
            },
          ]}
        />
        <Animated.View
          style={[
            styles.circle3,
            {
              transform: [
                {
                  rotate: rotationAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0deg', '-360deg'],
                  }),
                },
              ],
              opacity: glowAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.06, 0.2],
              }),
            },
          ]}
        />
      </Animated.View>

      {/* Back Button */}
      <Animated.View style={[styles.backWrapper, { transform: [{ scale: backScale }] }]}>
        <TouchableOpacity
          style={styles.backButton}
          activeOpacity={0.8}
          onPress={handleBackPress}
        >
          <LinearGradient
            colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
            style={styles.backButtonGradient}
        >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>

      {/* Main Content */}
      <View style={styles.contentContainer}>
        <Animated.View 
          style={[
            styles.content,
            { 
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim || 0 }]
            }
          ]}
      >
          {/* Logo Section */}
          <Animated.View 
            style={[
              styles.logoContainer,
              { transform: [{ scale: pulseAnim }] }
            ]}
          >
            <LinearGradient
              colors={['rgba(102, 126, 234, 0.3)', 'rgba(118, 75, 162, 0.2)', 'rgba(240, 147, 251, 0.1)']}
              style={styles.logoGradient}
            >
              <Ionicons name="fitness" size={24} color="#fff" />
            </LinearGradient>
          </Animated.View>

          {/* Title Section */}
          <View style={styles.textContainer}>
            <Text style={styles.title}>Admin Panel</Text>
            <Text style={styles.subtitle}>SECURE ADMIN LOGIN</Text>
            </View>

          {/* Error Message */}
          {errorMessage !== '' && (
            <Animated.View style={styles.errorContainer}>
              <Text style={styles.errorText}>{errorMessage}</Text>
            </Animated.View>
          )}

          {/* Login Form */}
          <Animated.View 
            style={[
              styles.formContainer,
              { transform: [{ scale: inputScale }] }
            ]}
          >
            <View style={styles.inputContainer}>
              <Ionicons name="mail" size={24} color="rgba(255, 255, 255, 0.7)" style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Email Address"
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
              keyboardType="email-address"
              value={email}
              onChangeText={setEmail}
              autoCapitalize="none"
            />
            </View>

            <View style={styles.inputContainer}>
              <Ionicons name="lock-closed" size={24} color="rgba(255, 255, 255, 0.7)" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Password"
                placeholderTextColor="rgba(255, 255, 255, 0.6)"
                secureTextEntry={!showPassword}
                value={password}
                onChangeText={setPassword}
              />
              <TouchableOpacity
                style={styles.togglePassword}
                onPress={() => setShowPassword(!showPassword)}
                activeOpacity={0.7}
              >
                <Ionicons 
                  name={showPassword ? "eye-off" : "eye"} 
                  size={24} 
                  color="rgba(255, 255, 255, 0.7)" 
                />
              </TouchableOpacity>
            </View>

            {/* Login Button */}
            <Animated.View style={{ transform: [{ scale: buttonScale }] }}>
              <TouchableOpacity
                style={styles.loginButton}
                onPress={animateButton}
                activeOpacity={0.8}
              >
              <LinearGradient
                  colors={['#667eea', '#764ba2', '#f093fb', '#f5576c']}
                  style={styles.buttonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                  <Animated.View
                    style={[
                      styles.shimmerOverlay,
                      {
                        transform: [
                          {
                            translateX: shimmerAnim.interpolate({
                              inputRange: [0, 1],
                              outputRange: [-width, width],
                            }),
                          },
                        ],
                      },
                    ]}
                  />
                  <Text style={styles.buttonText}>Login</Text>
                </LinearGradient>
                </TouchableOpacity>
            </Animated.View>
          </Animated.View>
        </Animated.View>
      </View>
          </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  background: {
    flex: 1,
  },
  gradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  overlayGradient: {
    flex: 1,
  },
  circle1: {
    position: 'absolute',
    top: -hp(20),
    right: -wp(20),
    width: wp(40),
    height: wp(40),
    borderRadius: wp(20),
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
  },
  circle2: {
    position: 'absolute',
    bottom: -hp(15),
    left: -wp(15),
    width: wp(30),
    height: wp(30),
    borderRadius: wp(15),
    backgroundColor: 'rgba(240, 147, 251, 0.08)',
  },
  circle3: {
    position: 'absolute',
    top: hp(40),
    right: -wp(8),
    width: wp(16),
    height: wp(16),
    borderRadius: wp(8),
    backgroundColor: 'rgba(118, 75, 162, 0.06)',
  },
  backWrapper: {
    position: 'absolute',
    top: Platform.select({
      ios: hp(8),
      android: hp(6),
    }),
    left: wp(5),
    zIndex: 10,
  },
  backButton: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    overflow: 'hidden',
  },
  backButtonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(8),
    paddingVertical: hp(5),
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    maxWidth: wp(85),
  },
  logoContainer: {
    width: wp(25),
    height: wp(25),
    borderRadius: wp(12.5),
    overflow: 'hidden',
    marginBottom: hp(4),
  },
  logoGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    alignItems: 'center',
    paddingHorizontal: wp(5),
    marginBottom: hp(6),
  },
  title: {
    fontSize: hp(4.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: hp(2),
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 3 },
    textShadowRadius: 8,
    letterSpacing: 0.5,
  },
  subtitle: {
    fontSize: hp(2.2),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: hp(3),
  },
  errorContainer: {
    backgroundColor: 'rgba(255, 59, 48, 0.2)',
    paddingHorizontal: wp(4),
    paddingVertical: hp(1.5),
    borderRadius: wp(2),
    marginBottom: hp(3),
    borderWidth: 1,
    borderColor: 'rgba(255, 59, 48, 0.3)',
  },
  errorText: {
    color: '#ff3b30',
    fontSize: hp(1.8),
    fontFamily: 'Poppins_400Regular',
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
    alignItems: 'center',
  },
  inputContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: wp(3),
    marginBottom: hp(2.5),
    paddingHorizontal: wp(4),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  inputIcon: {
    marginRight: wp(3),
  },
  input: {
    flex: 1,
    fontSize: hp(2),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    paddingVertical: hp(2.5),
  },
  togglePassword: {
    padding: wp(1),
  },
  loginButton: {
    width: wp(75),
    height: hp(7.5),
    borderRadius: hp(3.75),
    overflow: 'hidden',
    marginTop: hp(3),
  },
  buttonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  shimmerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    transform: [{ skewX: '-20deg' }],
  },
  buttonText: {
    color: '#fff',
    fontSize: hp(2.4),
    fontFamily: 'Poppins_700Bold',
    letterSpacing: 0.5,
  },
  particle: {
    position: 'absolute',
    width: 4,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  floatingShape: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
  },
  glowCircle: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 20,
    elevation: 10,
  },
});