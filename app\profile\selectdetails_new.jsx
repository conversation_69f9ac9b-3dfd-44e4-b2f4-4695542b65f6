import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Platform,
  ScrollView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import {
  useFonts,
  Poppins_700Bold,
  Poppins_400Regular,
  Poppins_600SemiBold,
} from '@expo-google-fonts/poppins';
import { Animated as RNAnimated } from 'react-native';
import Animated, { SlideInUp } from 'react-native-reanimated';
import FloatingShape from '../../components/FloatingShape';

const { width, height } = Dimensions.get('window');

// Advanced Particle component (from index.jsx)
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;
  const scaleAnim = useRef(new RNAnimated.Value(0)).current;
  const rotationAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.parallel([
          RNAnimated.timing(particleAnim, {
            toValue: 1,
            duration: (4000 + Math.random() * 3000) * speed,
            useNativeDriver: true,
          }),
          RNAnimated.timing(opacityAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
          RNAnimated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 40,
            useNativeDriver: true,
          }),
        ]),
        RNAnimated.parallel([
          RNAnimated.timing(opacityAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
          RNAnimated.timing(scaleAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      ])
    );
    RNAnimated.loop(
      RNAnimated.timing(rotationAnim, {
        toValue: 1,
        duration: 8000 + Math.random() * 4000,
        useNativeDriver: true,
      })
    ).start();
    setTimeout(() => animation.start(), delay);
  }, []);

  return (
    <RNAnimated.View
      style={[
        style,
        {
          opacity: opacityAnim,
          transform: [
            { translateY: particleAnim.interpolate({ inputRange: [0, 1], outputRange: [0, -height * 1.5] }) },
            { scale: scaleAnim },
            { rotate: rotationAnim.interpolate({ inputRange: [0, 1], outputRange: ['0deg', '360deg'] }) },
          ],
        },
      ]}
    />
  );
};

export default function SelectDetails() {
  const router = useRouter();
  const [selectedDetails, setSelectedDetails] = useState({
    fitnessLevel: '',
    targetArea: '',
    activityLevel: '',
  });

  let [fontsLoaded] = useFonts({
    Poppins_700Bold,
    Poppins_400Regular,
    Poppins_600SemiBold,
  });

  const fitnessLevels = [
    { id: 'beginner', title: 'Beginner', icon: 'star-outline', description: 'New to fitness' },
    { id: 'intermediate', title: 'Intermediate', icon: 'star-half', description: 'Some experience' },
    { id: 'advanced', title: 'Advanced', icon: 'star', description: 'Experienced' },
  ];

  const targetAreas = [
    { id: 'fullbody', title: 'Full Body', icon: 'body-outline', description: 'Complete workout' },
    { id: 'upperbody', title: 'Upper Body', icon: 'fitness-outline', description: 'Arms, chest, back' },
    { id: 'lowerbody', title: 'Lower Body', icon: 'walk-outline', description: 'Legs and glutes' },
    { id: 'core', title: 'Core', icon: 'ellipse-outline', description: 'Abs and core' },
    { id: 'cardio', title: 'Cardio', icon: 'heart-outline', description: 'Cardiovascular' },
  ];

  const activityLevels = [
    { id: 'sedentary', title: 'Sedentary', icon: 'bed-outline', description: 'Little to no exercise' },
    { id: 'light', title: 'Light', icon: 'walk-outline', description: 'Light exercise 1-3 days' },
    { id: 'moderate', title: 'Moderate', icon: 'fitness-outline', description: 'Moderate exercise 3-5 days' },
    { id: 'active', title: 'Active', icon: 'flash-outline', description: 'Hard exercise 6-7 days' },
    { id: 'veryactive', title: 'Very Active', icon: 'flame-outline', description: 'Very hard exercise daily' },
  ];

  const handleSelection = (category, value) => {
    setSelectedDetails(prev => ({
      ...prev,
      [category]: value,
    }));
  };

  const isComplete = () => {
    return selectedDetails.fitnessLevel && selectedDetails.targetArea && selectedDetails.activityLevel;
  };

  const handleContinue = () => {
    if (isComplete()) {
      // Store the selected details in local storage or context if needed
      // Then navigate to the next screen with a unique path
      router.push('/profile/fitness-level');
    }
  };

  if (!fontsLoaded) return null;

  return (
    <View style={{ flex: 1 }}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      <AdvancedParticle style={{ position: 'absolute', width: 24, height: 24, left: width * 0.1, top: 120, zIndex: 0 }} delay={0} speed={1.2} />
      <AdvancedParticle style={{ position: 'absolute', width: 18, height: 18, left: width * 0.8, top: 200, zIndex: 0 }} delay={500} speed={0.8} />
      <AdvancedParticle style={{ position: 'absolute', width: 12, height: 12, left: width * 0.2, top: 500, zIndex: 0 }} delay={1000} speed={1.5} />
      <FloatingShape style={{ position: 'absolute', width: 32, height: 32, backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 16, left: width * 0.85, top: 100, zIndex: 0 }} delay={0} rotationSpeed={0.5} />
      <FloatingShape style={{ position: 'absolute', width: 20, height: 20, backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 10, left: width * 0.05, top: 600, zIndex: 0 }} delay={1000} rotationSpeed={1.2} />
      <View style={{ position: 'absolute', top: 60, right: 30, width: 50, height: 50, borderRadius: 25, backgroundColor: 'rgba(255,255,255,0.1)' }} />
      <View style={{ position: 'absolute', bottom: 80, left: 20, width: 50, height: 50, borderRadius: 25, backgroundColor: 'rgba(255,255,255,0.1)' }} />
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={styles.background}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Back Button */}
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton} activeOpacity={0.8}>
            <LinearGradient
              colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
              style={styles.backButtonGradient}
            >
              <Ionicons name="arrow-back" size={24} color="#fff" />
            </LinearGradient>
          </TouchableOpacity>

          {/* Main Content with ScrollView */}
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.title}>Select Details</Text>
              <Text style={styles.subtitle}>Let's personalize your fitness journey</Text>
            </View>

            {/* Progress Indicator */}
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: '25%' }]} />
              </View>
              <Text style={styles.progressText}>Step 1 of 4</Text>
            </View>

            {/* Fitness Level Selection */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Fitness Level</Text>
              <Text style={styles.sectionDescription}>
                Choose your current fitness level to get personalized workouts
              </Text>
              
              <View style={styles.optionsContainer}>
                {fitnessLevels.map((level, index) => (
                  <View 
                    key={level.id} 
                  >
                    <TouchableOpacity
                      style={[
                        styles.optionCard,
                        selectedDetails.fitnessLevel === level.id && styles.optionCardSelected
                      ]}
                      onPress={() => handleSelection('fitnessLevel', level.id)}
                      activeOpacity={0.8}
                    >
                      <LinearGradient
                        colors={selectedDetails.fitnessLevel === level.id 
                          ? ['#667eea', '#764ba2'] 
                          : ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']
                        }
                        style={styles.optionGradient}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                      >
                        <View style={styles.optionIcon}>
                          <Ionicons 
                            name={level.icon} 
                            size={24} 
                            color={selectedDetails.fitnessLevel === level.id ? '#fff' : 'rgba(255,255,255,0.8)'} 
                          />
                        </View>
                        <View style={styles.optionContent}>
                          <Text style={[
                            styles.optionTitle,
                            selectedDetails.fitnessLevel === level.id && styles.optionTitleSelected
                          ]}>
                            {level.title}
                          </Text>
                          <Text style={[
                            styles.optionDescription,
                            selectedDetails.fitnessLevel === level.id && styles.optionDescriptionSelected
                          ]}>
                            {level.description}
                          </Text>
                        </View>
                        {selectedDetails.fitnessLevel === level.id && (
                          <Ionicons name="checkmark-circle" size={24} color="#fff" />
                        )}
                      </LinearGradient>
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            </View>

            {/* Target Area Selection */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Target Area</Text>
              <Text style={styles.sectionDescription}>
                Select the areas you want to focus on during your workouts
              </Text>
              
              <View style={styles.optionsContainer}>
                {targetAreas.map((area, index) => (
                  <View 
                    key={area.id} 
                  >
                    <TouchableOpacity
                      style={[
                        styles.optionCard,
                        selectedDetails.targetArea === area.id && styles.optionCardSelected
                      ]}
                      onPress={() => handleSelection('targetArea', area.id)}
                      activeOpacity={0.8}
                    >
                      <LinearGradient
                        colors={selectedDetails.targetArea === area.id 
                          ? ['#667eea', '#764ba2'] 
                          : ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']
                        }
                        style={styles.optionGradient}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                      >
                        <View style={styles.optionIcon}>
                          <Ionicons 
                            name={area.icon} 
                            size={24} 
                            color={selectedDetails.targetArea === area.id ? '#fff' : 'rgba(255,255,255,0.8)'} 
                          />
                        </View>
                        <View style={styles.optionContent}>
                          <Text style={[
                            styles.optionTitle,
                            selectedDetails.targetArea === area.id && styles.optionTitleSelected
                          ]}>
                            {area.title}
                          </Text>
                          <Text style={[
                            styles.optionDescription,
                            selectedDetails.targetArea === area.id && styles.optionDescriptionSelected
                          ]}>
                            {area.description}
                          </Text>
                        </View>
                        {selectedDetails.targetArea === area.id && (
                          <Ionicons name="checkmark-circle" size={24} color="#fff" />
                        )}
                      </LinearGradient>
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            </View>

            {/* Activity Level Selection */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Activity Level</Text>
              <Text style={styles.sectionDescription}>
                How active are you in your daily life?
              </Text>
              
              <View style={styles.optionsContainer}>
                {activityLevels.map((level, index) => (
                  <View 
                    key={level.id} 
                  >
                    <TouchableOpacity
                      style={[
                        styles.optionCard,
                        selectedDetails.activityLevel === level.id && styles.optionCardSelected
                      ]}
                      onPress={() => handleSelection('activityLevel', level.id)}
                      activeOpacity={0.8}
                    >
                      <LinearGradient
                        colors={selectedDetails.activityLevel === level.id 
                          ? ['#667eea', '#764ba2'] 
                          : ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']
                        }
                        style={styles.optionGradient}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                      >
                        <View style={styles.optionIcon}>
                          <Ionicons 
                            name={level.icon} 
                            size={24} 
                            color={selectedDetails.activityLevel === level.id ? '#fff' : 'rgba(255,255,255,0.8)'} 
                          />
                        </View>
                        <View style={styles.optionContent}>
                          <Text style={[
                            styles.optionTitle,
                            selectedDetails.activityLevel === level.id && styles.optionTitleSelected
                          ]}>
                            {level.title}
                          </Text>
                          <Text style={[
                            styles.optionDescription,
                            selectedDetails.activityLevel === level.id && styles.optionDescriptionSelected
                          ]}>
                            {level.description}
                          </Text>
                        </View>
                        {selectedDetails.activityLevel === level.id && (
                          <Ionicons name="checkmark-circle" size={24} color="#fff" />
                        )}
                      </LinearGradient>
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            </View>
          </ScrollView>

          {/* Continue Button - always visible at the bottom */}
          <View style={styles.fixedButtonContainer}>
              <TouchableOpacity
                style={styles.button}
                onPress={handleContinue}
                activeOpacity={0.8}
                disabled={!isComplete()}
              >
                <LinearGradient
                  colors={isComplete() ? ['#667eea', '#764ba2'] : ['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.2)']}
                  style={styles.buttonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                >
                  <Text style={[styles.buttonText, !isComplete() && styles.buttonTextDisabled]}>
                    Continue
                  </Text>
                  <Ionicons name="arrow-forward" size={20} color={isComplete() ? "#fff" : "rgba(255,255,255,0.5)"} />
                </LinearGradient>
              </TouchableOpacity>
          </View>
        </LinearGradient>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    paddingTop: 50,
  },
  particle: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  floatingShape: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 10,
  },
  backButtonGradient: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 100,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  progressContainer: {
    marginBottom: 30,
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 3,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#fff',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 20,
    textAlign: 'center',
  },
  optionsContainer: {
    gap: 12,
  },
  optionCard: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  optionCardSelected: {
    transform: [{ scale: 1.02 }],
  },
  optionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 4,
  },
  optionTitleSelected: {
    color: '#fff',
  },
  optionDescription: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  optionDescriptionSelected: {
    color: 'rgba(255, 255, 255, 0.9)',
  },
  buttonContainer: {
    marginTop: 20,
    marginBottom: 40,
    alignItems: 'center',
  },
  button: {
    width: '70%',
    height: 56,
    borderRadius: 28,
    overflow: 'hidden',
  },
  buttonGradient: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    marginRight: 8,
  },
  buttonTextDisabled: {
    color: 'rgba(255, 255, 255, 0.5)',
  },
  fixedButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 30,
    alignItems: 'center',
    zIndex: 100,
  },
}); 