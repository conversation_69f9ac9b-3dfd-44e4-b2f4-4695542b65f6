import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  ScrollView,
  Image,
  Dimensions,
  Animated
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import { demoExercises } from '../../constants/data';

const { width, height } = Dimensions.get('window');

// Advanced Particle component
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animSpeed = 4000 * speed + Math.random() * 2000;
    const animDelay = Math.random() * 3000 + delay;
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, { toValue: 1, duration: animSpeed, useNativeDriver: true }),
        Animated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = Animated.sequence([
      Animated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 1, duration: animSpeed - 1000, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);
    
    const timeoutId = setTimeout(() => {
      animation.start();
      Animated.loop(opacitySequence).start();
    }, animDelay);

    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, [delay, speed]);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <Animated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

const FloatingShape = ({ style, delay = 0, rotationSpeed = 1 }) => {
  const floatAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
        Animated.timing(floatAnim, {
          toValue: 0,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
    Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 15000 * rotationSpeed,
        useNativeDriver: true,
      })
    ).start();
    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [
            {
              translateY: (floatAnim && floatAnim.interpolate) ? 
                floatAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -30],
                }) : 0,
            },
            {
              rotate: (rotationAnim && rotationAnim.interpolate) ? 
                rotationAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg'],
                }) : '0deg',
            },
            {
              scale: scaleAnim || 1,
            },
          ],
        },
      ]}
    />
  );
};

export default function ExerciseLibrary() {
  const router = useRouter();
  const [exercises, setExercises] = useState([]);
  const [filteredExercises, setFilteredExercises] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedExercise, setSelectedExercise] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  const categories = ['all', 'back', 'cardio', 'chest', 'lowerarms', 'lowerlegs', 'shoulder', 'abs', 'legs', 'fullbody', 'core'];
  const difficulties = ['Beginner', 'Intermediate', 'Advanced', 'Expert'];

  useEffect(() => {
    fetchExercises();
  }, []);

  useEffect(() => {
    filterExercises();
  }, [searchQuery, selectedCategory, exercises]);

  const fetchExercises = async () => {
    try {
      const exercisesData = await AsyncStorage.getItem('@exercises');
      if (exercisesData) {
        const parsedExercises = JSON.parse(exercisesData);
        setExercises(parsedExercises);
      } else {
        // Initialize with demo exercises
        await AsyncStorage.setItem('@exercises', JSON.stringify(demoExercises));
        setExercises(demoExercises);
      }
    } catch (error) {
      console.error('Error fetching exercises:', error);
      Alert.alert('Error', 'Failed to load exercises');
    } finally {
      setLoading(false);
    }
  };

  const filterExercises = () => {
    let filtered = exercises;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(exercise => exercise.bodyPart === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(exercise =>
        exercise.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        exercise.target?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        exercise.bodyPart?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredExercises(filtered);
  };

  const handleExerciseAction = (exercise, action) => {
    setSelectedExercise(exercise);
    switch (action) {
      case 'view':
        setModalVisible(true);
        break;
      case 'edit':
        setEditModalVisible(true);
        break;
      case 'delete':
        handleDeleteExercise(exercise);
        break;
    }
  };

  const handleDeleteExercise = (exercise) => {
    Alert.alert(
      'Delete Exercise',
      `Are you sure you want to delete "${exercise.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const updatedExercises = exercises.filter(e => e.id !== exercise.id);
              await AsyncStorage.setItem('@exercises', JSON.stringify(updatedExercises));
              setExercises(updatedExercises);
              Alert.alert('Success', 'Exercise deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete exercise');
            }
          }
        }
      ]
    );
  };

  const handleSaveExercise = async (exerciseData, isNew = false) => {
    try {
      let updatedExercises;
      
      if (isNew) {
        const newExercise = {
          ...exerciseData,
          id: Date.now().toString(),
          createdAt: new Date().toISOString()
        };
        updatedExercises = [...exercises, newExercise];
      } else {
        updatedExercises = exercises.map(e =>
          e.id === exerciseData.id ? { ...exerciseData, updatedAt: new Date().toISOString() } : e
        );
      }

      await AsyncStorage.setItem('@exercises', JSON.stringify(updatedExercises));
      setExercises(updatedExercises);
      setEditModalVisible(false);
      setAddModalVisible(false);
      Alert.alert('Success', `Exercise ${isNew ? 'added' : 'updated'} successfully`);
    } catch (error) {
      Alert.alert('Error', `Failed to ${isNew ? 'add' : 'update'} exercise`);
    }
  };

  const renderExerciseItem = ({ item }) => (
    <View style={styles.exerciseCard}>
      <View style={styles.exerciseHeader}>
        <Image source={{ uri: item.gifUrl }} style={styles.exerciseImage} />
        <View style={styles.exerciseInfo}>
          <Text style={styles.exerciseName}>{item.name}</Text>
          <Text style={styles.exerciseTarget}>Target: {item.target}</Text>
          <Text style={styles.exerciseBodyPart}>Category: {item.bodyPart}</Text>
          <Text style={styles.exerciseEquipment}>Equipment: {item.equipment}</Text>
        </View>
        <View style={styles.exerciseActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#4ECDC4' }]}
            onPress={() => handleExerciseAction(item, 'view')}
          >
            <Ionicons name="eye" size={16} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#45B7D1' }]}
            onPress={() => handleExerciseAction(item, 'edit')}
          >
            <Ionicons name="create" size={16} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#FF6B6B' }]}
            onPress={() => handleExerciseAction(item, 'delete')}
          >
            <Ionicons name="trash" size={16} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      {item.secondaryMuscles && item.secondaryMuscles.length > 0 && (
        <View style={styles.secondaryMuscles}>
          <Text style={styles.secondaryMusclesLabel}>Secondary muscles:</Text>
          <Text style={styles.secondaryMusclesText}>
            {item.secondaryMuscles.join(', ')}
          </Text>
        </View>
      )}
    </View>
  );

  if (!fontsLoaded) return null;

  return (
    <>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={{ flex: 1 }}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Example floating shape/particle */}
        <Animated.View style={{ position: 'absolute', top: 40, left: 30, width: 60, height: 60, borderRadius: 30, backgroundColor: 'rgba(255,255,255,0.08)' }} />
        {/* Main content below, unchanged logic */}
        <View style={{ flex: 1 }}>
          <View style={styles.outerContainer}>
            {/* Animated Particles and Floating Shapes */}
            <AdvancedParticle style={styles.particle1} delay={0} speed={1.2} />
            <AdvancedParticle style={styles.particle2} delay={500} speed={0.8} />
            <AdvancedParticle style={styles.particle3} delay={1000} speed={1.5} />
            <FloatingShape style={styles.floatingShape1} delay={0} rotationSpeed={0.5} />
            <FloatingShape style={styles.floatingShape2} delay={1000} rotationSpeed={1.2} />
            <View style={[styles.glowCircle, { top: 60, right: 30 }]} />
            <View style={[styles.glowCircle, { bottom: 80, left: 20 }]} />
            {/* Main Content */}
            <LinearGradient
              colors={['#1E90FF', '#00C9A7', '#21D4FD']}
              style={styles.container}
            >
              {/* Header */}
              <View style={styles.header}>
                <TouchableOpacity
                  style={styles.backButton}
                  onPress={() => router.back()}
                >
                  <Ionicons name="arrow-back" size={24} color="#fff" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Exercise Library</Text>
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={() => setAddModalVisible(true)}
                >
                  <Ionicons name="add" size={24} color="#fff" />
                </TouchableOpacity>
              </View>

              {/* Search and Filter */}
              <View style={styles.searchContainer}>
                <View style={styles.searchInputContainer}>
                  <Ionicons name="search" size={20} color="#7f8c8d" style={styles.searchIcon} />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search exercises..."
                    placeholderTextColor="#7f8c8d"
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                  />
                </View>
                
                <View style={styles.filterContainer}>
                  <Picker
                    selectedValue={selectedCategory}
                    style={styles.categoryPicker}
                    onValueChange={setSelectedCategory}
                  >
                    {categories.map((category) => (
                      <Picker.Item 
                        key={category} 
                        label={category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)} 
                        value={category} 
                      />
                    ))}
                  </Picker>
                </View>
              </View>

              {/* Exercise List */}
              <FlatList
                data={filteredExercises}
                renderItem={renderExerciseItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
              />

              {/* Exercise Detail Modal */}
              <Modal
                animationType="slide"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
              >
                <ExerciseDetailModal
                  exercise={selectedExercise}
                  onClose={() => setModalVisible(false)}
                />
              </Modal>

              {/* Edit Exercise Modal */}
              <Modal
                animationType="slide"
                transparent={true}
                visible={editModalVisible}
                onRequestClose={() => setEditModalVisible(false)}
              >
                <ExerciseFormModal
                  exercise={selectedExercise}
                  onSave={handleSaveExercise}
                  onClose={() => setEditModalVisible(false)}
                  title="Edit Exercise"
                  categories={categories}
                />
              </Modal>

              {/* Add Exercise Modal */}
              <Modal
                animationType="slide"
                transparent={true}
                visible={addModalVisible}
                onRequestClose={() => setAddModalVisible(false)}
              >
                <ExerciseFormModal
                  exercise={null}
                  onSave={(data) => handleSaveExercise(data, true)}
                  onClose={() => setAddModalVisible(false)}
                  title="Add New Exercise"
                  categories={categories}
                />
              </Modal>
            </LinearGradient>
          </View>
        </View>
      </LinearGradient>
    </>
  );
}

// Exercise Detail Modal Component
const ExerciseDetailModal = ({ exercise, onClose }) => {
  if (!exercise) return null;

  return (
    <View style={styles.modalOverlay}>
      <View style={styles.modalContent}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>{exercise.name}</Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#2c3e50" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalBody}>
          <Image source={{ uri: exercise.gifUrl }} style={styles.modalImage} />
          
          <Text style={styles.modalLabel}>Target Muscle:</Text>
          <Text style={styles.modalValue}>{exercise.target}</Text>

          <Text style={styles.modalLabel}>Body Part:</Text>
          <Text style={styles.modalValue}>{exercise.bodyPart}</Text>

          <Text style={styles.modalLabel}>Equipment:</Text>
          <Text style={styles.modalValue}>{exercise.equipment}</Text>

          {exercise.secondaryMuscles && exercise.secondaryMuscles.length > 0 && (
            <>
              <Text style={styles.modalLabel}>Secondary Muscles:</Text>
              <Text style={styles.modalValue}>{exercise.secondaryMuscles.join(', ')}</Text>
            </>
          )}

          {exercise.instructions && exercise.instructions.length > 0 && (
            <>
              <Text style={styles.modalLabel}>Instructions:</Text>
              {exercise.instructions.map((instruction, index) => (
                <Text key={index} style={styles.instructionText}>
                  {index + 1}. {instruction}
                </Text>
              ))}
            </>
          )}
        </ScrollView>
      </View>
    </View>
  );
};

// Exercise Form Modal Component
const ExerciseFormModal = ({ exercise, onSave, onClose, title, categories }) => {
  const [formData, setFormData] = useState(exercise || {
    name: '',
    target: '',
    bodyPart: 'chest',
    equipment: 'body weight',
    gifUrl: '',
    videoUrl: '',
    instructions: [''],
    secondaryMuscles: [''],
    videoTutorialEnabled: true
  });

  const handleSave = () => {
    if (!formData.name || !formData.target) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    // Clean up arrays
    const cleanedData = {
      ...formData,
      instructions: formData.instructions.filter(i => i.trim()),
      secondaryMuscles: formData.secondaryMuscles.filter(m => m.trim())
    };

    onSave(cleanedData);
  };

  const addInstruction = () => {
    setFormData({
      ...formData,
      instructions: [...formData.instructions, '']
    });
  };

  const updateInstruction = (index, value) => {
    const newInstructions = [...formData.instructions];
    newInstructions[index] = value;
    setFormData({ ...formData, instructions: newInstructions });
  };

  const removeInstruction = (index) => {
    const newInstructions = formData.instructions.filter((_, i) => i !== index);
    setFormData({ ...formData, instructions: newInstructions });
  };

  return (
    <View style={styles.modalOverlay}>
      <View style={styles.modalContent}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>{title}</Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#2c3e50" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalBody}>
          <Text style={styles.inputLabel}>Exercise Name *</Text>
          <TextInput
            style={styles.textInput}
            value={formData.name}
            onChangeText={(text) => setFormData({...formData, name: text})}
            placeholder="Enter exercise name"
          />

          <Text style={styles.inputLabel}>Target Muscle *</Text>
          <TextInput
            style={styles.textInput}
            value={formData.target}
            onChangeText={(text) => setFormData({...formData, target: text})}
            placeholder="Enter target muscle"
          />

          <Text style={styles.inputLabel}>Body Part</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={formData.bodyPart}
              style={styles.picker}
              onValueChange={(value) => setFormData({...formData, bodyPart: value})}
            >
              {categories.filter(c => c !== 'all').map((category) => (
                <Picker.Item key={category} label={category} value={category} />
              ))}
            </Picker>
          </View>

          <Text style={styles.inputLabel}>Equipment</Text>
          <TextInput
            style={styles.textInput}
            value={formData.equipment}
            onChangeText={(text) => setFormData({...formData, equipment: text})}
            placeholder="Enter equipment needed"
          />

          <Text style={styles.inputLabel}>GIF URL</Text>
          <TextInput
            style={styles.textInput}
            value={formData.gifUrl}
            onChangeText={(text) => setFormData({...formData, gifUrl: text})}
            placeholder="Enter GIF URL"
          />

          <Text style={styles.inputLabel}>Video URL</Text>
          <TextInput
            style={styles.textInput}
            value={formData.videoUrl}
            onChangeText={(text) => setFormData({...formData, videoUrl: text})}
            placeholder="Enter video URL"
          />

          <Text style={styles.inputLabel}>Instructions</Text>
          {formData.instructions.map((instruction, index) => (
            <View key={index} style={styles.instructionRow}>
              <TextInput
                style={[styles.textInput, styles.instructionInput]}
                value={instruction}
                onChangeText={(text) => updateInstruction(index, text)}
                placeholder={`Step ${index + 1}`}
              />
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => removeInstruction(index)}
              >
                <Ionicons name="remove-circle" size={24} color="#FF6B6B" />
              </TouchableOpacity>
            </View>
          ))}
          <TouchableOpacity style={styles.addButton} onPress={addInstruction}>
            <Ionicons name="add-circle" size={24} color="#4ECDC4" />
            <Text style={styles.addButtonText}>Add Instruction</Text>
          </TouchableOpacity>

          <View style={styles.modalActions}>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  particle1: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderRadius: 12,
    left: width * 0.1,
    top: 120,
    zIndex: 0,
  },
  particle2: {
    position: 'absolute',
    width: 18,
    height: 18,
    borderRadius: 9,
    left: width * 0.8,
    top: 200,
    zIndex: 0,
  },
  particle3: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    left: width * 0.2,
    top: 500,
    zIndex: 0,
  },
  particleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  floatingShape1: {
    position: 'absolute',
    width: 32,
    height: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    left: width * 0.85,
    top: 100,
    zIndex: 0,
  },
  floatingShape2: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    left: width * 0.05,
    top: 600,
    zIndex: 0,
  },
  glowCircle: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    zIndex: 0,
  },
  container: {
    flex: 1,
    paddingTop: 50,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  addButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 15,
    marginBottom: 10,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
  },
  filterContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
  },
  categoryPicker: {
    height: 50,
    color: '#2c3e50',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  exerciseCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  exerciseHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  exerciseImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
  },
  exerciseInfo: {
    flex: 1,
  },
  exerciseName: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  exerciseTarget: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: '#7f8c8d',
    marginBottom: 2,
  },
  exerciseBodyPart: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    marginBottom: 2,
  },
  exerciseEquipment: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
  },
  exerciseActions: {
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  actionButton: {
    padding: 8,
    borderRadius: 8,
    marginBottom: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  secondaryMuscles: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#ecf0f1',
  },
  secondaryMusclesLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: '#7f8c8d',
    marginBottom: 4,
  },
  secondaryMusclesText: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#95a5a6',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
  },
  modalBody: {
    maxHeight: 400,
  },
  modalImage: {
    width: '100%',
    height: 150,
    borderRadius: 12,
    marginBottom: 16,
  },
  modalLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginTop: 12,
    marginBottom: 4,
  },
  modalValue: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    marginBottom: 8,
  },
  instructionText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    marginBottom: 4,
    lineHeight: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginTop: 12,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ecf0f1',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
    marginBottom: 12,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ecf0f1',
    borderRadius: 8,
    marginBottom: 12,
  },
  picker: {
    height: 50,
    color: '#2c3e50',
  },
  instructionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  instructionInput: {
    flex: 1,
    marginRight: 10,
    marginBottom: 0,
  },
  removeButton: {
    padding: 4,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    marginBottom: 20,
  },
  addButtonText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#4ECDC4',
    marginLeft: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    backgroundColor: '#95a5a6',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    marginRight: 10,
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: '#4ECDC4',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    marginLeft: 10,
  },
  saveButtonText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    textAlign: 'center',
  },
});