import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Animated,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import * as Progress from 'react-native-progress';

const { width, height } = Dimensions.get('window');

// Advanced Particle component
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animSpeed = 4000 * speed + Math.random() * 2000;
    const animDelay = Math.random() * 3000 + delay;
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, { toValue: 1, duration: animSpeed, useNativeDriver: true }),
        Animated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = Animated.sequence([
      Animated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 1, duration: animSpeed - 1000, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);
    
    const timeoutId = setTimeout(() => {
      animation.start();
      Animated.loop(opacitySequence).start();
    }, animDelay);

    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, [delay, speed]);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <Animated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

const FloatingShape = ({ style, delay = 0, rotationSpeed = 1 }) => {
  const floatAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const floatAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
        Animated.timing(floatAnim, {
          toValue: 0,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
      ])
    );
    
    const rotateAnimation = Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 15000 * rotationSpeed,
        useNativeDriver: true,
      })
    );
    
    const timeoutId = setTimeout(() => {
      floatAnimation.start();
      rotateAnimation.start();
    }, delay);

    return () => {
      clearTimeout(timeoutId);
      floatAnimation.stop();
      rotateAnimation.stop();
    };
  }, [delay, rotationSpeed]);

  const translateY = floatAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });

  const rotate = rotationAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{ translateY }, { rotate }],
        },
      ]}
    />
  );
};

export default function PlanPerformance() {
  // Always call useFonts first to maintain hook order
  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  const router = useRouter();
  const [analytics, setAnalytics] = useState({
    planStats: [],
    overallStats: {
      totalPlans: 0,
      totalEnrollments: 0,
      averageCompletion: 0,
      mostPopularPlan: null,
      bestPerformingPlan: null
    },
    userEngagement: {
      activeUsers: 0,
      completedPlans: 0,
      averageProgress: 0,
      retentionRate: 0
    }
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all data from AsyncStorage
      const [plansData, historyData, usersData, performanceData] = await Promise.all([
        AsyncStorage.getItem('@diet_plans'),
        AsyncStorage.getItem('@workout_history'),
        AsyncStorage.getItem('@FitnessApp:users'),
        AsyncStorage.getItem('@plan_performance')
      ]);

      let plans = [];
      let history = [];
      let users = [];
      let performance = [];

      try { plans = plansData ? JSON.parse(plansData) : []; } catch (e) { console.error('Error parsing plans:', e); }
      try { history = historyData ? JSON.parse(historyData) : []; } catch (e) { console.error('Error parsing history:', e); }
      try { users = usersData ? JSON.parse(usersData) : []; } catch (e) { console.error('Error parsing users:', e); }
      try { performance = performanceData ? JSON.parse(performanceData) : []; } catch (e) { console.error('Error parsing performance:', e); }

      // Calculate plan statistics with error handling
      const planStats = plans.map(plan => {
        try {
          const planHistory = history.filter(h => h.planTitle === plan.title || h.planId === plan.id);
          const enrollments = planHistory.length;
          const completions = planHistory.filter(h => h.status === 'completed').length;
          const inProgress = planHistory.filter(h => h.status === 'active' || h.status === 'in_progress').length;
          const completionRate = enrollments > 0 ? (completions / enrollments) * 100 : 0;
          
          // Calculate average progress
          const totalProgress = planHistory.reduce((sum, h) => {
            const progress = h.daysCompleted && h.totalDays ? (h.daysCompleted / h.totalDays) * 100 : 0;
            return sum + progress;
          }, 0);
          const averageProgress = enrollments > 0 ? totalProgress / enrollments : 0;

          return {
            ...plan,
            enrollments,
            completions,
            inProgress,
            completionRate,
            averageProgress,
            popularity: enrollments
          };
        } catch (error) {
          console.error('Error processing plan:', plan.title, error);
          return {
            ...plan,
            enrollments: 0,
            completions: 0,
            inProgress: 0,
            completionRate: 0,
            averageProgress: 0,
            popularity: 0
          };
        }
      });

      // Sort by popularity and completion rate
      const sortedByPopularity = [...planStats].sort((a, b) => b.popularity - a.popularity);
      const sortedByCompletion = [...planStats].sort((a, b) => b.completionRate - a.completionRate);

      // Calculate overall statistics
      const totalPlans = plans.length;
      const totalEnrollments = planStats.reduce((sum, plan) => sum + plan.enrollments, 0);
      const totalCompletions = planStats.reduce((sum, plan) => sum + plan.completions, 0);
      const averageCompletion = totalEnrollments > 0 ? (totalCompletions / totalEnrollments) * 100 : 0;
      const mostPopularPlan = sortedByPopularity[0] || null;
      const bestPerformingPlan = sortedByCompletion[0] || null;

      // Calculate user engagement
      const activeUsers = users.filter(user => {
        const lastActivity = user.lastLogin || user.lastActivity;
        if (!lastActivity) return false;
        const daysSinceActivity = (Date.now() - new Date(lastActivity).getTime()) / (1000 * 60 * 60 * 24);
        return daysSinceActivity <= 7;
      }).length;

      const completedPlans = totalCompletions;
      const averageProgress = planStats.reduce((sum, plan) => sum + plan.averageProgress, 0) / Math.max(planStats.length, 1);
      const retentionRate = totalEnrollments > 0 ? (activeUsers / totalEnrollments) * 100 : 0;

      setAnalytics({
        planStats: planStats,
        overallStats: {
          totalPlans,
          totalEnrollments,
          averageCompletion,
          mostPopularPlan,
          bestPerformingPlan
        },
        userEngagement: {
          activeUsers,
          completedPlans,
          averageProgress,
          retentionRate
        }
      });

    } catch (error) {
      console.error('Error fetching plan performance analytics:', error);
      setError('Failed to load analytics data.');
      // Set default values if error occurs
      setAnalytics({
        planStats: [],
        overallStats: {
          totalPlans: 0,
          totalEnrollments: 0,
          averageCompletion: 0,
          mostPopularPlan: null,
          bestPerformingPlan: null
        },
        userEngagement: {
          activeUsers: 0,
          completedPlans: 0,
          averageProgress: 0,
          retentionRate: 0
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const renderPlanCard = (plan, index) => (
    <View key={plan.id} style={styles.planCard}>
      <View style={styles.planHeader}>
        <View style={styles.planRank}>
          <Text style={styles.rankNumber}>#{index + 1}</Text>
        </View>
        <View style={styles.planInfo}>
          <Text style={styles.planTitle}>{plan.title}</Text>
          <Text style={styles.planCategory}>{plan.category} • {plan.difficulty}</Text>
        </View>
        <View style={styles.planMetrics}>
          <Text style={styles.metricValue}>{plan.enrollments}</Text>
          <Text style={styles.metricLabel}>Enrollments</Text>
        </View>
      </View>

      <View style={styles.planStats}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{plan.completions}</Text>
          <Text style={styles.statLabel}>Completed</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{plan.inProgress}</Text>
          <Text style={styles.statLabel}>In Progress</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{plan.completionRate.toFixed(1)}%</Text>
          <Text style={styles.statLabel}>Completion Rate</Text>
        </View>
      </View>

      <View style={styles.progressSection}>
        <Text style={styles.progressLabel}>Average Progress</Text>
        <Progress.Bar
          progress={plan.averageProgress / 100}
          width={width - 80}
          height={8}
          color="#4ECDC4"
          unfilledColor="#ecf0f1"
          borderWidth={0}
          borderRadius={4}
        />
        <Text style={styles.progressText}>{plan.averageProgress.toFixed(1)}%</Text>
      </View>
    </View>
  );

  const renderOverviewCard = (title, value, subtitle, icon, color) => (
    <View style={[styles.overviewCard, { borderLeftColor: color }]}>
      <View style={styles.overviewHeader}>
        <Ionicons name={icon} size={24} color={color} />
        <Text style={styles.overviewValue}>{value}</Text>
      </View>
      <Text style={styles.overviewTitle}>{title}</Text>
      {subtitle && <Text style={styles.overviewSubtitle}>{subtitle}</Text>}
    </View>
  );

  if (!fontsLoaded) {
    return null;
  }

  if (loading) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2']}
          style={StyleSheet.absoluteFill}
        />
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Plan Performance</Text>
          </View>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#fff" />
            <Text style={styles.loadingText}>Loading plan performance...</Text>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2']}
          style={StyleSheet.absoluteFill}
        />
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Plan Performance</Text>
          </View>
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={64} color="#FF6B6B" />
            <Text style={styles.errorTitle}>Error Loading Data</Text>
            <Text style={styles.errorMessage}>
              {error.message || 'Failed to load plan performance data. Please try again.'}
            </Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={fetchAnalytics}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={styles.outerContainer}>
      <LinearGradient
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
        style={StyleSheet.absoluteFill}
      />
      {/* Animated Particles and Floating Shapes */}
      <AdvancedParticle style={styles.particle1} delay={0} speed={1.2} />
      <AdvancedParticle style={styles.particle2} delay={500} speed={0.8} />
      <AdvancedParticle style={styles.particle3} delay={1000} speed={1.5} />
      <FloatingShape style={styles.floatingShape1} delay={0} rotationSpeed={0.5} />
      <FloatingShape style={styles.floatingShape2} delay={1000} rotationSpeed={1.2} />
      <View style={[styles.glowCircle, { top: 60, right: 30 }]} />
      <View style={[styles.glowCircle, { bottom: 80, left: 20 }]} />
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Plan Performance</Text>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={fetchAnalytics}
          >
            <Ionicons name="refresh" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Overview Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Overview</Text>
            <View style={styles.overviewGrid}>
              {renderOverviewCard(
                'Total Plans',
                analytics.overallStats.totalPlans,
                'Active plans in system',
                'list',
                '#667eea'
              )}
              {renderOverviewCard(
                'Total Enrollments',
                analytics.overallStats.totalEnrollments,
                'Users enrolled in plans',
                'people',
                '#4ECDC4'
              )}
              {renderOverviewCard(
                'Avg Completion',
                `${analytics.overallStats.averageCompletion.toFixed(1)}%`,
                'Average completion rate',
                'checkmark-circle',
                '#FF6B6B'
              )}
              {renderOverviewCard(
                'Active Users',
                analytics.userEngagement.activeUsers,
                'Currently active users',
                'pulse',
                '#FFD166'
              )}
            </View>
          </View>

          {/* User Engagement */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>User Engagement</Text>
            <View style={styles.engagementContainer}>
              <View style={styles.engagementCard}>
                <Text style={styles.engagementValue}>{analytics.userEngagement.completedPlans}</Text>
                <Text style={styles.engagementLabel}>Completed Plans</Text>
              </View>
              <View style={styles.engagementCard}>
                <Text style={styles.engagementValue}>{analytics.userEngagement.averageProgress.toFixed(1)}%</Text>
                <Text style={styles.engagementLabel}>Avg Progress</Text>
              </View>
              <View style={styles.engagementCard}>
                <Text style={styles.engagementValue}>{analytics.userEngagement.retentionRate.toFixed(1)}%</Text>
                <Text style={styles.engagementLabel}>Retention Rate</Text>
              </View>
            </View>
          </View>

          {/* Top Performers */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Top Performing Plans</Text>
            <View style={styles.topPerformersContainer}>
              {analytics.overallStats.mostPopularPlan && (
                <View style={styles.topPerformerCard}>
                  <Ionicons name="star" size={32} color="#FFD166" />
                  <Text style={styles.topPerformerTitle}>Most Popular</Text>
                  <Text style={styles.topPerformerName}>{analytics.overallStats.mostPopularPlan.title}</Text>
                  <Text style={styles.topPerformerStat}>{analytics.overallStats.mostPopularPlan.enrollments} enrollments</Text>
                </View>
              )}
              {analytics.overallStats.bestPerformingPlan && (
                <View style={styles.topPerformerCard}>
                  <Ionicons name="trophy" size={32} color="#4ECDC4" />
                  <Text style={styles.topPerformerTitle}>Best Performing</Text>
                  <Text style={styles.topPerformerName}>{analytics.overallStats.bestPerformingPlan.title}</Text>
                  <Text style={styles.topPerformerStat}>{analytics.overallStats.bestPerformingPlan.completionRate.toFixed(1)}% completion</Text>
                </View>
              )}
            </View>
          </View>

          {/* Plan Performance List */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Plan Performance</Text>
            {analytics.planStats.length > 0 ? (
              analytics.planStats.map((plan, index) => renderPlanCard(plan, index))
            ) : (
              <View style={styles.emptyState}>
                <Ionicons name="document-text" size={64} color="#95a5a6" />
                <Text style={styles.emptyStateText}>No plan data available</Text>
              </View>
            )}
          </View>
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  particle1: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderRadius: 12,
    left: width * 0.1,
    top: 120,
    zIndex: 0,
  },
  particle2: {
    position: 'absolute',
    width: 18,
    height: 18,
    borderRadius: 9,
    left: width * 0.8,
    top: 200,
    zIndex: 0,
  },
  particle3: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    left: width * 0.2,
    top: 500,
    zIndex: 0,
  },
  particleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  floatingShape1: {
    position: 'absolute',
    width: 32,
    height: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    left: width * 0.85,
    top: 100,
    zIndex: 0,
  },
  floatingShape2: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    left: width * 0.05,
    top: 600,
    zIndex: 0,
  },
  glowCircle: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 20,
    elevation: 10,
    zIndex: 0,
  },
  container: {
    flex: 1,
    paddingTop: 50,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  refreshButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  content: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  overviewCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    width: (width - 50) / 2,
    marginBottom: 15,
    borderLeftWidth: 4,
  },
  overviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  overviewValue: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
  },
  overviewTitle: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#7f8c8d',
    marginBottom: 4,
  },
  overviewSubtitle: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#95a5a6',
  },
  engagementContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  engagementCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    width: (width - 60) / 3,
    alignItems: 'center',
  },
  engagementValue: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: 5,
  },
  engagementLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    textAlign: 'center',
    marginBottom: 10,
  },
  topPerformersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  topPerformerCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: (width - 50) / 2,
    alignItems: 'center',
  },
  topPerformerTitle: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#7f8c8d',
    marginTop: 10,
    marginBottom: 5,
  },
  topPerformerName: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    textAlign: 'center',
    marginBottom: 5,
  },
  topPerformerStat: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#95a5a6',
    textAlign: 'center',
  },
  planCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  planHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  planRank: {
    backgroundColor: '#4ECDC4',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  rankNumber: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  planInfo: {
    flex: 1,
  },
  planTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  planCategory: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
  },
  planMetrics: {
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
  },
  metricLabel: {
    fontSize: 10,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
  },
  planStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
    paddingVertical: 10,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#ecf0f1',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
  },
  statLabel: {
    fontSize: 10,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
  },
  progressSection: {
    alignItems: 'center',
  },
  progressLabel: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: '#7f8c8d',
    marginBottom: 8,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    color: '#4ECDC4',
    marginTop: 5,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    marginTop: 10,
  },
  bottomSpacing: {
    height: 50,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginTop: 10,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: 5,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#667eea',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  safeArea: {
    flex: 1,
    backgroundColor: 'transparent',
  },
});