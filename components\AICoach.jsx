// This file is used in the project (imported by app/ai-coach.jsx and homepage.jsx). Do NOT delete.
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Modal,
  ScrollView,
  TextInput,
  Dimensions,
  Image,
  Linking,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import AIWorkoutGenerator from './AIWorkoutGenerator';
import { demoExercises, handleVideoTutorial } from '../constants/data';
import Constants from 'expo-constants';
import { useFonts, Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold } from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';

const { width, height } = Dimensions.get('window');

const AICoach = ({ userEmail, onWorkoutGenerated, userProfile, onGenerateWorkout }) => {
  const [isActive, setIsActive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [chatVisible, setChatVisible] = useState(false);
  const [chatMessages, setChatMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [activityRecognition, setActivityRecognition] = useState('idle');
  const [workoutGeneratorVisible, setWorkoutGeneratorVisible] = useState(false);
  const [selectedExercise, setSelectedExercise] = useState(null);
  const [lastUserQuestion, setLastUserQuestion] = useState('');
  const [conversationHistory, setConversationHistory] = useState([]);
  const AI_API_KEY = Constants.manifest?.extra?.OPENAI_API_KEY;

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  useEffect(() => {
    if (isActive) {
      startActivityRecognition();
    } else {
      stopActivityRecognition();
    }
  }, [isActive]);

  const startActivityRecognition = () => {
    const interval = setInterval(() => {
      recognizeActivity();
    }, 3000);
    
    return () => clearInterval(interval);
  };

  const stopActivityRecognition = () => {
    // Stop activity recognition
  };

  const recognizeActivity = () => {
    const activities = ['walking', 'running', 'cycling', 'jumping', 'squatting', 'idle'];
    const weights = [0.2, 0.15, 0.1, 0.1, 0.1, 0.35]; // Higher probability for idle
    
    let random = Math.random();
    let cumulativeWeight = 0;
    
    for (let i = 0; i < activities.length; i++) {
      cumulativeWeight += weights[i];
      if (random <= cumulativeWeight) {
        setActivityRecognition(activities[i]);
        break;
      }
    }
  };

  const generateAIWorkout = async () => {
    setLoading(true);
    try {
      // Simulate AI workout generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const workoutTypes = ['HIIT', 'Strength', 'Cardio', 'Yoga', 'Pilates', 'CrossFit'];
      const intensities = ['Beginner', 'Intermediate', 'Advanced'];
      const durations = [15, 20, 30, 45, 60];
      
      const workout = {
        id: Date.now().toString(),
        type: workoutTypes[Math.floor(Math.random() * workoutTypes.length)],
        intensity: intensities[Math.floor(Math.random() * intensities.length)],
        duration: durations[Math.floor(Math.random() * durations.length)],
        exercises: generateExercises(),
        aiTip: generateAITip(),
        caloriesBurn: Math.floor(Math.random() * 300) + 200,
        createdAt: new Date().toISOString(),
      };

      // Save workout to storage
      await saveWorkout(workout);
      
      if (onWorkoutGenerated) {
        onWorkoutGenerated(workout);
      }

      Alert.alert(
        '🤖 AI Coach Recommendation',
        `I've created a personalized ${workout.duration}-minute ${workout.type} workout at ${workout.intensity} level.\n\n💡 ${workout.aiTip}`,
        [
          { text: 'View Details', onPress: () => showWorkoutDetails(workout) },
          { text: 'Start Now', onPress: () => startWorkout(workout) }
        ]
      );
    } catch (error) {
      console.error('AI workout generation error:', error);
      Alert.alert('AI Coach Offline', 'Unable to generate workout. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleCustomWorkout = () => {
    setWorkoutGeneratorVisible(true);
  };

  const generateExercises = () => {
    const allExercises = [
      'Push-ups', 'Squats', 'Lunges', 'Plank', 'Burpees', 'Mountain Climbers',
      'Jumping Jacks', 'High Knees', 'Russian Twists', 'Deadlifts', 'Pull-ups',
      'Dips', 'Wall Sits', 'Bicycle Crunches', 'Jump Squats'
    ];
    
    const exerciseCount = Math.floor(Math.random() * 4) + 3; // 3-6 exercises
    const selectedExercises = [];
    
    for (let i = 0; i < exerciseCount; i++) {
      const randomIndex = Math.floor(Math.random() * allExercises.length);
      const exercise = allExercises[randomIndex];
      if (!selectedExercises.includes(exercise)) {
        selectedExercises.push(exercise);
      }
    }
    
    return selectedExercises;
  };

  const generateAITip = () => {
    const tips = [
      'Focus on proper form over speed. Quality movements lead to better results!',
      'Remember to breathe consistently throughout each exercise.',
      'Stay hydrated and listen to your body during the workout.',
      'Warm up properly before starting and cool down afterwards.',
      'Progressive overload is key - gradually increase intensity over time.',
      'Rest is just as important as training for muscle recovery.',
      'Consistency beats perfection - show up every day!',
    ];
    
    return tips[Math.floor(Math.random() * tips.length)];
  };

  const saveWorkout = async (workout) => {
    try {
      const key = `@ai_workouts:${userEmail}`;
      const existingWorkouts = await AsyncStorage.getItem(key);
      const workouts = existingWorkouts ? JSON.parse(existingWorkouts) : [];
      
      workouts.unshift(workout); // Add to beginning
      
      // Keep only last 10 workouts
      if (workouts.length > 10) {
        workouts.splice(10);
      }
      
      await AsyncStorage.setItem(key, JSON.stringify(workouts));
    } catch (error) {
      console.error('Error saving workout:', error);
    }
  };

  const showWorkoutDetails = (workout) => {
    Alert.alert(
      `${workout.type} Workout Details`,
      `Duration: ${workout.duration} minutes\nIntensity: ${workout.intensity}\nCalories: ~${workout.caloriesBurn}\n\nExercises:\n${workout.exercises.map((ex, i) => `${i + 1}. ${ex}`).join('\n')}\n\n💡 ${workout.aiTip}`,
      [
        { text: 'Close', style: 'cancel' },
        { text: 'Start Workout', onPress: () => startWorkout(workout) }
      ]
    );
  };

  const startWorkout = (workout) => {
    setIsActive(true);
    Alert.alert(
      '🚀 Workout Started!',
      `Your ${workout.type} workout is now active. AI coaching is enabled.\n\n📱 Features Active:\n• Real-time form guidance\n• Activity recognition\n• Progress tracking\n• Voice coaching`,
      [{ text: 'Let\'s Go!', style: 'default' }]
    );
  };

  const openVideoTutorial = async (exercise) => {
    try {
      const videoUrl = handleVideoTutorial(exercise.name, exercise.videoUrl);
      const supported = await Linking.canOpenURL(videoUrl);
      if (supported) {
        await Linking.openURL(videoUrl);
      } else {
        Alert.alert('Error', 'Cannot open video tutorial');
      }
    } catch (error) {
      console.error('Error opening video:', error);
      Alert.alert('Error', 'Failed to open video tutorial');
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;
    setLastUserQuestion(inputMessage);
    const userMessage = {
      id: Date.now(),
      text: inputMessage,
      sender: 'user',
      timestamp: new Date().toISOString(),
    };
    setChatMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    
    // Show typing indicator
    const typingMessage = {
      id: Date.now() + 0.5,
      text: '...',
      sender: 'ai',
      isTyping: true,
      timestamp: new Date().toISOString(),
    };
    setChatMessages(prev => [...prev, typingMessage]);
    
    // Update conversation history (keep last 5 exchanges)
    setConversationHistory(prev => {
      const updated = [...prev, { role: 'user', content: inputMessage }];
      return updated.length > 10 ? updated.slice(updated.length - 10) : updated;
    });
    
    // Generate AI response
    let aiResponse = '';
    
    try {
      // Process the user's message to determine the appropriate response
      const lowerInput = inputMessage.toLowerCase();
      
      // Check for workout-related questions
      if (lowerInput.includes('workout') || lowerInput.includes('exercise') || lowerInput.includes('routine')) {
        aiResponse = generateWorkoutResponse(lowerInput);
      } 
      // Check for nutrition-related questions
      else if (lowerInput.includes('eat') || lowerInput.includes('food') || lowerInput.includes('diet') || 
               lowerInput.includes('nutrition') || lowerInput.includes('protein') || lowerInput.includes('calorie')) {
        aiResponse = generateNutritionResponse(lowerInput);
      }
      // Check for motivation-related questions
      else if (lowerInput.includes('motivat') || lowerInput.includes('lazy') || 
               lowerInput.includes('give up') || lowerInput.includes('don\'t feel like')) {
        aiResponse = generateMotivationResponse();
      }
      // Check for injury or pain-related questions
      else if (lowerInput.includes('hurt') || lowerInput.includes('pain') || 
               lowerInput.includes('injur') || lowerInput.includes('strain')) {
        aiResponse = generateInjuryResponse();
      }
      // Check for specific exercise questions
      else if (checkForExerciseQuestion(lowerInput)) {
        aiResponse = generateExerciseInstructions(lowerInput);
      }
      // Check for goal-related questions
      else if (lowerInput.includes('goal') || lowerInput.includes('target') || 
               lowerInput.includes('aim') || lowerInput.includes('want to')) {
        aiResponse = generateGoalResponse(lowerInput);
      }
      // Check for progress-related questions
      else if (lowerInput.includes('progress') || lowerInput.includes('improve') || 
               lowerInput.includes('better') || lowerInput.includes('track')) {
        aiResponse = generateProgressResponse();
      }
      // Check for recovery-related questions
      else if (lowerInput.includes('recover') || lowerInput.includes('rest') || 
               lowerInput.includes('sore') || lowerInput.includes('sleep')) {
        aiResponse = generateRecoveryResponse();
      }
      // Check for equipment-related questions
      else if (lowerInput.includes('equipment') || lowerInput.includes('gear') || 
               lowerInput.includes('machine') || lowerInput.includes('weight')) {
        aiResponse = generateEquipmentResponse(lowerInput);
      }
      // Check for time-related questions
      else if (lowerInput.includes('time') || lowerInput.includes('long') || 
               lowerInput.includes('duration') || lowerInput.includes('minutes') || 
               lowerInput.includes('hours')) {
        aiResponse = generateTimeResponse(lowerInput);
      }
      // Check for greeting
      else if (lowerInput.includes('hello') || lowerInput.includes('hi') || 
               lowerInput.includes('hey') || lowerInput === 'yo') {
        aiResponse = "Hello! I'm your AI fitness coach. How can I help you with your fitness journey today?";
      }
      // Default response for other questions
      else {
        aiResponse = generateGeneralResponse();
      }
      
      // Add personalization
      if (userProfile && userProfile.displayName) {
        // 30% chance to include the user's name
        if (Math.random() < 0.3) {
          aiResponse = `${userProfile.displayName}, ${aiResponse.charAt(0).toLowerCase()}${aiResponse.slice(1)}`;
        }
      }
      
      // Add follow-up question 50% of the time
      if (Math.random() < 0.5) {
        const followUps = [
          "Is there anything specific you'd like to know about this?",
          "Do you have any other questions about your fitness routine?",
          "Would you like more detailed information on this topic?",
          "How does that sound to you?",
          "Is there a particular aspect of this you'd like to focus on?",
          "Have you tried this approach before?",
          "What's your experience with this so far?",
          "Does this align with your fitness goals?"
        ];
        aiResponse += " " + followUps[Math.floor(Math.random() * followUps.length)];
      }
      
    } catch (err) {
      console.error('Error generating AI response:', err);
      aiResponse = "I'm sorry, I encountered an issue while processing your question. Could you try asking in a different way?";
    }
    
    // Simulate typing delay based on response length
    const typingDelay = Math.min(1000 + aiResponse.length * 10, 3000);
    
    setTimeout(() => {
      // Remove typing indicator and add real response
      setChatMessages(prev => prev.filter(msg => !msg.isTyping));
      
      const aiMessage = {
        id: Date.now() + 1,
        text: aiResponse,
        sender: 'ai',
        timestamp: new Date().toISOString(),
      };
      
      setChatMessages(prev => [...prev, aiMessage]);
      
      // Update conversation history
      setConversationHistory(prev => {
        const updated = [...prev, { role: 'assistant', content: aiResponse }];
        return updated.length > 10 ? updated.slice(updated.length - 10) : updated;
      });
      
      // Save conversation to AsyncStorage
      saveConversation([...conversationHistory, 
        { role: 'user', content: inputMessage },
        { role: 'assistant', content: aiResponse }
      ]);
      
    }, typingDelay);
  };
  
  // Helper function to save conversation to AsyncStorage
  const saveConversation = async (conversation) => {
    if (!userEmail) return;
    try {
      await AsyncStorage.setItem(`@ai_coach_conversation:${userEmail}`, JSON.stringify(conversation));
    } catch (error) {
      console.error('Error saving conversation:', error);
    }
  };
  
  // Helper function to check if the question is about a specific exercise
  const checkForExerciseQuestion = (input) => {
    const exercises = [
      'push-up', 'pushup', 'squat', 'lunge', 'plank', 'burpee', 'deadlift', 'bench press',
      'pull-up', 'pullup', 'crunch', 'sit-up', 'situp', 'mountain climber', 'jumping jack',
      'bicep curl', 'tricep extension', 'shoulder press', 'leg press', 'calf raise'
    ];
    
    return exercises.some(exercise => input.includes(exercise));
  };
  
  // Generate response for workout-related questions
  const generateWorkoutResponse = (input) => {
    const workoutResponses = [
      "For an effective workout routine, I recommend combining strength training, cardio, and flexibility exercises. Aim for 3-5 sessions per week, alternating between different muscle groups to allow for recovery.",
      "A balanced workout plan should include both cardio and strength training. Start with a 5-10 minute warm-up, then do 20-30 minutes of your main workout, and finish with a 5-minute cool-down and stretching.",
      "For beginners, I suggest starting with bodyweight exercises like push-ups, squats, and planks. As you build strength, you can gradually add resistance with weights or bands.",
      "HIIT (High-Intensity Interval Training) is great for burning calories and improving cardiovascular health. Try 30 seconds of intense work followed by 30 seconds of rest, repeated for 15-20 minutes.",
      "For muscle building, focus on compound exercises that work multiple muscle groups, like squats, deadlifts, and bench presses. Aim for 3-4 sets of 8-12 reps with adequate weight.",
      "A full-body workout 3 times a week is perfect for beginners. Include exercises for all major muscle groups: legs, chest, back, shoulders, arms, and core.",
      "For weight loss, combine strength training with cardio. The strength training builds muscle, which increases your metabolism, while cardio burns additional calories."
    ];
    
    if (input.includes('home') || input.includes('no equipment')) {
      return "For a great home workout without equipment, try this circuit: 15 push-ups, 20 squats, 10 lunges per leg, 30-second plank, and 30 jumping jacks. Rest for 1 minute, then repeat 3-5 times. This full-body workout targets all major muscle groups and gets your heart rate up.";
    }
    
    if (input.includes('beginner')) {
      return "For beginners, start with this simple routine 3 times a week: 10 squats, 5-10 push-ups (modified if needed), 10 walking lunges, 15-second plank, and 30 seconds of marching in place. Rest as needed between exercises. Focus on proper form rather than speed or reps.";
    }
    
    if (input.includes('advanced')) {
      return "For advanced fitness enthusiasts, try this challenging routine: 4 sets of 5 exercises (pull-ups, pistol squats, handstand push-ups, plyometric lunges, and L-sits), with minimal rest between exercises and 2 minutes between sets. This targets strength, power, and endurance simultaneously.";
    }
    
    return workoutResponses[Math.floor(Math.random() * workoutResponses.length)];
  };
  
  // Generate response for nutrition-related questions
  const generateNutritionResponse = (input) => {
    if (input.includes('protein')) {
      return "Protein is essential for muscle repair and growth. Aim for 1.6-2.2g per kg of bodyweight daily if you're active. Good sources include lean meats, fish, eggs, dairy, legumes, and plant-based options like tofu and tempeh.";
    }
    
    if (input.includes('carb') || input.includes('carbohydrate')) {
      return "Carbohydrates are your body's primary energy source, especially important for high-intensity workouts. Focus on complex carbs like whole grains, fruits, vegetables, and legumes. Time your carb intake around your workouts for optimal performance and recovery.";
    }
    
    if (input.includes('fat')) {
      return "Healthy fats are crucial for hormone production and vitamin absorption. Include sources like avocados, nuts, seeds, olive oil, and fatty fish. Aim for 20-35% of your daily calories from primarily unsaturated fats.";
    }
    
    if (input.includes('calorie') || input.includes('weight loss')) {
      return "For weight loss, create a moderate calorie deficit of 300-500 calories per day through a combination of diet and exercise. Track your food intake, prioritize protein and fiber-rich foods that keep you full, and stay hydrated. Remember that sustainable weight loss is typically 0.5-1kg per week.";
    }
    
    if (input.includes('meal') || input.includes('plan')) {
      return "A balanced meal plan should include lean proteins, complex carbohydrates, healthy fats, and plenty of fruits and vegetables. Try to eat 3-4 hours apart, with a mix of larger meals and smaller snacks depending on your schedule and workout timing. Meal prepping can help you stay consistent.";
    }
    
    const nutritionResponses = [
      "Nutrition is just as important as exercise for fitness results. Focus on whole foods, adequate protein, and proper hydration. Aim to include a variety of colors on your plate to ensure you're getting a wide range of nutrients.",
      "Post-workout nutrition is crucial for recovery. Try to consume a combination of protein and carbohydrates within 30-60 minutes after exercise to optimize muscle repair and glycogen replenishment.",
      "Staying hydrated is essential for performance. Drink water throughout the day, and consider electrolytes during longer or more intense workouts, especially in hot conditions.",
      "Eating enough calories is important, even when trying to lose weight. Too large a deficit can slow your metabolism and lead to muscle loss. Find the balance that supports your activity level while moving toward your goals."
    ];
    
    return nutritionResponses[Math.floor(Math.random() * nutritionResponses.length)];
  };
  
  // Generate response for motivation-related questions
  const generateMotivationResponse = () => {
    const motivationResponses = [
      "Remember why you started. Write down your fitness goals and keep them visible. On tough days, focus on doing just 5 minutes—often you'll continue once you've started.",
      "Try setting smaller, achievable goals to build momentum. Each small win creates positive reinforcement and builds the habit. Celebrate these victories, no matter how small.",
      "Find activities you genuinely enjoy. Exercise shouldn't feel like punishment—it should be something you look forward to. Experiment until you find what clicks for you.",
      "Consider finding a workout buddy or joining a community. Social accountability can be incredibly motivating, and sharing the journey makes it more enjoyable.",
      "Track your progress with metrics beyond the scale—like strength gains, endurance improvements, or how your clothes fit. These non-scale victories can be powerful motivators.",
      "Remember that motivation naturally fluctuates. On days when it's low, rely on discipline and established habits to carry you through. It's consistency, not perfection, that brings results.",
      "Visualize how you'll feel after your workout, not just how you feel before it. That post-exercise endorphin boost is real and worth pushing through the initial resistance."
    ];
    
    return motivationResponses[Math.floor(Math.random() * motivationResponses.length)];
  };
  
  // Generate response for injury-related questions
  const generateInjuryResponse = () => {
    const injuryResponses = [
      "If you're experiencing sharp pain (not just muscle soreness), it's important to rest and avoid movements that aggravate it. Consider the RICE method: Rest, Ice, Compression, and Elevation. If pain persists for more than a few days, consult a healthcare professional.",
      "Distinguish between muscle soreness and injury pain. Soreness typically feels dull, affects both sides equally, peaks 24-72 hours after exercise, and gradually improves. Injury pain is often sharp, may involve swelling, and doesn't improve with continued movement.",
      "Prevention is key for injuries. Always warm up properly, use appropriate form, increase intensity gradually, and include recovery days in your routine. Strengthening supporting muscles, especially your core, can also help prevent many common injuries.",
      "For minor strains, gentle movement within a pain-free range can promote healing, but avoid pushing through pain. Gradually reintroduce activity as you heal, and consider working with a physical therapist for proper rehabilitation.",
      "Listen to your body's signals. Pain is a warning system—ignoring it can lead to more serious injuries. It's better to take a few days off than be forced to take months off due to a worsening condition."
    ];
    
    return injuryResponses[Math.floor(Math.random() * injuryResponses.length)];
  };
  
  // Generate instructions for specific exercises
  const generateExerciseInstructions = (input) => {
    if (input.includes('push-up') || input.includes('pushup')) {
      return "For a proper push-up: Start in a plank position with hands slightly wider than shoulders. Keep your body in a straight line from head to heels. Lower your chest toward the ground by bending your elbows, keeping them at about a 45-degree angle from your body. Push back up to the starting position. If this is too challenging, start with modified push-ups from your knees.";
    }
    
    if (input.includes('squat')) {
      return "For a proper squat: Stand with feet shoulder-width apart, toes slightly turned out. Keep your chest up and core engaged. Bend your knees and push your hips back as if sitting in a chair. Lower until your thighs are parallel to the ground (or as low as you can with good form). Keep your knees tracking over your toes, not caving inward. Push through your heels to return to standing.";
    }
    
    if (input.includes('plank')) {
      return "For a proper plank: Start in a push-up position, then lower onto your forearms. Elbows should be directly under your shoulders. Keep your body in a straight line from head to heels—don't let your hips sag or pike up. Engage your core by drawing your navel toward your spine. Hold this position while breathing normally. Start with 20-30 seconds and gradually increase your time.";
    }
    
    if (input.includes('lunge')) {
      return "For proper lunges: Stand tall with feet hip-width apart. Step forward with one leg and lower your body until both knees are bent at 90-degree angles. Your front knee should be aligned with your ankle, not pushing past your toes. Your back knee should hover just above the ground. Push through the heel of your front foot to return to standing. Alternate legs or complete all reps on one side before switching.";
    }
    
    if (input.includes('deadlift')) {
      return "For a proper deadlift: Stand with feet hip-width apart, with the barbell (or weights) over your mid-foot. Hinge at your hips, keeping your back flat and chest up. Grab the bar with hands just outside your legs. Push through your heels, keeping the bar close to your body as you stand up. Keep your core engaged throughout the movement. Lower the weight by hinging at the hips and bending the knees. This exercise requires good form, so consider working with a trainer initially.";
    }
    
    return "To perform this exercise correctly, focus on proper form rather than speed or weight. Start with a lighter weight to master the technique. Engage the target muscles consciously, use controlled movements, and maintain proper breathing throughout. If you'd like specific instructions for a particular exercise, please let me know which one.";
  };
  
  // Generate response for goal-related questions
  const generateGoalResponse = (input) => {
    if (input.includes('weight loss') || input.includes('lose weight')) {
      return "For weight loss, combine regular exercise with a slight calorie deficit. Include both strength training (to preserve muscle) and cardio (for calorie burning). Aim for 3-5 workouts per week, mixing high-intensity intervals with steady-state cardio. Remember that nutrition plays a major role—focus on whole foods, adequate protein, and portion control.";
    }
    
    if (input.includes('muscle') || input.includes('strength') || input.includes('stronger')) {
      return "For building muscle and strength, focus on progressive overload—gradually increasing the weight, reps, or sets over time. Prioritize compound movements like squats, deadlifts, bench press, and rows. Aim for 3-4 strength sessions per week, allowing muscle groups 48 hours to recover. Ensure you're eating enough calories and protein (1.6-2.2g per kg of bodyweight) to support muscle growth.";
    }
    
    if (input.includes('endurance') || input.includes('stamina') || input.includes('marathon')) {
      return "To improve endurance, gradually increase your training volume over time. Mix longer, steady-state cardio sessions with interval training. For running, follow the 10% rule—don't increase your weekly mileage by more than 10% at a time. Include cross-training to reduce injury risk, and don't neglect strength training, which can improve running economy and prevent imbalances.";
    }
    
    if (input.includes('tone') || input.includes('toning')) {
      return "Muscle 'toning' is actually a combination of building some muscle and reducing body fat to make that muscle visible. Include strength training with moderate weights (8-12 reps per set), high-intensity interval training for fat loss, and proper nutrition with adequate protein. Contrary to common belief, lifting weights won't make you bulky unless that's specifically your goal with appropriate training and nutrition.";
    }
    
    if (input.includes('flexibility') || input.includes('mobile') || input.includes('stretch')) {
      return "To improve flexibility and mobility, incorporate dedicated stretching sessions 2-3 times per week, holding each stretch for 30-60 seconds. Dynamic stretching is best before workouts, while static stretching works well after exercise or as standalone sessions. Yoga and mobility drills can also be excellent additions to your routine. Consistency is key—flexibility improves gradually over time.";
    }
    
    const goalResponses = [
      "Setting SMART goals is crucial for fitness success. Make sure your goals are Specific, Measurable, Achievable, Relevant, and Time-bound. Instead of 'get fit,' try 'be able to do 10 push-ups in a row within 8 weeks.'",
      "Consider setting both process and outcome goals. Outcome goals focus on the result (like losing 5kg), while process goals focus on the behaviors that will get you there (like exercising 4 times per week). Process goals give you more daily control and satisfaction.",
      "Balance your fitness goals across different domains—strength, endurance, flexibility, and skill. This creates a well-rounded fitness profile and helps prevent plateaus and overuse injuries.",
      "Remember that realistic timelines are important for sustainable progress. Fitness adaptations take time—muscle building, fat loss, and endurance improvements all follow their own timelines. Patience and consistency will get you there."
    ];
    
    return goalResponses[Math.floor(Math.random() * goalResponses.length)];
  };
  
  // Generate response for progress-related questions
  const generateProgressResponse = () => {
    const progressResponses = [
      "Track multiple metrics to gauge your progress—not just weight. Consider taking measurements, progress photos, performance metrics (like weights lifted or running times), energy levels, and how clothes fit. This gives you a more complete picture of your improvements.",
      "If you've hit a plateau, try changing one variable in your routine. This could be increasing intensity, changing exercises, adjusting volume, or modifying your rest periods. Sometimes, your body simply adapts to the same stimulus over time.",
      "Progress isn't always linear. Expect fluctuations and occasional plateaus—they're normal parts of the fitness journey. Focus on the overall trend rather than day-to-day or week-to-week variations.",
      "Consider keeping a workout journal to track your exercises, sets, reps, and weights. This helps you implement progressive overload systematically and see patterns in your performance. Many fitness apps can help with this tracking.",
      "Remember that rest and recovery are essential for progress. Sometimes taking a deload week (reducing intensity or volume) can help break through plateaus by allowing your body to fully recover and come back stronger."
    ];
    
    return progressResponses[Math.floor(Math.random() * progressResponses.length)];
  };
  
  // Generate response for recovery-related questions
  const generateRecoveryResponse = () => {
    const recoveryResponses = [
      "Quality sleep is one of the most important recovery tools. Aim for 7-9 hours per night, as this is when most muscle repair and growth occurs. Create a consistent sleep schedule and optimize your sleep environment for quality rest.",
      "Active recovery—like light walking, swimming, or yoga—can enhance recovery by increasing blood flow to muscles without adding stress. Consider incorporating active recovery days between more intense training sessions.",
      "Proper nutrition plays a key role in recovery. Protein helps repair muscle tissue, carbohydrates replenish glycogen stores, and anti-inflammatory foods can help manage exercise-induced inflammation. Stay hydrated as well.",
      "Consider recovery techniques like foam rolling, massage, contrast therapy (alternating hot and cold), or gentle stretching. These can help reduce muscle tension and improve circulation to healing tissues.",
      "Listen to your body's signals. Persistent fatigue, decreased performance, irritability, or ongoing soreness can indicate that you need more recovery time. Sometimes taking an extra rest day is the most productive thing you can do."
    ];
    
    return recoveryResponses[Math.floor(Math.random() * recoveryResponses.length)];
  };
  
  // Generate response for equipment-related questions
  const generateEquipmentResponse = (input) => {
    if (input.includes('home') || input.includes('minimal')) {
      return "For an effective home gym with minimal equipment, consider: a pair of adjustable dumbbells, resistance bands of varying strengths, a stability ball, and a yoga mat. These versatile tools allow for hundreds of exercises targeting all major muscle groups. As you progress, you might add a pull-up bar or kettlebell for more options.";
    }
    
    if (input.includes('weight') || input.includes('dumbbell') || input.includes('barbell')) {
      return "When choosing weights, start lighter than you think and focus on proper form. For beginners, adjustable dumbbells offer versatility and save space. If you're considering barbells, ensure you have appropriate safety equipment like a rack with spotters or safety arms. Progressive overload is key, so having a range of weights or adjustable options is valuable.";
    }
    
    if (input.includes('cardio') || input.includes('machine')) {
      return "For cardio equipment, consider what you'll enjoy using consistently. Treadmills offer versatile walking/running options, ellipticals and stationary bikes are lower impact, and rowing machines provide full-body workouts. If space or budget is limited, a jump rope is an incredibly effective, portable cardio tool that costs very little.";
    }
    
    const equipmentResponses = [
      "Remember that equipment doesn't make or break your fitness journey—consistency and effort do. Many effective exercises require just your body weight, and you can always add intensity through tempo changes, increased range of motion, or unilateral variations.",
      "When investing in fitness equipment, prioritize versatility and quality over quantity. A few well-made, multipurpose pieces will serve you better than many specialized, lower-quality items.",
      "Consider your specific goals when selecting equipment. Strength goals might require different tools than endurance or flexibility goals. Your available space and budget are also important factors in building your fitness toolkit."
    ];
    
    return equipmentResponses[Math.floor(Math.random() * equipmentResponses.length)];
  };
  
  // Generate response for time-related questions
  const generateTimeResponse = (input) => {
    if (input.includes('quick') || input.includes('short') || input.includes('busy')) {
      return "Even short workouts can be effective. Try a 10-minute HIIT circuit: 30 seconds each of jumping jacks, push-ups, squats, mountain climbers, and plank, with minimal rest between exercises. Repeat twice. High intensity is key when time is limited—push yourself during those short intervals.";
    }
    
    if (input.includes('how long') || input.includes('how much time')) {
      return "The ideal workout duration depends on intensity and goals. Generally, aim for 150-300 minutes of moderate activity or 75-150 minutes of vigorous activity weekly, plus strength training twice weekly. This can be divided based on your schedule—5 30-minute sessions, 3 50-minute sessions, etc. Quality and consistency matter more than duration.";
    }
    
    if (input.includes('results') || input.includes('see change')) {
      return "Timeframes for visible results vary by individual and goal. Generally, you might notice strength improvements within 2-4 weeks, endurance changes in 4-6 weeks, and visible body composition changes in 6-12 weeks. Remember that internal improvements (like cardiovascular health) start immediately, even if not visible. Consistency is the key factor in seeing results.";
    }
    
    const timeResponses = [
      "When time is limited, focus on compound exercises that work multiple muscle groups simultaneously, like squats, deadlifts, push-ups, rows, and lunges. This gives you more bang for your buck compared to isolation exercises.",
      "Consider splitting your exercise into smaller chunks throughout the day if you can't find a larger block of time. Three 10-minute sessions can be as effective as one 30-minute session for many health markers.",
      "The best workout schedule is one you can maintain consistently. Whether that's 3 longer sessions or 5 shorter ones per week, consistency over time yields results. Find what fits your lifestyle and preferences."
    ];
    
    return timeResponses[Math.floor(Math.random() * timeResponses.length)];
  };
  
  // Generate general response for other questions
  const generateGeneralResponse = () => {
    const generalResponses = [
      "A balanced fitness approach includes strength training, cardiovascular exercise, flexibility work, and adequate recovery. Try to incorporate all these elements into your weekly routine for optimal health and fitness.",
      "Remember that fitness is a journey, not a destination. Focus on building sustainable habits rather than seeking quick fixes. Small, consistent actions compound over time to create significant results.",
      "Your fitness journey is unique to you. While it's good to learn from others, avoid comparing your progress to someone else's. Focus on becoming better than your previous self, not better than someone else.",
      "Consider working with a certified fitness professional, at least initially, to establish proper form and a suitable program for your specific needs and goals. This investment can prevent injuries and accelerate your progress.",
      "Fitness benefits extend far beyond physical appearance—improved mood, energy, sleep quality, cognitive function, and reduced disease risk are all powerful reasons to stay active, regardless of aesthetic changes.",
      "Try to find joy in the process of exercise itself, rather than viewing it solely as a means to an end. When you enjoy your workouts, adherence becomes much easier, and results follow naturally.",
      "Remember that all movement counts. On days when a structured workout isn't possible, look for ways to increase your non-exercise activity—take the stairs, walk during phone calls, or do quick movement breaks during sedentary periods."
    ];
    
    return generalResponses[Math.floor(Math.random() * generalResponses.length)];
  };

  const generateAIResponse = (userInput) => {
    const lowerInput = userInput.toLowerCase();
    // Categorized responses
    const responses = {
      general: [
      "Great question! I'd recommend focusing on proper form first, then gradually increasing intensity.",
      "Based on your fitness level, I suggest starting with 3 sets of 10-12 reps for strength training.",
      "Remember to stay hydrated and listen to your body. Rest days are just as important as training days!",
      "For optimal results, try to maintain consistency with your workouts. Even 20 minutes daily is better than nothing!",
      "I can help you create a personalized workout plan. What are your main fitness goals?",
        "Mixing up your workouts can help prevent plateaus and keep things interesting!",
        "Tracking your progress is a great way to stay motivated. Try logging your workouts!",
        "If you ever feel pain (not just soreness), stop and reassess your form or take a break.",
        "Warming up before and cooling down after exercise helps prevent injury and aids recovery.",
        "Don't forget to stretch! Flexibility is important for overall fitness.",
      ],
      nutrition: [
        "A balanced diet with enough protein, carbs, and healthy fats is key for fitness progress.",
        "Try to include a variety of fruits and vegetables in your meals for optimal nutrition.",
        "Staying hydrated is crucial—aim for at least 2 liters of water a day.",
        "If you're looking to lose weight, a slight calorie deficit and regular exercise is effective.",
        "For muscle gain, make sure you're eating enough protein and total calories.",
        "Meal prepping can help you stick to your nutrition goals!",
        "Don't skip breakfast—it's important for energy and metabolism.",
        "Healthy snacks like nuts, yogurt, or fruit can keep you energized between meals.",
        "Consult a registered dietitian for personalized nutrition advice.",
        "Micronutrients like vitamins and minerals are just as important as macros!",
      ],
      motivation: [
        "Remember, progress takes time—celebrate small wins along the way!",
        "Stay consistent, even on days you don't feel like it. Your future self will thank you!",
        "Set realistic goals and track your achievements to stay motivated.",
        "Find a workout buddy or community for extra accountability and fun!",
        "Visualize your goals and remind yourself why you started.",
        "Motivation gets you started, but discipline keeps you going!",
        "If you miss a day, don't stress—just get back on track tomorrow!",
      ],
      recovery: [
        "Rest and recovery are just as important as your workouts.",
        "Aim for 7-9 hours of sleep each night to help your body recover.",
        "Active recovery, like walking or gentle yoga, can help reduce soreness.",
        "Foam rolling and stretching can speed up muscle recovery.",
        "Listen to your body—if you're feeling run down, take a rest day.",
        "Proper hydration and nutrition aid recovery.",
        "If soreness persists for more than 3 days, consider consulting a professional.",
      ],
      injury: [
        "If you experience sharp pain, stop immediately and consult a professional.",
        "Proper form is key to preventing injuries. Don't rush your reps!",
        "Strengthening your core can help prevent many common injuries.",
        "Gradually increase intensity to avoid overuse injuries.",
        "Make sure you're wearing appropriate footwear for your activity.",
        "Warming up properly reduces injury risk significantly.",
        "Cross-training can help prevent overuse injuries from repetitive movements.",
      ],
    };
    
    // Determine which category to use based on keywords
    let category = 'general';
    if (lowerInput.includes('eat') || lowerInput.includes('food') || lowerInput.includes('diet') || lowerInput.includes('nutrition') || lowerInput.includes('protein') || lowerInput.includes('calorie')) {
      category = 'nutrition';
    } else if (lowerInput.includes('motivat') || lowerInput.includes('lazy') || lowerInput.includes('give up') || lowerInput.includes('don\'t feel like') || lowerInput.includes('goal')) {
      category = 'motivation';
    } else if (lowerInput.includes('recover') || lowerInput.includes('rest') || lowerInput.includes('sore') || lowerInput.includes('sleep') || lowerInput.includes('tired')) {
      category = 'recovery';
    } else if (lowerInput.includes('hurt') || lowerInput.includes('pain') || lowerInput.includes('injur') || lowerInput.includes('strain') || lowerInput.includes('sprain')) {
      category = 'injury';
    }
    
    // Return a random response from the selected category
    const categoryResponses = responses[category];
    return categoryResponses[Math.floor(Math.random() * categoryResponses.length)];
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.container}>
        <LinearGradient 
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} 
          style={styles.gradient}
        >
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#ffffff" />
            <Text style={styles.loadingText}>Loading AI Coach...</Text>
          </View>
        </LinearGradient>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient 
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} 
        style={styles.gradient}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>AI Coach</Text>
          <TouchableOpacity style={styles.chatButton} onPress={() => setChatVisible(true)}>
            <Ionicons name="chatbubble-ellipses" size={28} color="#fff" />
          </TouchableOpacity>
        </View>

        {/* Main Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Status Card */}
          <View style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <Ionicons name={isActive ? "fitness" : "fitness-outline"} size={24} color="#fff" />
              <Text style={styles.statusText}>
                {isActive ? 'AI Coach Active' : 'AI Coach Ready'}
              </Text>
            </View>
            {isActive && (
              <Text style={styles.activityText}>
                Current activity: {activityRecognition.charAt(0).toUpperCase() + activityRecognition.slice(1)}
              </Text>
            )}
          </View>

          {/* Action Buttons */}
          <View style={styles.actionsContainer}>
            <TouchableOpacity 
              style={[styles.actionButton, styles.primaryButton]} 
              onPress={generateAIWorkout}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <Ionicons name="flash" size={20} color="#fff" />
                  <Text style={styles.actionButtonText}>Quick Workout</Text>
                </>
              )}
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.actionButton, styles.secondaryButton]} 
              onPress={handleCustomWorkout}
            >
              <Ionicons name="options" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Custom Plan</Text>
            </TouchableOpacity>
          </View>

          {/* Recommended Exercises */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recommended Exercises</Text>
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingLeft: 5, paddingRight: 20 }}
            >
              {demoExercises.map((exercise, index) => (
                <TouchableOpacity 
                  key={index} 
                  style={styles.exerciseCard}
                  onPress={() => setSelectedExercise(exercise)}
                >
                  <Image 
                    source={{ uri: exercise.imageUrl }} 
                    style={styles.exerciseThumbnail}
                    resizeMode="cover"
                  />
                  <Text style={styles.exerciseTitle}>{exercise.name}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* Selected Exercise */}
          {selectedExercise && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Exercise Details</Text>
              <View style={styles.exerciseDetailCard}>
                <Image 
                  source={{ uri: selectedExercise.imageUrl }} 
                  style={styles.exerciseImage}
                  resizeMode="cover"
                />
                <View style={styles.exerciseInfo}>
                  <Text style={styles.exerciseName}>{selectedExercise.name}</Text>
                  <Text style={styles.exerciseTarget}>Target: {selectedExercise.target}</Text>
                  <View style={styles.exerciseButtons}>
                    <TouchableOpacity 
                      style={styles.backButton}
                      onPress={() => setSelectedExercise(null)}
                    >
                      <Ionicons name="arrow-back" size={16} color="#fff" />
                      <Text style={styles.backButtonText}>Back</Text>
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={styles.videoButton}
                      onPress={() => openVideoTutorial(selectedExercise)}
                    >
                      <Ionicons name="videocam" size={16} color="#fff" />
                      <Text style={styles.videoButtonText}>Watch Tutorial</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          )}

          {/* AI Tips */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>AI Coach Tips</Text>
            <View style={styles.tipsCard}>
              <Text style={styles.tipsTitle}>💡 Today's Fitness Insight</Text>
              <Text style={styles.tipsText}>
                {lastUserQuestion ? 
                  generateAIResponse(lastUserQuestion) : 
                  "Consistency is key to fitness success. Even a short daily workout is better than an occasional intense session. Try to establish a regular routine that fits your lifestyle."}
              </Text>
            </View>
          </View>
        </ScrollView>

        {/* Chat Modal */}
        <Modal
          visible={chatVisible}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setChatVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.chatContainer}>
              <View style={styles.chatHeader}>
                <Text style={styles.chatTitle}>Chat with AI Coach</Text>
                <TouchableOpacity onPress={() => setChatVisible(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              <ScrollView 
                style={styles.chatMessages}
                ref={ref => {
                  if (ref && chatMessages.length > 0) {
                    ref.scrollToEnd({ animated: true });
                  }
                }}
              >
                {chatMessages.length === 0 ? (
                  <Text style={styles.chatPlaceholder}>
                    Ask me anything about fitness, nutrition, or workout plans!
                  </Text>
                ) : (
                  chatMessages.map(message => (
                    <View
                      key={message.id}
                      style={[
                        styles.message,
                        message.sender === 'user' ? styles.userMessage : styles.aiMessage,
                        message.isTyping && styles.typingMessage
                      ]}
                    >
                      {message.isTyping ? (
                        <View style={styles.typingIndicator}>
                          <View style={styles.typingDot} />
                          <View style={[styles.typingDot, {animationDelay: '0.2s'}]} />
                          <View style={[styles.typingDot, {animationDelay: '0.4s'}]} />
                        </View>
                      ) : (
                        <Text style={[
                          styles.messageText, 
                          { color: message.sender === 'user' ? '#fff' : '#222' }
                        ]}>
                          {message.text}
                        </Text>
                      )}
                    </View>
                  ))
                )}
              </ScrollView>
              <View style={styles.chatInput}>
                <TextInput
                  style={styles.input}
                  value={inputMessage}
                  onChangeText={setInputMessage}
                  placeholder="Ask your AI coach..."
                  placeholderTextColor="#999"
                />
                <TouchableOpacity style={styles.sendButton} onPress={sendMessage}>
                  <Ionicons name="send" size={20} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* Workout Generator Modal */}
        <Modal
          visible={workoutGeneratorVisible}
          animationType="slide"
          onRequestClose={() => setWorkoutGeneratorVisible(false)}
        >
          <AIWorkoutGenerator
            onClose={() => setWorkoutGeneratorVisible(false)}
            onWorkoutGenerated={(workout) => {
              setWorkoutGeneratorVisible(false);
              if (onWorkoutGenerated) onWorkoutGenerated(workout);
            }}
            userProfile={userProfile}
          />
        </Modal>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp(6),
    paddingTop: Platform.OS === 'ios' ? hp(6) : hp(4),
    paddingBottom: hp(2),
  },
  headerTitle: {
    fontSize: wp(6),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  chatButton: {
    padding: wp(2),
  },
  content: {
    flex: 1,
    paddingHorizontal: wp(6),
    paddingBottom: hp(4),
  },
  statusCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: wp(5),
    marginBottom: hp(2.5),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  statusText: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginLeft: wp(2.5),
  },
  activityText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: hp(3),
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: hp(1.8),
    paddingHorizontal: wp(3),
    borderRadius: 12,
    marginHorizontal: wp(1),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  primaryButton: {
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    borderColor: '#667eea',
  },
  secondaryButton: {
    backgroundColor: 'rgba(118, 75, 162, 0.2)',
    borderColor: '#764ba2',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    marginLeft: wp(2),
  },
  section: {
    marginBottom: hp(3),
  },
  sectionTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(1.5),
  },
  exerciseCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: wp(3),
    marginRight: wp(3),
    width: wp(36),
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  exerciseThumbnail: {
    width: wp(30),
    height: hp(10),
    borderRadius: 8,
    marginBottom: hp(1),
  },
  exerciseTitle: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    textAlign: 'center',
  },
  exerciseDetailCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: wp(4),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  exerciseImage: {
    width: '100%',
    height: hp(20),
    borderRadius: 12,
    marginBottom: hp(1.5),
  },
  exerciseInfo: {
    width: '100%',
  },
  exerciseName: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(0.5),
  },
  exerciseTarget: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: hp(1.5),
  },
  exerciseButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: wp(3),
    paddingVertical: hp(1),
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  backButtonText: {
    color: '#fff',
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    marginLeft: wp(1),
  },
  videoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    paddingHorizontal: wp(3),
    paddingVertical: hp(1),
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#667eea',
  },
  videoButtonText: {
    color: '#fff',
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    marginLeft: wp(1),
  },
  tipsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: wp(4),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  tipsTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(1),
  },
  tipsText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: hp(2.5),
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  chatContainer: {
    flex: 1,
    backgroundColor: '#fff',
    marginTop: Platform.OS === 'ios' ? hp(6) : hp(4),
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  chatHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: wp(5),
    backgroundColor: '#667eea',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  chatTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  chatMessages: {
    flex: 1,
    padding: wp(5),
  },
  chatPlaceholder: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#999',
    textAlign: 'center',
    marginTop: hp(10),
  },
  message: {
    marginBottom: hp(1.5),
    maxWidth: '80%',
  },
  aiMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#f0f0f0',
    padding: wp(3),
    borderRadius: 12,
    borderBottomLeftRadius: 4,
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#667eea',
    padding: wp(3),
    borderRadius: 12,
    borderBottomRightRadius: 4,
  },
  messageText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
  },
  typingMessage: {
    backgroundColor: 'rgba(240, 240, 240, 0.7)',
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#667eea',
    marginHorizontal: 2,
    opacity: 0.6,
  },
  chatInput: {
    flexDirection: 'row',
    padding: wp(4),
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    paddingHorizontal: wp(4),
    paddingVertical: hp(1.2),
    marginRight: wp(2),
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
  },
  sendButton: {
    backgroundColor: '#667eea',
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    marginTop: hp(2),
  },
});

export default AICoach;