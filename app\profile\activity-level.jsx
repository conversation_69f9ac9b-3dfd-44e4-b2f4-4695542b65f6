import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  Platform,
  Dimensions,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useUser } from '../../context/UserContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StatusBar } from 'expo-status-bar';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { Animated as RNAnimated } from 'react-native';
import {
  useFonts,
  Poppins_700Bold,
  Poppins_400Regular,
  Poppins_600SemiBold,
} from '@expo-google-fonts/poppins';

const { width, height } = Dimensions.get('window');

const ACTIVITY_LEVELS = [
  { id: 'sedentary', label: 'Sedentary', icon: 'bed-outline', description: 'Little to no exercise' },
  { id: 'light', label: 'Light', icon: 'walk-outline', description: 'Light exercise 1-3 days' },
  { id: 'moderate', label: 'Moderate', icon: 'fitness-outline', description: 'Moderate exercise 3-5 days' },
  { id: 'active', label: 'Active', icon: 'flash-outline', description: 'Hard exercise 6-7 days' },
  { id: 'veryactive', label: 'Very Active', icon: 'flame-outline', description: 'Very hard exercise daily' },
];

const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;
  const scaleAnim = useRef(new RNAnimated.Value(0)).current;
  const rotationAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.parallel([
          RNAnimated.timing(particleAnim, {
            toValue: 1,
            duration: (4000 + Math.random() * 3000) * speed,
            useNativeDriver: true,
          }),
          RNAnimated.timing(opacityAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
          RNAnimated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 40,
            useNativeDriver: true,
          }),
          RNAnimated.timing(rotationAnim, {
            toValue: 1,
            duration: 3000 * speed,
            useNativeDriver: true,
          }),
        ]),
        RNAnimated.parallel([
          RNAnimated.timing(particleAnim, {
            toValue: 0,
            duration: (4000 + Math.random() * 3000) * speed,
            useNativeDriver: true,
          }),
          RNAnimated.timing(opacityAnim, {
            toValue: 0,
            duration: 1500,
            useNativeDriver: true,
          }),
          RNAnimated.spring(scaleAnim, {
            toValue: 0,
            friction: 8,
            tension: 40,
            useNativeDriver: true,
          }),
          RNAnimated.timing(rotationAnim, {
            toValue: 0,
            duration: 3000 * speed,
            useNativeDriver: true,
          }),
        ]),
      ])
    );

    setTimeout(() => {
      animation.start();
    }, delay);

    return () => animation.stop();
  }, []);

  const particleStyle = {
    transform: [
      {
        translateY: particleAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0, -100],
        }),
      },
      {
        scale: scaleAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0.3, 1],
        }),
      },
      {
        rotate: rotationAnim.interpolate({
          inputRange: [0, 1],
          outputRange: ['0deg', '360deg'],
        }),
      },
    ],
    opacity: opacityAnim,
  };

  return (
    <RNAnimated.View style={[styles.particle, particleStyle, style]}>
      <LinearGradient
        colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
        style={styles.particleGradient}
      />
    </RNAnimated.View>
  );
};

export default function ActivityLevel() {
  const { activityLevel, setActivityLevel, setUserHistory, currentUser, updateCurrentUser } = useUser();
  const router = useRouter();
  const userEmail = currentUser?.email;
  const [loading, setLoading] = useState(true);
  const [selectedLevel, setSelectedLevel] = useState(activityLevel || '');
  const fadeAnim = useRef(new RNAnimated.Value(0)).current;
  const slideAnim = useRef(new RNAnimated.Value(50)).current;
  const scaleAnim = useRef(new RNAnimated.Value(0.9)).current;
  const buttonScale = useRef(new RNAnimated.Value(1)).current;

  let [fontsLoaded] = useFonts({
    Poppins_700Bold,
    Poppins_400Regular,
    Poppins_600SemiBold,
  });

  useEffect(() => {
    // Main entrance animations
    RNAnimated.timing(fadeAnim, { 
      toValue: 1, 
      duration: 1000, 
      useNativeDriver: true 
    }).start();
    
    RNAnimated.timing(slideAnim, { 
      toValue: 0, 
      duration: 800, 
      useNativeDriver: true 
    }).start();
    
    RNAnimated.spring(scaleAnim, { 
      toValue: 1, 
      damping: 12, 
      stiffness: 100, 
      useNativeDriver: true 
    }).start();
  }, []);

  const contentAnimStyle = {
    opacity: fadeAnim,
    transform: [ { translateY: slideAnim }, { scale: scaleAnim } ]
  };

  const buttonAnimStyle = { transform: [{ scale: buttonScale }] };

  // Load activity level for current user only
  useEffect(() => {
    if (!userEmail) return;
    (async () => {
      try {
        if (currentUser && currentUser.isNewUser) {
          setSelectedLevel('');
          setLoading(false);
          return;
        }
        // Try to get from user-specific data
        const userDataKey = `@user_data:${userEmail}`;
        const userDataString = await AsyncStorage.getItem(userDataKey);
        if (userDataString) {
          const userData = JSON.parse(userDataString);
          if (userData.activityLevel) {
            setSelectedLevel(userData.activityLevel);
            setLoading(false);
            return;
          }
        }
        // Try to get from user-specific profile
        const profileKey = `@user_profile:${userEmail}`;
        const profileData = await AsyncStorage.getItem(profileKey);
        if (profileData) {
          const profile = JSON.parse(profileData);
          if (profile.activityLevel) {
            setSelectedLevel(profile.activityLevel);
            setLoading(false);
            return;
          }
        }
        setSelectedLevel('');
        setLoading(false);
      } catch (error) {
        setSelectedLevel('');
        setLoading(false);
      }
    })();
  }, [userEmail, currentUser]);

  const handleNext = async () => {
    if (!selectedLevel) {
      Alert.alert('Selection Required', 'Please select your activity level');
      return;
    }
    try {
      if (!userEmail) {
        Alert.alert('Error', 'User email not found. Please log in again.');
        return;
      }
      // Save to user-specific profile
      const profileKey = `@user_profile:${userEmail}`;
      const profileData = await AsyncStorage.getItem(profileKey);
      const profile = profileData ? JSON.parse(profileData) : {};
      profile.activityLevel = selectedLevel;
      await AsyncStorage.setItem(profileKey, JSON.stringify(profile));
      // Also save to user data
      const userDataKey = `@user_data:${userEmail}`;
      const userDataString = await AsyncStorage.getItem(userDataKey);
      const userData = userDataString ? JSON.parse(userDataString) : {};
      userData.activityLevel = selectedLevel;
      await AsyncStorage.setItem(userDataKey, JSON.stringify(userData));
      // Update user context
      await updateCurrentUser({ activityLevel: selectedLevel });
      router.push('/profile/measurements');
    } catch (error) {
      Alert.alert('Error', 'Failed to save your activity level.');
    }
  };

  const handleBack = () => {
    router.push('/profile/target-area');
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
          style={StyleSheet.absoluteFill}
        />
        <ActivityIndicator size="large" color="#667eea" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      <LinearGradient
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      >
        {/* Animated Background Elements */}
        <AdvancedParticle style={{ left: wp(10), top: hp(20) }} delay={0} speed={1.2} />
        <AdvancedParticle style={{ left: wp(80), top: hp(30) }} delay={500} speed={0.8} />
        <AdvancedParticle style={{ left: wp(20), top: hp(70) }} delay={1000} speed={1.5} />
        <View style={[styles.glowCircle, { top: hp(10), right: wp(10) }]} />
        <View style={[styles.glowCircle, { bottom: hp(20), left: wp(5) }]} />
        <View style={styles.circle1} />
        <View style={styles.circle2} />
        <View style={styles.circle3} />

        {/* Back Button */}
        <TouchableOpacity onPress={handleBack} style={styles.backButton} activeOpacity={0.8}>
          <LinearGradient 
            colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']} 
            style={styles.backButtonGradient}
          >
            <Ionicons name="arrow-back" size={24} color="#ffffff" />
          </LinearGradient>
        </TouchableOpacity>

        <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
          <RNAnimated.View style={contentAnimStyle}>
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.title}>Activity Level</Text>
              <Text style={styles.subtitle}>How active are you currently?</Text>
            </View>

            {/* Progress Indicator */}
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: '100%' }]} />
              </View>
              <Text style={styles.progressText}>Step 5 of 5</Text>
            </View>

            {/* Activity Level Options */}
            <View style={styles.optionsContainer}>
              {ACTIVITY_LEVELS.map(option => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.optionItem,
                    selectedLevel === option.id && styles.optionItemSelected
                  ]}
                  onPress={() => setSelectedLevel(option.id)}
                  activeOpacity={0.8}
                >
                  <View style={[
                    styles.optionIcon,
                    selectedLevel === option.id && styles.optionIconSelected
                  ]}>
                    <Ionicons
                      name={option.icon}
                      size={28}
                      color={selectedLevel === option.id ? '#667eea' : 'rgba(255,255,255,0.8)'}
                    />
                  </View>
                  <View style={styles.optionContent}>
                    <Text style={[
                      styles.optionLabel,
                      selectedLevel === option.id && styles.optionLabelSelected
                    ]}>
                      {option.label}
                    </Text>
                    <Text style={[
                      styles.optionDescription,
                      selectedLevel === option.id && styles.optionDescriptionSelected
                    ]}>
                      {option.description}
                    </Text>
                  </View>
                  {selectedLevel === option.id && (
                    <Ionicons name="checkmark-circle" size={24} color="#667eea" style={{ marginLeft: 10 }} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
            
            <Text style={styles.helperText}>
              This helps us tailor workouts to your current fitness capabilities
            </Text>
          </RNAnimated.View>
        </ScrollView>

        {/* Continue Button - always visible at the bottom */}
        <RNAnimated.View style={[styles.fixedButtonContainer, buttonAnimStyle]}>
          <TouchableOpacity
            style={styles.button}
            onPress={handleNext}
            activeOpacity={0.8}
            disabled={!selectedLevel}
          >
            <LinearGradient
              colors={selectedLevel ? ['#667eea', '#764ba2'] : ['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.2)']}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={[styles.buttonText, !selectedLevel && styles.buttonTextDisabled]}>
                Continue
              </Text>
              <Ionicons name="arrow-forward" size={20} color={selectedLevel ? "#fff" : "rgba(255,255,255,0.5)"} />
            </LinearGradient>
          </TouchableOpacity>
        </RNAnimated.View>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0c0c0c',
  },
  background: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  circle1: {
    position: 'absolute',
    top: -100,
    right: -100,
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  circle2: {
    position: 'absolute',
    bottom: -50,
    left: -50,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  circle3: {
    position: 'absolute',
    top: '40%',
    right: -30,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  glowCircle: {
    position: 'absolute',
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    zIndex: 0,
  },
  backButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 30,
    left: 20,
    zIndex: 10,
  },
  backButtonGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 120 : 100,
    paddingBottom: 100, // Extra padding for button
  },
  header: {
    marginBottom: 20,
    alignItems: 'center',
  },
  title: {
    fontFamily: 'Poppins_700Bold',
    fontSize: 28,
    color: '#ffffff',
    marginBottom: 5,
    textAlign: 'center',
  },
  subtitle: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  progressContainer: {
    marginBottom: 30,
    alignItems: 'center',
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
    width: '100%',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#667eea',
    borderRadius: 4,
  },
  progressText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  optionsContainer: {
    marginBottom: 20,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    marginBottom: 16,
  },
  optionItemSelected: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#667eea',
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionIconSelected: {
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
  },
  optionContent: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 4,
  },
  optionLabelSelected: {
    color: '#2c3e50',
  },
  optionDescription: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  optionDescriptionSelected: {
    color: 'rgba(44, 62, 80, 0.7)',
  },
  helperText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.5)',
    textAlign: 'center',
    marginBottom: 20,
    fontStyle: 'italic',
  },
  fixedButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 30,
    alignItems: 'center',
    zIndex: 100,
  },
  button: {
    width: '90%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  buttonText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#ffffff',
    marginRight: 8,
  },
  buttonTextDisabled: {
    color: 'rgba(255, 255, 255, 0.5)',
  },
  particle: {
    position: 'absolute',
    width: 20,
    height: 20,
    zIndex: 1,
  },
  particleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  },
});