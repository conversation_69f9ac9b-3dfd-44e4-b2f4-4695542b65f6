import React, { createContext, useContext, useState, useEffect, useCallback, useMemo, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEYS = {
  USERS: '@FitnessApp:users',
  CURRENT_USER: '@FitnessApp:currentUser',
  DIET_PLANS: '@diet_plans'
};

const UserContext = createContext();

export const UserProvider = ({ children }) => {
  const [users, setUsers] = useState([]);
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [plans, setPlans] = useState([]);
  const initialized = useRef(false);

  // Form state
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [height, setHeight] = useState('');
  const [weight, setWeight] = useState('');
  const [targetWeight, setTargetWeight] = useState('');
  const [targetAreas, setTargetAreas] = useState([]);
  const [activityLevel, setActivityLevel] = useState('');
  const [duration, setDuration] = useState('');
  const [userHistory, setUserHistory] = useState([]);
  const [fitnessLevel, setFitnessLevel] = useState('');
  const [unit, setUnit] = useState('metric');
  const [gender, setGender] = useState('');
  const [age, setAge] = useState('');
  const [targetHeight, setTargetHeight] = useState('');

  const isProfileComplete = useCallback((user = currentUser) => {
    if (!user) return false;
    
    // Check if the isProfileComplete flag is explicitly set
    if (user.isProfileComplete === true) return true;
    
    // Otherwise check if all required fields are present
    return (
      user.height &&
      user.weight &&
      user.activityLevel &&
      user.fitnessLevel &&
      user.gender &&
      user.targetAreas?.length > 0
    );
  }, [currentUser]);

  const loadPlans = useCallback(async () => {
    try {
      const plansString = await AsyncStorage.getItem(STORAGE_KEYS.DIET_PLANS);
      if (plansString) setPlans(JSON.parse(plansString));
    } catch (error) {
      console.error('Error loading plans:', error);
    }
  }, []);

  const saveData = useCallback(async (usersToSave, currentUserToSave) => {
    try {
      if (!usersToSave || !currentUserToSave) return;

      await AsyncStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(usersToSave));
      await AsyncStorage.setItem(
        STORAGE_KEYS.CURRENT_USER,
        JSON.stringify({
          ...currentUserToSave,
          isProfileComplete: isProfileComplete(currentUserToSave),
        })
      );
    } catch (error) {
      console.error('Error saving user data:', error);
    }
  }, [isProfileComplete]);

  // 1. Add helper to get user-specific keys
  const getUserDataKey = (email) => `@user_data:${email}`;
  const getUserProfileKey = (email) => `@user_profile:${email}`;
  const getUserHistoryKey = (email) => `@userHistory:${email}`;

  useEffect(() => {
    if (initialized.current) return;
    initialized.current = true;

    const loadData = async () => {
      try {
        const [usersData, currentUserData, currentUserEmail] = await Promise.all([
          AsyncStorage.getItem(STORAGE_KEYS.USERS),
          AsyncStorage.getItem(STORAGE_KEYS.CURRENT_USER),
          AsyncStorage.getItem('@current_user'),
        ]);

        let currentUsers = usersData ? JSON.parse(usersData) : [];
        if (currentUsers.length === 0) {
          // Create a test user if no users exist
          const testUser = {
            name: 'Test User',
            email: '<EMAIL>',
            password: '123456',
            height: '',
            weight: '',
            targetWeight: '',
            targetAreas: [],
            activityLevel: '',
            duration: '',
            fitnessLevel: '',
            unit: 'metric',
            gender: '',
            userHistory: [],
            signupTime: new Date().toISOString(),
            lastLogin: new Date().toISOString(),
            isProfileComplete: false
          };
          currentUsers = [testUser];
          await AsyncStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(currentUsers));
          await AsyncStorage.setItem(getUserDataKey(testUser.email), JSON.stringify(testUser));
          await AsyncStorage.setItem(getUserProfileKey(testUser.email), JSON.stringify({}));
          await AsyncStorage.setItem(getUserHistoryKey(testUser.email), JSON.stringify([]));
        }
        setUsers(currentUsers);
        // Reset all form fields to empty values by default
        setName(''); setEmail(''); setPassword(''); setHeight(''); setWeight(''); setTargetWeight(''); setTargetAreas([]); setActivityLevel(''); setDuration(''); setFitnessLevel(''); setUnit('metric'); setGender(''); setAge(''); setTargetHeight(''); setUserHistory([]);
        // Load current user data if available
        if (currentUserEmail) {
          // Try to load user-specific data first (source of truth)
          const userDataString = await AsyncStorage.getItem(getUserDataKey(currentUserEmail));
          if (userDataString) {
            const userData = JSON.parse(userDataString);
            setCurrentUser(userData);
            setName(userData.name || ''); setEmail(userData.email || ''); setPassword(userData.password || ''); setHeight(userData.height || ''); setWeight(userData.weight || ''); setTargetWeight(userData.targetWeight || ''); setTargetAreas(userData.targetAreas || []); setActivityLevel(userData.activityLevel || ''); setDuration(userData.duration || ''); setFitnessLevel(userData.fitnessLevel || ''); setUnit(userData.unit || 'metric'); setGender(userData.gender || ''); setAge(userData.age || ''); setTargetHeight(userData.targetHeight || ''); setUserHistory(userData.userHistory || []);
          } else if (currentUserData) {
            // Fallback to shared data if user-specific data not found
            const parsedUser = JSON.parse(currentUserData);
            setCurrentUser(parsedUser);
            setName(parsedUser.name || ''); setEmail(parsedUser.email || ''); setPassword(parsedUser.password || '');
            // Create user-specific data from shared data
            await AsyncStorage.setItem(getUserDataKey(currentUserEmail), JSON.stringify(parsedUser));
          } else {
            // If user-specific data not found, and no shared data, create minimal user data
            const userData = {
              email: currentUserEmail,
              isNewUser: true,
              isProfileComplete: false,
              lastLogin: new Date().toISOString()
            };
            await AsyncStorage.setItem(getUserDataKey(currentUserEmail), JSON.stringify(userData));
            await AsyncStorage.setItem(getUserProfileKey(currentUserEmail), JSON.stringify({}));
            await AsyncStorage.setItem(getUserHistoryKey(currentUserEmail), JSON.stringify([]));
            setCurrentUser(userData);
            setEmail(currentUserEmail);
          }
        } else if (currentUserData) {
          // If no current user email but we have current user data
          const parsedUser = JSON.parse(currentUserData);
          setCurrentUser(parsedUser);
          setName(parsedUser.name || ''); setEmail(parsedUser.email || ''); setPassword(parsedUser.password || '');
        }
        await loadPlans();
      } catch (error) {
        console.error('Error loading user data:', error);
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, [loadPlans]);

  // 2. Register user: always create user-specific storage
  const registerUser = useCallback(async (newUser) => {
    try {
      const trimmedEmail = newUser.email.toLowerCase().trim();
      const trimmedName = newUser.name.trim();
      const trimmedPassword = newUser.password.trim();
      // Load fresh user data from AsyncStorage
      const usersData = await AsyncStorage.getItem(STORAGE_KEYS.USERS);
      const currentUsers = usersData ? JSON.parse(usersData) : [];
      // Check if user already exists
      const existingUser = currentUsers.find(user => user.email === trimmedEmail);
      if (existingUser) throw new Error('An account with this email already exists. Please login instead.');
      // Create a clean new user object
      const now = new Date();
      const loginDate = now.toLocaleDateString();
      const loginTime = now.toLocaleTimeString();
      const newUserWithMeta = {
        name: trimmedName,
        email: trimmedEmail,
        password: trimmedPassword,
        signupTime: now.toISOString(),
        lastLogin: now.toISOString(),
        loginDate,
        loginTime,
        registrationDate: loginDate,
        registrationTime: loginTime,
        isProfileComplete: false,
        isNewUser: true,
        userHistory: []
      };
      const updatedUsers = [...currentUsers, newUserWithMeta];
      // Save to AsyncStorage - both shared and user-specific storage
      await AsyncStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(updatedUsers));
      await AsyncStorage.setItem(STORAGE_KEYS.CURRENT_USER, JSON.stringify(newUserWithMeta));
      await AsyncStorage.setItem(getUserDataKey(trimmedEmail), JSON.stringify(newUserWithMeta));
      await AsyncStorage.setItem(getUserProfileKey(trimmedEmail), JSON.stringify({}));
      await AsyncStorage.setItem(getUserHistoryKey(trimmedEmail), JSON.stringify([]));
      await AsyncStorage.setItem('@current_user', trimmedEmail);
      setUsers(updatedUsers);
      setCurrentUser(newUserWithMeta);
      // Reset all form fields for new user
      setName(trimmedName); setEmail(trimmedEmail); setPassword(trimmedPassword); setHeight(''); setWeight(''); setTargetWeight(''); setTargetAreas([]); setActivityLevel(''); setDuration(''); setFitnessLevel(''); setUnit('metric'); setGender(''); setAge(''); setTargetHeight(''); setUserHistory([]);
      return newUserWithMeta;
    } catch (error) {
      console.error('UserContext: Registration error:', error);
      throw error;
    }
  }, []);

  // 3. Login user: always load from user-specific storage
  const loginUser = useCallback(async (email, password) => {
    try {
      const trimmedEmail = email.toLowerCase().trim();
      const trimmedPassword = password.trim();
      // Load fresh user data from AsyncStorage
      const usersData = await AsyncStorage.getItem(STORAGE_KEYS.USERS);
      const currentUsers = usersData ? JSON.parse(usersData) : [];
      // Find user
      const user = currentUsers.find(u => u.email === trimmedEmail);
      if (!user) throw new Error('User not found. Please check your email or sign up.');
      if (user.password !== trimmedPassword) throw new Error('Incorrect password. Please try again.');
      // Update login timestamp
      const now = new Date();
      const updatedUser = {
        ...user,
        lastLogin: now.toISOString(),
        isProfileComplete: isProfileComplete(user)
      };
      const updatedUsers = currentUsers.map(u => u.email === trimmedEmail ? updatedUser : u);
      await AsyncStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(updatedUsers));
      await AsyncStorage.setItem(STORAGE_KEYS.CURRENT_USER, JSON.stringify(updatedUser));
      await AsyncStorage.setItem('@current_user', trimmedEmail);
      // Get user-specific data - this is the source of truth for this user
      let userData = {};
      const userDataString = await AsyncStorage.getItem(getUserDataKey(trimmedEmail));
      if (userDataString) {
        userData = JSON.parse(userDataString);
        userData = { ...userData, lastLogin: now.toISOString() };
      } else {
        userData = { ...updatedUser };
      }
      await AsyncStorage.setItem(getUserDataKey(trimmedEmail), JSON.stringify(userData));
      setUsers(updatedUsers);
      setCurrentUser(userData);
      setName(userData.name || ''); setEmail(userData.email || ''); setPassword(userData.password || ''); setHeight(userData.height || ''); setWeight(userData.weight || ''); setTargetWeight(userData.targetWeight || ''); setTargetAreas(userData.targetAreas || []); setActivityLevel(userData.activityLevel || ''); setDuration(userData.duration || ''); setFitnessLevel(userData.fitnessLevel || ''); setUnit(userData.unit || 'metric'); setGender(userData.gender || ''); setAge(userData.age || ''); setTargetHeight(userData.targetHeight || ''); setUserHistory(userData.userHistory || []);
      return userData;
    } catch (error) {
      console.error('UserContext: Login error:', error);
      throw error;
    }
  }, [isProfileComplete]);

  // 4. Logout user: clear all form state
  const logoutUser = useCallback(async () => {
    try {
      // Remove all user-related AsyncStorage keys
      await AsyncStorage.removeItem(STORAGE_KEYS.CURRENT_USER);
      await AsyncStorage.removeItem('@current_user');
      // Optionally clear other user-specific keys if needed
      // Reset all state
      setCurrentUser(null);
      setName('');
      setEmail('');
      setPassword('');
      setHeight('');
      setWeight('');
      setTargetWeight('');
      setTargetAreas([]);
      setActivityLevel('');
      setDuration('');
      setFitnessLevel('');
      setUnit('metric');
      setGender('');
      setAge('');
      setTargetHeight('');
      setUserHistory([]);
      setPlans([]);
      setLoading(false);
      console.log('UserContext: User logged out and state reset.');
    } catch (error) {
      console.error('UserContext: Error during logout:', error);
      throw error;
    }
  }, []);

  // 5. Update user: always update user-specific storage
  const updateCurrentUser = useCallback(async (updatedData) => {
    try {
      if (!currentUser) throw new Error('No user logged in');
      const updatedUser = {
        ...currentUser,
        ...updatedData,
        isProfileComplete: isProfileComplete({ ...currentUser, ...updatedData }),
      };
      const updatedUsers = users.map(u => u.email === currentUser.email ? updatedUser : u);
      await AsyncStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(updatedUsers));
      await AsyncStorage.setItem(STORAGE_KEYS.CURRENT_USER, JSON.stringify(updatedUser));
      await AsyncStorage.setItem(getUserDataKey(currentUser.email), JSON.stringify(updatedUser));
      // Also update user profile for profile screens
      const profileKey = getUserProfileKey(currentUser.email);
      const profileData = await AsyncStorage.getItem(profileKey);
      const profile = profileData ? JSON.parse(profileData) : {};
      const updatedProfile = { ...profile };
      if (updatedData.fitnessLevel) updatedProfile.fitnessLevel = updatedData.fitnessLevel;
      if (updatedData.gender) updatedProfile.gender = updatedData.gender;
      if (updatedData.targetAreas) updatedProfile.targetAreas = updatedData.targetAreas;
      if (updatedData.activityLevel) updatedProfile.activityLevel = updatedData.activityLevel;
      if (updatedData.height || updatedData.weight || updatedData.targetWeight || updatedData.targetHeight || updatedData.age) {
        updatedProfile.measurements = {
          ...(profile.measurements || {}),
          height: updatedData.height || profile.measurements?.height,
          weight: updatedData.weight || profile.measurements?.weight,
          targetWeight: updatedData.targetWeight || profile.measurements?.targetWeight,
          targetHeight: updatedData.targetHeight || profile.measurements?.targetHeight,
          age: updatedData.age || profile.measurements?.age
        };
      }
      await AsyncStorage.setItem(profileKey, JSON.stringify(updatedProfile));
      setUsers(updatedUsers);
      setCurrentUser(updatedUser);
      // Update form fields with the updated data
      if (updatedData.name) setName(updatedData.name);
      if (updatedData.email) setEmail(updatedData.email);
      if (updatedData.password) setPassword(updatedData.password);
      if (updatedData.height) setHeight(updatedData.height);
      if (updatedData.weight) setWeight(updatedData.weight);
      if (updatedData.targetWeight) setTargetWeight(updatedData.targetWeight);
      if (updatedData.targetAreas) setTargetAreas(updatedData.targetAreas);
      if (updatedData.activityLevel) setActivityLevel(updatedData.activityLevel);
      if (updatedData.duration) setDuration(updatedData.duration);
      if (updatedData.fitnessLevel) setFitnessLevel(updatedData.fitnessLevel);
      if (updatedData.unit) setUnit(updatedData.unit);
      if (updatedData.gender) setGender(updatedData.gender);
      if (updatedData.age) setAge(updatedData.age);
      if (updatedData.targetHeight) setTargetHeight(updatedData.targetHeight);
      if (updatedData.userHistory) setUserHistory(updatedData.userHistory);
      return updatedUser;
    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  }, [currentUser, users, isProfileComplete]);

  const contextValue = useMemo(() => ({
    // State
    users,
    currentUser,
    loading,
    plans,
    
    // Actions
    registerUser,
    loginUser,
    logoutUser,
    updateCurrentUser,
    isProfileComplete,
    setPlans,
    
    // Form state
    name, setName,
    email, setEmail,
    password, setPassword,
    height, setHeight,
    weight, setWeight,
    targetWeight, setTargetWeight,
    targetAreas, setTargetAreas,
    activityLevel, setActivityLevel,
    duration, setDuration,
    userHistory, setUserHistory,
    fitnessLevel, setFitnessLevel,
    unit, setUnit,
    gender, setGender,
    age, setAge,
    targetHeight, setTargetHeight
  }), [
    users,
    currentUser,
    loading,
    plans,
    registerUser,
    loginUser,
    logoutUser,
    updateCurrentUser,
    isProfileComplete,
    setPlans,
    name, setName,
    email, setEmail,
    password, setPassword,
    height, setHeight,
    weight, setWeight,
    targetWeight, setTargetWeight,
    targetAreas, setTargetAreas,
    activityLevel, setActivityLevel,
    duration, setDuration,
    userHistory, setUserHistory,
    fitnessLevel, setFitnessLevel,
    unit, setUnit,
    gender, setGender,
    age, setAge,
    targetHeight, setTargetHeight
  ]);

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export default UserProvider;