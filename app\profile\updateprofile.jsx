import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  SafeAreaView,
  StatusBar,
  Animated as RNAnimated,
  Dimensions,
  TextInput,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { Poppins_400Regular, Poppins_700Bold, Poppins_600SemiBold, useFonts } from '@expo-google-fonts/poppins';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useUser } from '../../context/UserContext';

const { width, height } = Dimensions.get('window');

const AdvancedParticle = ({ style }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const speed = 4000 + Math.random() * 2000;
    const delay = Math.random() * 3000;

    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(particleAnim, { toValue: 1, duration: speed, useNativeDriver: true }),
        RNAnimated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = RNAnimated.sequence([
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: speed - 1000, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);

    setTimeout(() => {
      animation.start();
      RNAnimated.loop(opacitySequence).start();
    }, delay);

  }, []);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <RNAnimated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

export default function UpdateProfile() {
  const router = useRouter();
  const { currentUser, updateCurrentUser } = useUser();
  const [loading, setLoading] = useState(false);
  
  // Form state
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [height, setHeight] = useState('');
  const [weight, setWeight] = useState('');
  const [targetWeight, setTargetWeight] = useState('');
  const [gender, setGender] = useState('');
  const [activityLevel, setActivityLevel] = useState('');
  const [fitnessLevel, setFitnessLevel] = useState('');
  const [targetAreas, setTargetAreas] = useState([]);

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_700Bold,
    Poppins_600SemiBold,
  });

  useEffect(() => {
    if (currentUser) {
      setName(currentUser.name || '');
      setEmail(currentUser.email || '');
      setHeight(currentUser.height || '');
      setWeight(currentUser.weight || '');
      setTargetWeight(currentUser.targetWeight || '');
      setGender(currentUser.gender || '');
      setActivityLevel(currentUser.activityLevel || '');
      setFitnessLevel(currentUser.fitnessLevel || '');
      setTargetAreas(currentUser.targetAreas || []);
    }
  }, [currentUser]);

  const handleBack = () => {
    router.back();
  };

  const handleSave = async () => {
    if (!name.trim() || !email.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setLoading(true);
    try {
      const updatedUser = {
        ...currentUser,
        name: name.trim(),
        email: email.trim(),
        height,
        weight,
        targetWeight,
        gender,
        activityLevel,
        fitnessLevel,
        targetAreas,
        lastUpdated: new Date().toISOString(),
      };

      await updateCurrentUser(updatedUser);
      await AsyncStorage.setItem('@FitnessApp:currentUser', JSON.stringify(updatedUser));
      
      Alert.alert('Success', 'Profile updated successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const toggleTargetArea = (area) => {
    setTargetAreas(prev => {
      if (prev.includes(area)) {
        return prev.filter(a => a !== area);
      } else {
        return [...prev, area];
      }
    });
  };

  const activityLevels = ['Sedentary', 'Lightly Active', 'Moderately Active', 'Very Active', 'Extremely Active'];
  const fitnessLevels = ['Beginner', 'Intermediate', 'Advanced'];
  const targetAreaOptions = ['Arms', 'Chest', 'Back', 'Legs', 'Core', 'Full Body'];

  if (!fontsLoaded) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#0c0c0c' }}>
        <LinearGradient colors={['#0c0c0c', '#1a1a2e']} style={StyleSheet.absoluteFill} />
        <ActivityIndicator size="large" color="#fff" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      {[...Array(20)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Update Profile</Text>
          <TouchableOpacity onPress={handleSave} style={styles.saveButton} disabled={loading}>
            <Text style={styles.saveButtonText}>{loading ? 'Saving...' : 'Save'}</Text>
          </TouchableOpacity>
        </View>

        <KeyboardAvoidingView style={{ flex: 1 }} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContainer}
          >
            {/* Personal Information */}
            <Animated.View entering={FadeInUp.delay(100).duration(600)}>
              <View style={styles.sectionHeader}>
                <Ionicons name="person-outline" size={20} color="#667eea" />
                <Text style={styles.sectionTitle}>Personal Information</Text>
              </View>
              
              <View style={styles.formCard}>
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Name *</Text>
                  <TextInput
                    style={styles.input}
                    value={name}
                    onChangeText={setName}
                    placeholder="Enter your name"
                    placeholderTextColor="rgba(255,255,255,0.5)"
                  />
                </View>
                
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Email *</Text>
                  <TextInput
                    style={styles.input}
                    value={email}
                    onChangeText={setEmail}
                    placeholder="Enter your email"
                    placeholderTextColor="rgba(255,255,255,0.5)"
                    keyboardType="email-address"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Gender</Text>
                  <View style={styles.radioGroup}>
                    {['Male', 'Female', 'Other'].map((option) => (
                      <TouchableOpacity
                        key={option}
                        style={[styles.radioButton, gender === option && styles.radioButtonActive]}
                        onPress={() => setGender(option)}
                      >
                        <Text style={[styles.radioText, gender === option && styles.radioTextActive]}>
                          {option}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              </View>
            </Animated.View>

            {/* Physical Information */}
            <Animated.View entering={FadeInUp.delay(200).duration(600)}>
              <View style={styles.sectionHeader}>
                <Ionicons name="fitness-outline" size={20} color="#667eea" />
                <Text style={styles.sectionTitle}>Physical Information</Text>
              </View>
              
              <View style={styles.formCard}>
                <View style={styles.inputRow}>
                  <View style={[styles.inputGroup, { flex: 1, marginRight: 10 }]}>
                    <Text style={styles.inputLabel}>Height (cm)</Text>
                    <TextInput
                      style={styles.input}
                      value={height}
                      onChangeText={setHeight}
                      placeholder="170"
                      placeholderTextColor="rgba(255,255,255,0.5)"
                      keyboardType="numeric"
                    />
                  </View>
                  
                  <View style={[styles.inputGroup, { flex: 1 }]}>
                    <Text style={styles.inputLabel}>Weight (kg)</Text>
                    <TextInput
                      style={styles.input}
                      value={weight}
                      onChangeText={setWeight}
                      placeholder="70"
                      placeholderTextColor="rgba(255,255,255,0.5)"
                      keyboardType="numeric"
                    />
                  </View>
                </View>
                
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Target Weight (kg)</Text>
                  <TextInput
                    style={styles.input}
                    value={targetWeight}
                    onChangeText={setTargetWeight}
                    placeholder="65"
                    placeholderTextColor="rgba(255,255,255,0.5)"
                    keyboardType="numeric"
                  />
                </View>
              </View>
            </Animated.View>

            {/* Activity & Fitness */}
            <Animated.View entering={FadeInUp.delay(300).duration(600)}>
              <View style={styles.sectionHeader}>
                <Ionicons name="trending-up-outline" size={20} color="#667eea" />
                <Text style={styles.sectionTitle}>Activity & Fitness</Text>
              </View>
              
              <View style={styles.formCard}>
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Activity Level</Text>
                  <View style={styles.dropdownContainer}>
                    {activityLevels.map((level) => (
                      <TouchableOpacity
                        key={level}
                        style={[styles.dropdownItem, activityLevel === level && styles.dropdownItemActive]}
                        onPress={() => setActivityLevel(level)}
                      >
                        <Text style={[styles.dropdownText, activityLevel === level && styles.dropdownTextActive]}>
                          {level}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Fitness Level</Text>
                  <View style={styles.dropdownContainer}>
                    {fitnessLevels.map((level) => (
                      <TouchableOpacity
                        key={level}
                        style={[styles.dropdownItem, fitnessLevel === level && styles.dropdownItemActive]}
                        onPress={() => setFitnessLevel(level)}
                      >
                        <Text style={[styles.dropdownText, fitnessLevel === level && styles.dropdownTextActive]}>
                          {level}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              </View>
            </Animated.View>

            {/* Target Areas */}
            <Animated.View entering={FadeInUp.delay(400).duration(600)}>
              <View style={styles.sectionHeader}>
                <Ionicons name="body-outline" size={20} color="#667eea" />
                <Text style={styles.sectionTitle}>Target Areas</Text>
              </View>
              
              <View style={styles.formCard}>
                <View style={styles.targetAreasGrid}>
                  {targetAreaOptions.map((area) => (
                    <TouchableOpacity
                      key={area}
                      style={[styles.targetAreaButton, targetAreas.includes(area) && styles.targetAreaButtonActive]}
                      onPress={() => toggleTargetArea(area)}
                    >
                      <Text style={[styles.targetAreaText, targetAreas.includes(area) && styles.targetAreaTextActive]}>
                        {area}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </Animated.View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 5,
    zIndex: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 40,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 22,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  saveButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(102,126,234,0.2)',
    borderRadius: 10,
  },
  saveButtonText: {
    color: '#667eea',
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
  },
  scrollContainer: {
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginLeft: 10,
  },
  formCard: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 16,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  inputGroup: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  inputRow: {
    flexDirection: 'row',
  },
  radioGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  radioButton: {
    flex: 1,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  radioButtonActive: {
    backgroundColor: 'rgba(102,126,234,0.2)',
    borderColor: 'rgba(102,126,234,0.4)',
  },
  radioText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
  },
  radioTextActive: {
    color: '#667eea',
    fontFamily: 'Poppins_600SemiBold',
  },
  dropdownContainer: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  dropdownItem: {
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  dropdownItemActive: {
    backgroundColor: 'rgba(102,126,234,0.2)',
  },
  dropdownText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
  },
  dropdownTextActive: {
    color: '#667eea',
    fontFamily: 'Poppins_600SemiBold',
  },
  targetAreasGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  targetAreaButton: {
    width: '48%',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  targetAreaButtonActive: {
    backgroundColor: 'rgba(102,126,234,0.2)',
    borderColor: 'rgba(102,126,234,0.4)',
  },
  targetAreaText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255,255,255,0.8)',
  },
  targetAreaTextActive: {
    color: '#667eea',
    fontFamily: 'Poppins_600SemiBold',
  },
});
