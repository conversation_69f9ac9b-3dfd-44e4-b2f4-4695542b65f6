// This file is used in the project (see homepage.jsx and navigation). Do NOT delete.
import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Linking,
  Platform,
  SafeAreaView,
  StatusBar,
  Animated,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
  Keyboard,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import AICoach from '../components/AICoach.redesigned';
import AIWorkoutGenerator from '../components/AIWorkoutGenerator';
import { useUser } from '../context/UserContext';
import { handleVideoTutorial } from '../constants/data';
import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width, height } = Dimensions.get('window');
const OPENAI_API_KEY = 'YOUR_OPENAI_API_KEY_HERE'; // TODO: Store securely in production!

const AdvancedParticle = ({ style }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const speed = 4000 + Math.random() * 2000;
    const delay = Math.random() * 3000;

    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(particleAnim, { toValue: 1, duration: speed, useNativeDriver: true }),
        Animated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = Animated.sequence([
      Animated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 1, duration: speed - 1000, useNativeDriver: true }),
      Animated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);

    setTimeout(() => {
      animation.start();
      Animated.loop(opacitySequence).start();
    }, delay);

  }, []);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <Animated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

async function savePushTokenForUser(userEmail) {
  const { status } = await Notifications.requestPermissionsAsync();
  if (status !== 'granted') return;
  const token = (await Notifications.getExpoPushTokenAsync()).data;
  // Save the token to AsyncStorage or your backend, associated with the user
  await AsyncStorage.setItem(`@push_token:${userEmail}`, token);
}

async function sendPushNotificationToUser(userEmail, title, message) {
  const token = await AsyncStorage.getItem(`@push_token:${userEmail}`);
  if (!token) return;

  await fetch('https://exp.host/--/api/v2/push/send', {
    method: 'POST',
    headers: { 'Accept': 'application/json', 'Content-Type': 'application/json' },
    body: JSON.stringify({
      to: token,
      sound: 'default',
      title,
      body: message,
      data: { customData: 'your data' }
    })
  });
}

export default function AICoachPage() {
  const router = useRouter();
  const { currentUser } = useUser();
  const [workoutGeneratorVisible, setWorkoutGeneratorVisible] = useState(false);
  const [aiCoachVisible, setAiCoachVisible] = useState(true);

  // AI Chat State
  const [chatMessages, setChatMessages] = useState([
    { sender: 'ai', text: 'Hi! I am your AI Coach. Ask me anything about fitness, workouts, or nutrition!' }
  ]);
  const [input, setInput] = useState('');
  const [sending, setSending] = useState(false);
  const scrollViewRef = useRef();

  // Add to the top-level state in AICoachPage:
  const [apiKey, setApiKey] = useState('');
  const [settingsVisible, setSettingsVisible] = useState(false);

  // On mount, load API key from AsyncStorage
  useEffect(() => {
    AsyncStorage.getItem('@openai_api_key').then(key => {
      if (key) setApiKey(key);
    });
  }, []);

  const handleClose = () => {
    router.back();
  };

  const handleGenerateWorkout = () => {
    setAiCoachVisible(false);
    setWorkoutGeneratorVisible(true);
  };

  const handleCustomWorkout = () => {
    setAiCoachVisible(false);
    setWorkoutGeneratorVisible(true);
  };

  const handleVideoPlay = async (exerciseName) => {
    const videoUrl = handleVideoTutorial(exerciseName);
    
    Alert.alert(
      '🎥 Exercise Video',
      `Opening tutorial video for ${exerciseName}`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Watch Video', 
          onPress: async () => {
            try {
              console.log('🎥 Opening video tutorial:', videoUrl);
              const supported = await Linking.canOpenURL(videoUrl);
              
              if (supported) {
                await Linking.openURL(videoUrl);
              } else {
                Alert.alert('❌ Error', 'Cannot open video URL. Please check your internet connection.');
              }
            } catch (error) {
              console.error('❌ Error opening video:', error);
              Alert.alert('❌ Error', 'Could not open video. Please check your internet connection.');
            }
          }
        }
      ]
    );
  };

  const handleWorkoutGenerated = (workout) => {
    console.log('🎉 Workout generated in ai-coach:', workout);
    
    // The workout is already being handled by AIWorkoutGenerator
    // Just close the modal and return to AI Coach
    console.log('✅ Workout generation completed, returning to AI Coach');
    setWorkoutGeneratorVisible(false);
    setAiCoachVisible(true);
    
    // Show a success message
    Alert.alert(
      '🎉 Workout Complete!',
      `Great job! You've completed your ${workout.goal} workout.\n\nDuration: ${workout.duration}\nLevel: ${workout.level}\nExercises: ${workout.exercises?.length || 0}`,
      [
        { 
          text: 'Generate Another', 
          onPress: () => {
            setAiCoachVisible(false);
            setWorkoutGeneratorVisible(true);
          }
        },
        { 
          text: 'Done', 
          style: 'default'
        }
      ]
    );
  };

  const sendMessage = async () => {
    if (!input.trim()) return;
    const userMsg = { sender: 'user', text: input.trim() };
    setChatMessages(msgs => [...msgs, userMsg]);
    setInput('');
    setSending(true);

    const keyToUse = apiKey || 'YOUR_OPENAI_API_KEY_HERE';
    if (!keyToUse || keyToUse === 'YOUR_OPENAI_API_KEY_HERE') {
      setChatMessages(msgs => [...msgs, { sender: 'ai', text: '❌ OpenAI API key not set. Please set your API key in settings.' }]);
      setSending(false);
      return;
    }

    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${keyToUse}`,
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: [
            { role: 'system', content: 'You are a helpful AI fitness coach. Answer questions about fitness, workouts, or nutrition in a friendly, concise way.' },
            ...chatMessages.filter(m => m.sender === 'user').map(m => ({ role: 'user', content: m.text })),
            { role: 'user', content: userMsg.text },
          ],
          max_tokens: 150,
          temperature: 0.7,
        }),
      });
      const data = await response.json();
      const aiText = data.choices?.[0]?.message?.content?.trim() || 'Sorry, I could not get a response from OpenAI.';
      setChatMessages(msgs => [...msgs, { sender: 'ai', text: aiText }]);
    } catch (err) {
      setChatMessages(msgs => [...msgs, { sender: 'ai', text: '❌ Error connecting to OpenAI. Please try again later.' }]);
    }
    setSending(false);
    scrollViewRef.current?.scrollToEnd({ animated: true });
  };

  return (
    <View style={styles.container}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      {[...Array(20)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      <View style={[styles.glowCircle, { top: '10%', right: -50 }]} />
      <View style={[styles.glowCircle, { bottom: '15%', left: -50 }]} />

      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" />

        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>AI Coach</Text>
          <TouchableOpacity onPress={() => setSettingsVisible(true)} style={{ marginLeft: 10 }}>
            <Ionicons name="settings-outline" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        {/* AI Coach Component */}
        <AICoach 
          userEmail={currentUser?.email}
          userProfile={currentUser}
          onWorkoutGenerated={handleWorkoutGenerated}
          onGenerateWorkout={handleGenerateWorkout}
        />
      </SafeAreaView>

      {/* AI Workout Generator Modal */}
      <Modal
        visible={workoutGeneratorVisible}
        animationType="slide"
        onRequestClose={() => {
          setWorkoutGeneratorVisible(false);
          setAiCoachVisible(true);
        }}
      >
        <AIWorkoutGenerator
          onClose={() => {
            setWorkoutGeneratorVisible(false);
            setAiCoachVisible(true);
          }}
          onWorkoutGenerated={(workout) => {
            handleWorkoutGenerated(workout);
            setWorkoutGeneratorVisible(false);
            setAiCoachVisible(true);
          }}
          userProfile={currentUser}
        />
      </Modal>

      {/* Settings Modal */}
      <Modal visible={settingsVisible} transparent animationType="fade" onRequestClose={() => setSettingsVisible(false)}>
        <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', alignItems: 'center' }}>
          <View style={{ backgroundColor: '#fff', borderRadius: 16, padding: 24, width: 320 }}>
            <Text style={{ fontWeight: 'bold', fontSize: 18, marginBottom: 12, color: '#222' }}>OpenAI API Key</Text>
            <TextInput
              style={{ backgroundColor: '#eee', color: '#222', borderRadius: 8, padding: 10, width: '100%', fontSize: 16, marginBottom: 16 }}
              placeholder="Enter your OpenAI API key"
              placeholderTextColor="#888"
              value={apiKey}
              onChangeText={setApiKey}
              autoFocus
            />
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
              <TouchableOpacity
                style={{ backgroundColor: '#667eea', borderRadius: 8, paddingVertical: 10, paddingHorizontal: 18 }}
                onPress={async () => {
                  await AsyncStorage.setItem('@openai_api_key', apiKey);
                  setSettingsVisible(false);
                }}
              >
                <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 16 }}>Save</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={{ marginLeft: 10, backgroundColor: '#F44336', borderRadius: 8, paddingVertical: 10, paddingHorizontal: 18 }}
                onPress={() => setSettingsVisible(false)}
              >
                <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 16 }}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 5,
    zIndex: 0,
  },
  glowCircle: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    zIndex: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight + 10 : 40,
    paddingBottom: 20,
  },
  closeButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 22,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  scrollContainer: {
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  chatContainer: {
    flexGrow: 1,
    padding: 18,
    paddingBottom: 80,
    backgroundColor: 'transparent',
  },
  chatBubble: {
    maxWidth: '80%',
    borderRadius: 18,
    padding: 12,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  userBubble: {
    alignSelf: 'flex-end',
    backgroundColor: '#7ed957',
    borderTopRightRadius: 4,
  },
  aiBubble: {
    alignSelf: 'flex-start',
    backgroundColor: '#fff',
    borderTopLeftRadius: 4,
  },
  chatText: {
    color: '#222',
    fontSize: 16,
    fontWeight: '500',
  },
  inputBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#222',
    borderRadius: 16,
    margin: 18,
    paddingHorizontal: 10,
    paddingVertical: 6,
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  input: {
    flex: 1,
    color: '#fff',
    fontSize: 16,
    paddingVertical: 6,
    paddingHorizontal: 10,
    backgroundColor: 'transparent',
  },
  sendButton: {
    padding: 8,
  },
});