# Title-Specific Images for ImageSlider

## 🎯 **Exact Title-Related Images for Each Category**

Here are the specific image URLs that match each title perfectly:

---

## 🏃‍♂️ **1. Morning Workout**
**Title**: "Morning Workout"  
**Subtitle**: "Start your day with energy"

### Specific Image URLs:
```javascript
morning1: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
morning2: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
sunrise: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
```

**Replace with these title-specific images:**
- `morning1`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`
- `morning2`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`
- `sunrise`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`

---

## 💪 **2. Strength Training**
**Title**: "Strength Training"  
**Subtitle**: "Build muscle and power"

### Specific Image URLs:
```javascript
strength1: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
strength2: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
muscle: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
```

**Replace with these title-specific images:**
- `strength1`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`
- `strength2`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`
- `muscle`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`

---

## 🏃‍♀️ **3. Cardio Blast**
**Title**: "Cardio Blast"  
**Subtitle**: "Burn calories and boost endurance"

### Specific Image URLs:
```javascript
cardio1: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
cardio2: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
running: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
```

**Replace with these title-specific images:**
- `cardio1`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`
- `cardio2`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`
- `running`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`

---

## 🧘‍♀️ **4. Flexibility & Yoga**
**Title**: "Flexibility & Yoga"  
**Subtitle**: "Improve mobility and recovery"

### Specific Image URLs:
```javascript
yoga1: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
yoga2: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
flexibility: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
```

**Replace with these title-specific images:**
- `yoga1`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`
- `yoga2`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`
- `flexibility`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`

---

## 🥗 **5. Nutrition Guide**
**Title**: "Nutrition Guide"  
**Subtitle**: "Fuel your fitness journey"

### Specific Image URLs:
```javascript
nutrition1: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
nutrition2: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
diet: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
```

**Replace with these title-specific images:**
- `nutrition1`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`
- `nutrition2`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`
- `diet`: `https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop`

---

## 🔧 **How to Update Images**

### Step 1: Replace URLs in `constants/remoteImages.js`
Replace the placeholder URLs with the specific images above.

### Step 2: Test the ImageSlider
The ImageSlider will automatically use the new images based on the category mapping.

### Step 3: Verify Each Title
Each title should now display its specific, related image:
- ✅ "Morning Workout" → Morning exercise images
- ✅ "Strength Training" → Weightlifting images  
- ✅ "Cardio Blast" → Running/cardio images
- ✅ "Flexibility & Yoga" → Yoga/stretching images
- ✅ "Nutrition Guide" → Healthy food images

---

## 📱 **Current ImageSlider Configuration**

```javascript
// In homepage.jsx
const sliderImages = [
  {
    title: 'Morning Workout',
    subtitle: 'Start your day with energy',
    image: { uri: getReliableImageSource('morning') }, // Uses morning1
    onPress: () => router.push('/morning-workouts')
  },
  {
    title: 'Strength Training', 
    subtitle: 'Build muscle and power',
    image: { uri: getReliableImageSource('strength') }, // Uses strength1
    onPress: () => router.push('/strength-training')
  },
  {
    title: 'Cardio Blast',
    subtitle: 'Burn calories and boost endurance', 
    image: { uri: getReliableImageSource('cardio') }, // Uses cardio1
    onPress: () => router.push('/cardio-blast')
  },
  {
    title: 'Flexibility & Yoga',
    subtitle: 'Improve mobility and recovery',
    image: { uri: getReliableImageSource('flexibility') }, // Uses flexibility
    onPress: () => router.push('/flexibility-yoga')
  },
  {
    title: 'Nutrition Guide',
    subtitle: 'Fuel your fitness journey',
    image: { uri: getReliableImageSource('nutrition') }, // Uses nutrition1
    onPress: () => router.push('/nutrition-guide')
  }
];
```

---

## ✅ **Status: Ready for Title-Specific Images**

All 5 categories are now properly mapped to their specific, title-related images. Each title will display exactly the right image that matches its content and purpose.

**Total Images Needed: 5 (one for each title)**
**Current Status: ✅ All titles mapped to specific images** 