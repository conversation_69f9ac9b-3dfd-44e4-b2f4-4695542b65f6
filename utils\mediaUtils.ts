/**
 * Utility functions for media handling in the fitness app
 */

export interface MediaItem {
  id: string;
  type: 'image' | 'video' | 'youtube';
  source: string;
  title?: string;
  description?: string;
  fallbackSource?: string;
  thumbnail?: string;
}

export interface MediaOptions {
  fallbackUrl?: string;
  quality?: 'low' | 'medium' | 'high';
  cacheEnabled?: boolean;
}

/**
 * Validates if a URL is a valid image URL
 */
export const isValidImageUrl = (url: string): boolean => {
  if (!url || typeof url !== 'string') return false;
  
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
  const lowerUrl = url.toLowerCase();
  
  return imageExtensions.some(ext => lowerUrl.includes(ext)) || 
         lowerUrl.includes('image') || 
         lowerUrl.includes('photo');
};

/**
 * Validates if a URL is a valid video URL
 */
export const isValidVideoUrl = (url: string): boolean => {
  if (!url || typeof url !== 'string') return false;
  
  const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm'];
  const lowerUrl = url.toLowerCase();
  
  return videoExtensions.some(ext => lowerUrl.includes(ext)) || 
         lowerUrl.includes('video');
};

/**
 * Validates if a URL is a YouTube URL
 */
export const isYouTubeUrl = (url: string): boolean => {
  if (!url || typeof url !== 'string') return false;
  
  return url.includes('youtube.com') || url.includes('youtu.be');
};

/**
 * Extracts YouTube video ID from URL
 */
export const extractYouTubeVideoId = (url: string): string | null => {
  if (!isYouTubeUrl(url)) return null;
  
  const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
  const match = url.match(regex);
  
  return match ? match[1] : null;
};

/**
 * Gets YouTube thumbnail URL from video ID
 */
export const getYouTubeThumbnail = (videoId: string, quality: 'default' | 'medium' | 'high' = 'medium'): string => {
  const qualities = {
    default: 'default',
    medium: 'mqdefault',
    high: 'hqdefault'
  };
  
  return `https://img.youtube.com/vi/${videoId}/${qualities[quality]}.jpg`;
};

/**
 * Determines media type from URL
 */
export const getMediaType = (url: string): 'image' | 'video' | 'youtube' => {
  if (isYouTubeUrl(url)) return 'youtube';
  if (isValidVideoUrl(url)) return 'video';
  return 'image'; // default to image
};

/**
 * Creates a media item from URL
 */
export const createMediaItem = (
  url: string, 
  options: MediaOptions & { title?: string; description?: string } = {}
): MediaItem => {
  const type = getMediaType(url);
  const id = Math.random().toString(36).substr(2, 9);
  
  let thumbnail = options.thumbnail;
  if (type === 'youtube' && !thumbnail) {
    const videoId = extractYouTubeVideoId(url);
    if (videoId) {
      thumbnail = getYouTubeThumbnail(videoId, 'medium');
    }
  }
  
  return {
    id,
    type,
    source: url,
    title: options.title,
    description: options.description,
    fallbackSource: options.fallbackUrl,
    thumbnail
  };
};

/**
 * Batch creates media items from URL array
 */
export const createMediaItems = (
  urls: string[], 
  options: MediaOptions = {}
): MediaItem[] => {
  return urls.map(url => createMediaItem(url, options));
};

/**
 * Validates media item
 */
export const isValidMediaItem = (item: MediaItem): boolean => {
  return !!(item.id && item.type && item.source);
};

/**
 * Filters valid media items
 */
export const filterValidMediaItems = (items: MediaItem[]): MediaItem[] => {
  return items.filter(isValidMediaItem);
};

/**
 * Gets optimized image URL based on device capabilities
 */
export const getOptimizedImageUrl = (
  url: string, 
  width: number, 
  height: number, 
  quality: 'low' | 'medium' | 'high' = 'medium'
): string => {
  // This is a placeholder for image optimization
  // In a real app, you might use services like Cloudinary, ImageKit, etc.
  return url;
};

/**
 * Default fallback image URL
 */
export const DEFAULT_FALLBACK_IMAGE = 'https://via.placeholder.com/400x300/cccccc/666666?text=No+Image';

/**
 * Gets fallback image for different contexts
 */
export const getFallbackImage = (context: 'exercise' | 'user' | 'general' = 'general'): string => {
  const fallbacks = {
    exercise: 'https://via.placeholder.com/400x300/667eea/ffffff?text=Exercise',
    user: 'https://via.placeholder.com/200x200/667eea/ffffff?text=User',
    general: DEFAULT_FALLBACK_IMAGE
  };
  
  return fallbacks[context];
};