<<<<<<<
import React, { useState } from 'react';



import { ScrollView, StyleSheet, View, TouchableOpacity, Modal, Alert } from 'react-native';



import { ThemedView } from '@/components/ThemedView';



import { ThemedText } from '@/components/ThemedText';



import SafeImage from '@/components/SafeImage';



import MediaRenderer from '@/components/MediaRenderer';



import { useThemeColor } from '@/hooks/useThemeColor';







const Favorites = () => {



  const [selectedVideo, setSelectedVideo] = useState(null);



  const [videoModalVisible, setVideoModalVisible] = useState(false);







  const backgroundColor = useThemeColor({}, 'background');



  const textColor = useThemeColor({}, 'text');



  const tintColor = useThemeColor({}, 'tint');







  // Sample favorite exercises with proper image URLs



  const favoriteExercises = [



    {



      id: '1',



      name: 'Push-ups',



      image: 'https://images.pexels.com/photos/416778/pexels-photo-416778.jpeg?auto=compress&w=400&h=300&fit=crop',



      fallbackImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',



      videoUrl: 'https://www.youtube.com/watch?v=tWjBnQX3if0',



      difficulty: 'Beginner',



      duration: '5 min',



      description: 'Classic upper body exercise for chest, shoulders, and triceps',



    },



    {



      id: '2',



      name: 'Squats',



      image: 'https://images.pexels.com/photos/863926/pexels-photo-863926.jpeg?auto=compress&w=400&h=300&fit=crop',



      fallbackImage: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop',



      videoUrl: 'https://www.youtube.com/watch?v=HHRDXEG1YCU',



      difficulty: 'Beginner',



      duration: '8 min',



      description: 'Lower body exercise targeting quads, glutes, and hamstrings',



    },



    {



      id: '3',



      name: 'Planks',



      image: 'https://images.pexels.com/photos/3076509/pexels-photo-3076509.jpeg?auto=compress&w=400&h=300&fit=crop',



      fallbackImage: 'https://images.unsplash.com/photo-1506629905607-d5b4e2e0e4b5?w=400&h=300&fit=crop',



      videoUrl: 'https://www.youtube.com/watch?v=Kj5VFsD1NkA',



      difficulty: 'Intermediate',



      duration: '3 min',



      description: 'Core strengthening exercise for stability and endurance',



    },



    {



      id: '4',



      name: 'Burpees',



      image: 'https://images.pexels.com/photos/3757942/pexels-photo-3757942.jpeg?auto=compress&w=400&h=300&fit=crop',



      fallbackImage: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=400&h=300&fit=crop',



      videoUrl: 'https://www.youtube.com/watch?v=tWjBnQX3if0',



      difficulty: 'Advanced',



      duration: '10 min',



      description: 'Full-body high-intensity exercise combining strength and cardio',



    },



    {



      id: '5',



      name: 'Mountain Climbers',



      image: 'https://images.pexels.com/photos/3768916/pexels-photo-3768916.jpeg?auto=compress&w=400&h=300&fit=crop',



      fallbackImage: 'https://images.unsplash.com/photo-1588286840104-8957b019727f?w=400&h=300&fit=crop',



      videoUrl: 'https://www.youtube.com/watch?v=HHRDXEG1YCU',



      difficulty: 'Intermediate',



      duration: '6 min',



      description: 'Dynamic cardio exercise that targets core and legs',



    },



    {



      id: '6',



      name: 'Lunges',



      image: 'https://images.pexels.com/photos/3757376/pexels-photo-3757376.jpeg?auto=compress&w=400&h=300&fit=crop',



      fallbackImage: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400&h=300&fit=crop',



      videoUrl: 'https://www.youtube.com/watch?v=Kj5VFsD1NkA',



      difficulty: 'Beginner',



      duration: '7 min',



      description: 'Unilateral leg exercise for balance and strength',



    },



  ];







  const handleExercisePress = (exercise) => {



    console.log('Exercise pressed:', exercise.name);



    Alert.alert(



      exercise.name,



      exercise.description,



      [



        { text: 'Cancel', style: 'cancel' },



        { text: 'Watch Video', onPress: () => openVideoModal(exercise) },



        { text: 'Start Workout', onPress: () => startWorkout(exercise) },



      ]



    );



  };







  const openVideoModal = (exercise) => {



    console.log('📹 Video URL:', exercise.videoUrl);



    console.log('📹 Embeddable URL:', `${exercise.videoUrl.replace('watch?v=', 'embed/')}?autoplay=1&rel=0&showinfo=0&controls=1`);



    setSelectedVideo(exercise);



    setVideoModalVisible(true);



  };







  const closeVideoModal = () => {



    setSelectedVideo(null);



    setVideoModalVisible(false);



  };







  const startWorkout = (exercise) => {



    console.log('Starting workout:', exercise.name);



    Alert.alert('Workout Started', `Starting ${exercise.name} workout for ${exercise.duration}`);



  };







  const getDifficultyColor = (difficulty) => {



    switch (difficulty.toLowerCase()) {



      case 'beginner':



        return '#4CAF50';



      case 'intermediate':



        return '#FF9800';



      case 'advanced':



        return '#F44336';



      default:



        return tintColor;



    }



  };







  const renderExerciseCard = (exercise) => (



    <TouchableOpacity



      key={exercise.id}



      style={[styles.exerciseCard, { backgroundColor }]}



      onPress={() => handleExercisePress(exercise)}



      activeOpacity={0.8}



    >



      <SafeImage



        source={{ uri: exercise.image }}



        fallbackSource={{ uri: exercise.fallbackImage }}



        style={styles.exerciseImage}



        resizeMode="cover"



        showLoadingIndicator={true}



        errorText="Image not available"



      />



      



      <View style={styles.exerciseInfo}>



        <ThemedText style={styles.exerciseName}>



          {exercise.name}



        </ThemedText>



        



        <View style={styles.exerciseDetails}>



          <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(exercise.difficulty) }]}>



            <ThemedText style={styles.difficultyText}>



              {exercise.difficulty}



            </ThemedText>



          </View>



          



          <ThemedText style={styles.durationText}>



            {exercise.duration}



          </ThemedText>



        </View>



        



        <ThemedText style={styles.exerciseDescription} numberOfLines={2}>



          {exercise.description}



        </ThemedText>



      </View>



    </TouchableOpacity>



  );







  return (



    <ScrollView style={[styles.container, { backgroundColor }]}>



      <ThemedView style={styles.content}>



        {/* Header */}



        <View style={styles.header}>



          <ThemedText type="title" style={styles.title}>



            Favorite Exercises



          </ThemedText>



          <ThemedText style={styles.subtitle}>



            Your saved workout routines



          </ThemedText>



        </View>







        {/* Exercise Grid */}



        <View style={styles.exerciseGrid}>



          {favoriteExercises.map(renderExerciseCard)}



        </View>







        {/* Video Modal */}



        <Modal



          visible={videoModalVisible}



          animationType="slide"



          presentationStyle="pageSheet"



          onRequestClose={closeVideoModal}



        >



          <View style={[styles.modalContainer, { backgroundColor }]}>



            <View style={styles.modalHeader}>



              <ThemedText type="subtitle" style={styles.modalTitle}>



                {selectedVideo?.name}



              </ThemedText>



              <TouchableOpacity



                style={styles.closeButton}



                onPress={closeVideoModal}



              >



                <ThemedText style={[styles.closeButtonText, { color: tintColor }]}>



                  Close



                </ThemedText>



              </TouchableOpacity>



            </View>



            



            {selectedVideo && (



              <View style={styles.videoContainer}>



                <MediaRenderer



                  type="youtube"



                  source={selectedVideo.videoUrl}



                  style={styles.video}



                  autoplay={false}



                  controls={true}



                />



              </View>



            )}



            



            <View style={styles.modalContent}>



              <ThemedText style={styles.modalDescription}>



                {selectedVideo?.description}



              </ThemedText>



            </View>



          </View>



        </Modal>



      </ThemedView>



    </ScrollView>



  );



};







const styles = StyleSheet.create({



  container: {



    flex: 1,



  },



  content: {



    padding: 16,



  },



  header: {



    marginBottom: 24,



    alignItems: 'center',



  },



  title: {



    marginBottom: 8,



  },



  subtitle: {



    opacity: 0.7,



    textAlign: 'center',



  },



  exerciseGrid: {



    gap: 16,



  },



  exerciseCard: {



    flexDirection: 'row',



    borderRadius: 12,



    overflow: 'hidden',



    elevation: 2,



    shadowColor: '#000',



    shadowOffset: { width: 0, height: 2 },



    shadowOpacity: 0.1,



    shadowRadius: 4,



    marginBottom: 16,



  },



  exerciseImage: {



    width: 120,



    height: 100,



  },



  exerciseInfo: {



    flex: 1,



    padding: 12,



    justifyContent: 'space-between',



  },



  exerciseName: {



    fontSize: 16,



    fontWeight: '600',



    marginBottom: 8,



  },



  exerciseDetails: {



    flexDirection: 'row',



    alignItems: 'center',



    marginBottom: 8,



  },



  difficultyBadge: {



    paddingHorizontal: 8,



    paddingVertical: 4,



    borderRadius: 12,



    marginRight: 12,



  },



  difficultyText: {



    color: 'white',



    fontSize: 12,



    fontWeight: '500',



  },



  durationText: {



    fontSize: 12,



    opacity: 0.7,



  },



  exerciseDescription: {



    fontSize: 14,



    opacity: 0.8,



    lineHeight: 18,



  },



  modalContainer: {



    flex: 1,



  },



  modalHeader: {



    flexDirection: 'row',



    justifyContent: 'space-between',



    alignItems: 'center',



    padding: 16,



    borderBottomWidth: 1,



    borderBottomColor: '#E0E0E0',



  },



  modalTitle: {



    flex: 1,



  },



  closeButton: {



    padding: 8,



  },



  closeButtonText: {



    fontSize: 16,



    fontWeight: '500',



  },



  videoContainer: {



    margin: 16,



  },



  video: {



    width: '100%',



    height: 220,



    borderRadius: 12,



  },



  modalContent: {



    padding: 16,



  },



  modalDescription: {



    fontSize: 16,



    lineHeight: 24,



  },



});







export default Favorites;



=======
import React, { useState } from 'react';

import { ScrollView, StyleSheet, View, TouchableOpacity, Modal, Alert } from 'react-native';

import { ThemedView } from '@/components/ThemedView';

import { ThemedText } from '@/components/ThemedText';

import SafeImage from '@/components/SafeImage';

import MediaRenderer from '@/components/MediaRenderer';

import { useThemeColor } from '@/hooks/useThemeColor';



const Favorites = () => {

  const [selectedVideo, setSelectedVideo] = useState(null);

  const [videoModalVisible, setVideoModalVisible] = useState(false);



  const backgroundColor = useThemeColor({}, 'background');

  const textColor = useThemeColor({}, 'text');

  const tintColor = useThemeColor({}, 'tint');



  // Sample favorite exercises with proper image URLs

  const favoriteExercises = [

    {

      id: '1',

      name: 'Push-ups',

      image: 'https://images.pexels.com/photos/416778/pexels-photo-416778.jpeg?auto=compress&w=400&h=300&fit=crop',

      fallbackImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',

      videoUrl: 'https://www.youtube.com/watch?v=tWjBnQX3if0',

      difficulty: 'Beginner',

      duration: '5 min',

      description: 'Classic upper body exercise for chest, shoulders, and triceps',

    },

    {

      id: '2',

      name: 'Squats',

      image: 'https://images.pexels.com/photos/863926/pexels-photo-863926.jpeg?auto=compress&w=400&h=300&fit=crop',

      fallbackImage: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop',

      videoUrl: 'https://www.youtube.com/watch?v=HHRDXEG1YCU',

      difficulty: 'Beginner',

      duration: '8 min',

      description: 'Lower body exercise targeting quads, glutes, and hamstrings',

    },

    {

      id: '3',

      name: 'Planks',

      image: 'https://images.pexels.com/photos/3076509/pexels-photo-3076509.jpeg?auto=compress&w=400&h=300&fit=crop',

      fallbackImage: 'https://images.unsplash.com/photo-1506629905607-d5b4e2e0e4b5?w=400&h=300&fit=crop',

      videoUrl: 'https://www.youtube.com/watch?v=Kj5VFsD1NkA',

      difficulty: 'Intermediate',

      duration: '3 min',

      description: 'Core strengthening exercise for stability and endurance',

    },

    {

      id: '4',

      name: 'Burpees',

      image: 'https://images.pexels.com/photos/3757942/pexels-photo-3757942.jpeg?auto=compress&w=400&h=300&fit=crop',

      fallbackImage: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=400&h=300&fit=crop',

      videoUrl: 'https://www.youtube.com/watch?v=tWjBnQX3if0',

      difficulty: 'Advanced',

      duration: '10 min',

      description: 'Full-body high-intensity exercise combining strength and cardio',

    },

    {

      id: '5',

      name: 'Mountain Climbers',

      image: 'https://images.pexels.com/photos/3768916/pexels-photo-3768916.jpeg?auto=compress&w=400&h=300&fit=crop',

      fallbackImage: 'https://images.unsplash.com/photo-1588286840104-8957b019727f?w=400&h=300&fit=crop',

      videoUrl: 'https://www.youtube.com/watch?v=HHRDXEG1YCU',

      difficulty: 'Intermediate',

      duration: '6 min',

      description: 'Dynamic cardio exercise that targets core and legs',

    },

    {

      id: '6',

      name: 'Lunges',

      image: 'https://images.pexels.com/photos/3757376/pexels-photo-3757376.jpeg?auto=compress&w=400&h=300&fit=crop',

      fallbackImage: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400&h=300&fit=crop',

      videoUrl: 'https://www.youtube.com/watch?v=Kj5VFsD1NkA',

      difficulty: 'Beginner',

      duration: '7 min',

      description: 'Unilateral leg exercise for balance and strength',

    },

  ];



  const handleExercisePress = (exercise) => {

    console.log('Exercise pressed:', exercise.name);

    Alert.alert(

      exercise.name,

      exercise.description,

      [

        { text: 'Cancel', style: 'cancel' },

        { text: 'Watch Video', onPress: () => openVideoModal(exercise) },

        { text: 'Start Workout', onPress: () => startWorkout(exercise) },

      ]

    );

  };



  const openVideoModal = (exercise) => {

    console.log('📹 Video URL:', exercise.videoUrl);

    console.log('📹 Embeddable URL:', `${exercise.videoUrl.replace('watch?v=', 'embed/')}?autoplay=1&rel=0&showinfo=0&controls=1`);

    setSelectedVideo(exercise);

    setVideoModalVisible(true);

  };



  const closeVideoModal = () => {

    setSelectedVideo(null);

    setVideoModalVisible(false);

  };



  const startWorkout = (exercise) => {

    console.log('Starting workout:', exercise.name);

    Alert.alert('Workout Started', `Starting ${exercise.name} workout for ${exercise.duration}`);

  };



  const getDifficultyColor = (difficulty) => {

    switch (difficulty.toLowerCase()) {

      case 'beginner':

        return '#4CAF50';

      case 'intermediate':

        return '#FF9800';

      case 'advanced':

        return '#F44336';

      default:

        return tintColor;

    }

  };



  const renderExerciseCard = (exercise) => (

    <TouchableOpacity

      key={exercise.id}

      style={[styles.exerciseCard, { backgroundColor }]}

      onPress={() => handleExercisePress(exercise)}

      activeOpacity={0.8}

    >

      <SafeImage

        source={{ uri: exercise.image }}

        fallbackSource={{ uri: exercise.fallbackImage }}

        style={styles.exerciseImage}

        resizeMode="cover"

        showLoadingIndicator={true}

        errorText="Image not available"

      />

      

      <View style={styles.exerciseInfo}>

        <ThemedText style={styles.exerciseName}>

          {exercise.name}

        </ThemedText>

        

        <View style={styles.exerciseDetails}>

          <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(exercise.difficulty) }]}>

            <ThemedText style={styles.difficultyText}>

              {exercise.difficulty}

            </ThemedText>

          </View>

          

          <ThemedText style={styles.durationText}>

            {exercise.duration}

          </ThemedText>

        </View>

        

        <ThemedText style={styles.exerciseDescription} numberOfLines={2}>

          {exercise.description}

        </ThemedText>

      </View>

    </TouchableOpacity>

  );



  return (

    <ScrollView style={[styles.container, { backgroundColor }]}>

      <ThemedView style={styles.content}>

        {/* Header */}

        <View style={styles.header}>

          <ThemedText type="title" style={styles.title}>

            Favorite Exercises

          </ThemedText>

          <ThemedText style={styles.subtitle}>

            Your saved workout routines

          </ThemedText>

        </View>



        {/* Exercise Grid */}

        <View style={styles.exerciseGrid}>

          {favoriteExercises.map(renderExerciseCard)}

        </View>



        {/* Video Modal */}

        <Modal

          visible={videoModalVisible}

          animationType="slide"

          presentationStyle="pageSheet"

          onRequestClose={closeVideoModal}

        >

          <View style={[styles.modalContainer, { backgroundColor }]}>

            <View style={styles.modalHeader}>

              <ThemedText type="subtitle" style={styles.modalTitle}>

                {selectedVideo?.name}

              </ThemedText>

              <TouchableOpacity

                style={styles.closeButton}

                onPress={closeVideoModal}

              >

                <ThemedText style={[styles.closeButtonText, { color: tintColor }]}>

                  Close

                </ThemedText>

              </TouchableOpacity>

            </View>

            

            {selectedVideo && (

              <View style={styles.videoContainer}>

                <MediaRenderer

                  type="youtube"

                  source={selectedVideo.videoUrl}

                  style={styles.video}

                  autoplay={false}

                  controls={true}

                />

              </View>

            )}

            

            <View style={styles.modalContent}>

              <ThemedText style={styles.modalDescription}>

                {selectedVideo?.description}

              </ThemedText>

            </View>

          </View>

        </Modal>

      </ThemedView>

    </ScrollView>

  );

};



const styles = StyleSheet.create({

  container: {

    flex: 1,

  },

  content: {

    padding: 16,

  },

  header: {

    marginBottom: 24,

    alignItems: 'center',

  },

  title: {

    marginBottom: 8,

  },

  subtitle: {

    opacity: 0.7,

    textAlign: 'center',

  },

  exerciseGrid: {

    gap: 16,

  },

  exerciseCard: {

    flexDirection: 'row',

    borderRadius: 12,

    overflow: 'hidden',

    elevation: 2,

    shadowColor: '#000',

    shadowOffset: { width: 0, height: 2 },

    shadowOpacity: 0.1,

    shadowRadius: 4,

    marginBottom: 16,

  },

  exerciseImage: {

    width: 120,

    height: 100,

  },

  exerciseInfo: {

    flex: 1,

    padding: 12,

    justifyContent: 'space-between',

  },

  exerciseName: {

    fontSize: 16,

    fontWeight: '600',

    marginBottom: 8,

  },

  exerciseDetails: {

    flexDirection: 'row',

    alignItems: 'center',

    marginBottom: 8,

  },

  difficultyBadge: {

    paddingHorizontal: 8,

    paddingVertical: 4,

    borderRadius: 12,

    marginRight: 12,

  },

  difficultyText: {

    color: 'white',

    fontSize: 12,

    fontWeight: '500',

  },

  durationText: {

    fontSize: 12,

    opacity: 0.7,

  },

  exerciseDescription: {

    fontSize: 14,

    opacity: 0.8,

    lineHeight: 18,

  },

  modalContainer: {

    flex: 1,

  },

  modalHeader: {

    flexDirection: 'row',

    justifyContent: 'space-between',

    alignItems: 'center',

    padding: 16,

    borderBottomWidth: 1,

    borderBottomColor: '#E0E0E0',

  },

  modalTitle: {

    flex: 1,

  },

  closeButton: {

    padding: 8,

  },

  closeButtonText: {

    fontSize: 16,

    fontWeight: '500',

  },

  videoContainer: {

    margin: 16,

  },

  video: {

    width: '100%',

    height: 220,

    borderRadius: 12,

  },

  modalContent: {

    padding: 16,

  },

  modalDescription: {

    fontSize: 16,

    lineHeight: 24,

  },

});



export default Favorites;

>>>>>>>
