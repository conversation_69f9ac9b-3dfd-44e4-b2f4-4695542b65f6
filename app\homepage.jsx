// This file is used in the project. Do NOT delete.
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Dimensions,
  Platform,
  StatusBar,
  SafeAreaView,
  Animated as RNAnimated,
  Modal,
  TextInput,
  Alert,
  Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInLeft } from 'react-native-reanimated';
import { useUser } from '../context/UserContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import RealTimeTracker from '../components/RealTimeTracker';
import SleepTracker from '../components/SleepTracker';
import StepCounter from '../components/StepCounter';
import SafeImage from '../components/SafeImage';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from '../utils/responsive';

const { width, height } = Dimensions.get('window');

// Daily Calendar Component
function DailyCalendar({ userEmail }) {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [workouts, setWorkouts] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [completedDays, setCompletedDays] = useState(new Set());
  const [newWorkout, setNewWorkout] = useState({
    type: '',
    duration: '',
    notes: '',
    completed: false
  });
  
  useEffect(() => {
    loadWorkouts();
    loadCompletedDays();
  }, [selectedDate, currentMonth]);

  const loadWorkouts = async () => {
    try {
      const dateStr = selectedDate.toISOString().split('T')[0];
      const workoutKey = `@daily_workouts:${userEmail}:${dateStr}`;
      const savedWorkouts = await AsyncStorage.getItem(workoutKey);
      
      if (savedWorkouts) {
        setWorkouts(JSON.parse(savedWorkouts));
      } else {
        setWorkouts([]);
      }
    } catch (error) {
      console.error('Error loading workouts:', error);
      setWorkouts([]);
    }
  };

  const loadCompletedDays = async () => {
    try {
      const completedKey = `@completed_days:${userEmail}`;
      const savedCompleted = await AsyncStorage.getItem(completedKey);
      if (savedCompleted) {
        setCompletedDays(new Set(JSON.parse(savedCompleted)));
      }
    } catch (error) {
      console.error('Error loading completed days:', error);
    }
  };

  const markDayCompleted = async (date) => {
    try {
      const dateStr = date.toISOString().split('T')[0];
      const newCompletedDays = new Set(completedDays);
      newCompletedDays.add(dateStr);
      setCompletedDays(newCompletedDays);
      
      const completedKey = `@completed_days:${userEmail}`;
      await AsyncStorage.setItem(completedKey, JSON.stringify([...newCompletedDays]));
    } catch (error) {
      console.error('Error marking day completed:', error);
    }
  };

  const addWorkout = async () => {
    try {
      const dateStr = selectedDate.toISOString().split('T')[0];
      const workoutKey = `@daily_workouts:${userEmail}:${dateStr}`;
      const workout = {
        id: Date.now().toString(),
        ...newWorkout,
        date: dateStr,
        timestamp: new Date().toISOString(),
        completed: true
      };
      
      const updatedWorkouts = [...workouts, workout];
      await AsyncStorage.setItem(workoutKey, JSON.stringify(updatedWorkouts));
      setWorkouts(updatedWorkouts);
      setNewWorkout({ type: '', duration: '', notes: '', completed: false });
      setShowAddModal(false);
      
      // Mark the day as completed
      await markDayCompleted(selectedDate);
      Alert.alert('Success', 'Workout added and day marked as completed!');
    } catch (error) {
      console.error('Error adding workout:', error);
      Alert.alert('Error', 'Failed to add workout');
    }
  };

  const getDayName = (date) => {
    return date.toLocaleDateString('en-US', { weekday: 'short' });
  };

  const getDayNumber = (date) => {
    return date.getDate();
  };

  const isToday = (date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };
  
  // Function to check if a date is in the past
  const isPastDate = (date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    date.setHours(0, 0, 0, 0);
    return date < today;
  };
  
  // Function to check if a date is selectable
  const isDateSelectable = (date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    date.setHours(0, 0, 0, 0);
    
    // If it's today or a future date, it's selectable
    if (date >= today) return true;
    
    // If it's a past date, check if it's completed
    const dateStr = date.toISOString().split('T')[0];
    return completedDays.has(dateStr);
  };

  const renderCalendarDay = (date) => {
    if (!date) return <View key={`empty-${Math.random()}`} style={styles.calendarDay} />;
    
    const dateStr = date.toISOString().split('T')[0];
    const dayWorkouts = workouts.filter(w => w.date === dateStr);
    const isSelected = selectedDate.toDateString() === date.toDateString();
    const isCompletedDay = completedDays.has(dateStr);
    const isPast = isPastDate(new Date(date));
    const isSelectable = isDateSelectable(new Date(date));
    
    // Clone the date to avoid modifying the original
    const clonedDate = new Date(date);
    
    return (
      <TouchableOpacity
        key={dateStr}
        style={[
          styles.calendarDay,
          isToday(date) && styles.calendarDayToday,
          isSelected && styles.calendarDaySelected,
          isCompletedDay && styles.calendarDayCompleted,
          dayWorkouts.length > 0 && styles.calendarDayWithWorkout,
          isPast && !isCompletedDay && styles.calendarDayPast,
          !isSelectable && styles.calendarDayDisabled
        ]}
        onPress={() => {
          // Only allow selecting today or future dates, or completed past dates
          if (isSelectable) {
            setSelectedDate(clonedDate);
          } else {
            // Show message that past dates can't be selected unless completed
            Alert.alert(
              "Date Selection",
              "You can only select today, future dates, or past dates that have been completed.",
              [{ text: "OK" }]
            );
          }
        }}
        disabled={!isSelectable}
      >
        <Text style={[
          styles.calendarDayNumber,
          isToday(date) && styles.calendarDayNumberToday,
          isSelected && styles.calendarDayNumberSelected,
          isCompletedDay && styles.calendarDayNumberCompleted,
          isPast && !isCompletedDay && styles.calendarDayNumberPast,
          !isSelectable && styles.calendarDayNumberDisabled
        ]}>
          {getDayNumber(date)}
        </Text>
        {isCompletedDay && (
          <View style={styles.workoutIndicator}>
            <Ionicons name="checkmark-circle" size={12} color="#4CAF50" />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const generateCalendarDays = () => {
    const days = [];
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    // Get the first day of the month
    const firstDayOfMonth = new Date(year, month, 1);
    
    // Get the last day of the month
    const lastDayOfMonth = new Date(year, month + 1, 0);
    
    // Get the day of the week for the first day (0 = Sunday, 1 = Monday, etc.)
    const firstDayOfWeek = firstDayOfMonth.getDay();
    
    // Add empty days for the days before the first day of the month
    for (let i = 0; i < firstDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add all days of the month
    for (let i = 1; i <= lastDayOfMonth.getDate(); i++) {
      const day = new Date(year, month, i);
      days.push(day);
    }
    
    // Add empty days to complete the last week if needed
    const remainingDays = 7 - (days.length % 7);
    if (remainingDays < 7) {
      for (let i = 0; i < remainingDays; i++) {
        days.push(null);
      }
    }
    
    return days;
  };
  
  const goToPreviousMonth = () => {
    setCurrentMonth(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentMonth(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));
  };

  return (
    <View style={styles.calendarContainer}>
      <View style={styles.calendarHeader}>
        <Text style={styles.calendarTitle}>Daily Calendar</Text>
        <TouchableOpacity
          style={styles.addWorkoutButton}
          onPress={() => setShowAddModal(true)}
        >
          <Ionicons name="add" size={20} color="#fff" />
        </TouchableOpacity>
      </View>
      
      <View style={styles.calendarMonthHeader}>
        <TouchableOpacity onPress={goToPreviousMonth} style={styles.calendarNavButton}>
          <Ionicons name="chevron-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.calendarMonthTitle}>
          {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
        </Text>
        <TouchableOpacity onPress={goToNextMonth} style={styles.calendarNavButton}>
          <Ionicons name="chevron-forward" size={24} color="#fff" />
        </TouchableOpacity>
      </View>
      
      <View style={styles.calendarWeekdayHeader}>
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
          <Text key={index} style={styles.calendarWeekdayText}>{day}</Text>
        ))}
      </View>
      
      <View style={styles.calendarGrid}>
        {generateCalendarDays().map((day, index) => renderCalendarDay(day))}
      </View>
      
      <View style={styles.selectedDateInfo}>
        <Text style={styles.selectedDateText}>
          {selectedDate.toLocaleDateString('en-US', { 
            weekday: 'long', 
            month: 'long', 
            day: 'numeric' 
          })}
        </Text>
        {workouts.length > 0 ? (
          <View style={styles.workoutList}>
            {workouts.map((workout, index) => (
              <View key={workout.id} style={styles.workoutItem}>
                <Ionicons name="fitness" size={16} color="#667eea" />
                <Text style={styles.workoutText}>
                  {workout.type} - {workout.duration} min
                </Text>
              </View>
            ))}
          </View>
        ) : (
          <Text style={styles.noWorkoutsText}>No workouts scheduled</Text>
        )}
      </View>

      {/* Add Workout Modal */}
      <Modal
        visible={showAddModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAddModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add Workout</Text>
              <TouchableOpacity onPress={() => setShowAddModal(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            
            <TextInput
              style={styles.input}
              placeholder="Workout Type"
              placeholderTextColor="rgba(255,255,255,0.5)"
              value={newWorkout.type}
              onChangeText={(text) => setNewWorkout({...newWorkout, type: text})}
            />
            
            <TextInput
              style={styles.input}
              placeholder="Duration (minutes)"
              placeholderTextColor="rgba(255,255,255,0.5)"
              value={newWorkout.duration}
              onChangeText={(text) => setNewWorkout({...newWorkout, duration: text})}
              keyboardType="numeric"
            />
            
            <TextInput
              style={[styles.input, styles.notesInput]}
              placeholder="Notes (optional)"
              placeholderTextColor="rgba(255,255,255,0.5)"
              value={newWorkout.notes}
              onChangeText={(text) => setNewWorkout({...newWorkout, notes: text})}
              multiline
            />
            
            <TouchableOpacity
              style={styles.addWorkoutModalButton}
              onPress={addWorkout}
            >
              <Text style={styles.addWorkoutModalButtonText}>Add Workout</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

// Advanced Particle component
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new RNAnimated.Value(0)).current;
  const opacityAnim = useRef(new RNAnimated.Value(0)).current;

  useEffect(() => {
    const animSpeed = 4000 * speed + Math.random() * 2000;
    const animDelay = Math.random() * 3000 + delay;
    const animation = RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(particleAnim, { toValue: 1, duration: animSpeed, useNativeDriver: true }),
        RNAnimated.timing(particleAnim, { toValue: 0, duration: 0, useNativeDriver: true }),
      ]),
    );
    const opacitySequence = RNAnimated.sequence([
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: 500, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 1, duration: animSpeed - 1000, useNativeDriver: true }),
      RNAnimated.timing(opacityAnim, { toValue: 0, duration: 500, useNativeDriver: true }),
    ]);
    
    const timeoutId = setTimeout(() => {
      animation.start();
      RNAnimated.loop(opacitySequence).start();
    }, animDelay);

    return () => {
      clearTimeout(timeoutId);
      animation.stop();
    };
  }, [delay, speed]);

  const top = particleAnim.interpolate({ inputRange: [0, 1], outputRange: [height, -20] });
  return <RNAnimated.View style={[style, { transform: [{ translateY: top }], opacity: opacityAnim }]} />;
};

// SliderItem Component
const SliderItem = ({ item }) => {
  const [imageError, setImageError] = useState(false);
  
  const handlePress = () => {
    try {
      console.log('Slider item pressed:', item.title);
      if (item.onPress) {
        item.onPress();
      }
    } catch (error) {
      console.error('Error in slider item press:', error);
      Alert.alert('Error', 'Unable to open this workout. Please try again.');
    }
  };
  
  const handleImageError = () => {
    console.error('Image loading error for:', item.title);
    setImageError(true);
  };
  
  const sliderWidth = typeof wp === 'function' ? wp('90%') : Dimensions.get('window').width * 0.9;
  const sliderHeight = typeof hp === 'function' ? hp('25%') : 200;
  return (
    <TouchableOpacity 
      activeOpacity={0.9}
      onPress={handlePress}
      style={{
        width: sliderWidth,
        height: sliderHeight,
        borderRadius: 16,
        marginHorizontal: wp ? wp('2%') : 16,
        overflow: 'hidden',
        alignSelf: 'center',
      }}
    >
      <LinearGradient
        colors={['rgba(0,0,0,0.4)', 'rgba(0,0,0,0.6)']}
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          borderRadius: 16,
          zIndex: 1,
        }}
      />
      {!imageError ? (
        <SafeImage
          source={item.image}
          fallbackSource={{ uri: getImageByCategory('default') }}
          style={{
            width: '100%',
            height: '100%',
            borderRadius: 16,
          }}
          onError={handleImageError}
        />
      ) : (
        <View style={{
          width: '100%',
          height: '100%',
          borderRadius: 16,
          backgroundColor: 'rgba(102, 126, 234, 0.3)',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Ionicons name="fitness-outline" size={48} color="rgba(255,255,255,0.7)" />
        </View>
      )}
      <View style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        padding: 20,
        zIndex: 2,
      }}>
        <Text style={{ 
          color: '#fff', 
          fontSize: 22, 
          fontWeight: 'bold', 
          fontFamily: 'Poppins_700Bold',
          textShadowColor: 'rgba(0,0,0,0.8)',
          textShadowOffset: { width: 1, height: 1 },
          textShadowRadius: 3,
        }}>
          {item.title}
        </Text>
        {item.subtitle && (
          <Text style={{ 
            color: '#fff', 
            fontSize: 14, 
            fontFamily: 'Poppins_400Regular', 
            marginTop: 4,
            textShadowColor: 'rgba(0,0,0,0.8)',
            textShadowOffset: { width: 1, height: 1 },
            textShadowRadius: 2,
          }}>
            {item.subtitle}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

// ImageSlider Component
function ImageSlider({ images }) {
  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef(null);

  // Validate images array
  const validImages = images && Array.isArray(images) && images.length > 0 ? images : [];

  // Define sliderWidth and sliderHeight here for use in FlatList style
  const sliderWidth = typeof wp === 'function' ? wp('90%') : Dimensions.get('window').width * 0.9;
  const sliderHeight = typeof hp === 'function' ? hp('25%') : 200;

  useEffect(() => {
    let interval;
    // Auto-scroll every 5 seconds
    if (validImages.length > 1) {
      interval = setInterval(() => {
        if (flatListRef.current) {
          const nextIndex = (activeIndex + 1) % validImages.length;
          flatListRef.current.scrollToIndex({
            index: nextIndex,
            animated: true,
          });
          setActiveIndex(nextIndex);
        }
      }, 5000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [activeIndex, validImages.length]);

  const handleScroll = (event) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    const index = Math.round(scrollPosition / sliderWidth);
    setActiveIndex(Math.max(0, Math.min(index, validImages.length - 1)));
  };

  const renderItem = ({ item, index }) => {
    return <SliderItem key={`slide-${index}`} item={item} />;
  };

  // If no images, show a placeholder
  if (validImages.length === 0) {
    return (
      <View style={{ marginVertical: 16, marginHorizontal: 16, width: '100%' }}>
        <Text style={{
          color: '#fff',
          fontSize: 20,
          fontWeight: 'bold',
          marginBottom: 12,
          fontFamily: 'Poppins_600SemiBold'
        }}>
          Featured Workouts
        </Text>
        <View style={{
          width: Dimensions.get('window').width - 32,
          height: 200,
          borderRadius: 16,
          backgroundColor: 'rgba(255,255,255,0.1)',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Ionicons name="fitness-outline" size={48} color="rgba(255,255,255,0.5)" />
          <Text style={{ color: 'rgba(255,255,255,0.7)', marginTop: 8, fontFamily: 'Poppins_400Regular' }}>
            Loading featured workouts...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View
      style={{
        marginVertical: 16,
        marginHorizontal: 0,
        width: '100%',
        alignItems: 'center',
      }}
    >
      <Text
        style={{
          color: '#fff',
          fontSize: 20,
          fontWeight: 'bold',
          marginBottom: 12,
          marginLeft: 16,
          fontFamily: 'Poppins_600SemiBold',
        }}
      >
        Featured Workouts
      </Text>
      <FlatList
        ref={flatListRef}
        data={validImages}
        renderItem={renderItem}
        keyExtractor={(item, index) => `slide-${index}`}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        contentContainerStyle={{
          paddingRight: typeof wp === 'function' ? wp('2%') : 16,
          alignItems: 'center',
        }}
        snapToInterval={sliderWidth}
        snapToAlignment="center"
        decelerationRate={Platform.OS === 'android' ? 0.98 : 'fast'}
        getItemLayout={(data, index) => ({
          length: sliderWidth,
          offset: sliderWidth * index,
          index,
        })}
        onError={(error) => {
          console.error('FlatList error:', error);
        }}
        showsVerticalScrollIndicator={false}
        style={{
          flexGrow: 0,
          height: sliderHeight + 8,
        }}
      />
      {validImages.length > 1 && (
        <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: 10 }}>
          {validImages.map((_, index) => (
            <View
              key={`dot-${index}`}
              style={{
                width: 8,
                height: 8,
                borderRadius: 4,
                backgroundColor:
                  index === activeIndex ? '#667eea' : 'rgba(255,255,255,0.5)',
                marginHorizontal: 4,
              }}
            />
          ))}
        </View>
      )}
    </View>
  );
}

// Remove this line as it's not needed

const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

// Add FloatingShape for animated background
const FloatingShape = ({ style, delay = 0, rotationSpeed = 1 }) => {
  const floatAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
        Animated.timing(floatAnim, {
          toValue: 0,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
    Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 15000 * rotationSpeed,
        useNativeDriver: true,
      })
    ).start();
    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [delay, rotationSpeed]);

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [
            {
              translateY: floatAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -30],
              }),
            },
            {
              rotate: rotationAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '360deg'],
              }),
            },
            {
              scale: scaleAnim,
            },
          ],
        },
      ]}
    >
      <LinearGradient
        colors={['rgba(102, 126, 234, 0.3)', 'rgba(118, 75, 162, 0.2)']}
        style={{
          width: '100%',
          height: '100%',
          borderRadius: 16,
        }}
      />
    </Animated.View>
  );
};

// Update: Utility to get current user from AsyncStorage
const getCurrentUser = async () => {
  // Try to get the current user email or username from AsyncStorage
  let email = await AsyncStorage.getItem('@current_user');
  if (!email) email = await AsyncStorage.getItem('@FitnessApp:currentUser');
  if (email && email.includes('@')) return email;
  // fallback: try to get from user context
  return null;
};

// Function to fetch today's statistics for the user
const fetchTodayStats = async (userEmail) => {
  try {
    if (!userEmail) {
      console.log('No user email provided for fetching today stats');
      return null;
    }
    
    console.log(`Fetching today's stats for user: ${userEmail}`);
    
    // Get today's date in YYYY-MM-DD format
    const today = new Date();
    const dateString = today.toISOString().split('T')[0];
    
    // Try to get user-specific stats for today
    const statsKey = `@daily_stats:${userEmail}:${dateString}`;
    const statsStr = await AsyncStorage.getItem(statsKey);
    
    if (statsStr) {
      const stats = JSON.parse(statsStr);
      console.log('Today stats found:', stats);
      return stats;
    }
    
    // If no stats for today, create default stats
    const defaultStats = {
      date: dateString,
      steps: 0,
      calories: 0,
      distance: 0,
      activeMinutes: 0,
      waterIntake: 0,
      sleepHours: 0,
      heartRate: { avg: 0, min: 0, max: 0, readings: [] },
      bloodPressure: { readings: [] },
      workouts: [],
      meals: [],
      weight: 0,
      mood: '',
      notes: ''
    };
    
    // Save default stats
    await AsyncStorage.setItem(statsKey, JSON.stringify(defaultStats));
    console.log('Created default stats for today');
    
    return defaultStats;
  } catch (error) {
    console.error('Error fetching today stats:', error);
    return null;
  }
};

import { getImageByCategory } from '../constants/remoteImages';

// Utility function to get reliable image sources
const getReliableImageSource = (imageKey) => {
  const url = getImageByCategory(imageKey);
  return { uri: url || getImageByCategory('default') };
};


export default function Home() {
  const router = useRouter();
  const { currentUser } = useUser();
  const [user, setUser] = useState(null);
  const [tabIndex, setTabIndex] = useState(0);
  const [menuVisible, setMenuVisible] = useState(false);
  const [healthMetrics, setHealthMetrics] = useState({
    steps: 0,
    calories: 0,
    distance: 0,
    sleep: 0,
    water: 0,
    workouts: 0
  });
  const scrollViewRef = useRef(null);

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  // Add error state
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchUsername() {
      try {
        setError(null);
        // Try multiple sources for user data
        let savedUser = await AsyncStorage.getItem('@user_data');
        if (!savedUser) {
          savedUser = await AsyncStorage.getItem('@FitnessApp:currentUser');
        }
        if (!savedUser) {
          savedUser = await AsyncStorage.getItem('@current_user');
        }
        
        if (savedUser) {
          const userData = JSON.parse(savedUser);
          setUser(userData);
        } else {
          // Try to get from user context
          if (currentUser) {
            setUser({
              name: currentUser.name || currentUser.email?.split('@')[0] || 'Fitness Warrior',
              email: currentUser.email
            });
          } else {
            // Set default user if no data available
            setUser({
              name: 'Fitness Warrior',
              email: null
            });
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError('Failed to load user data');
        // Fallback to user context or default
        if (currentUser) {
          setUser({
            name: currentUser.name || currentUser.email?.split('@')[0] || 'Fitness Warrior',
            email: currentUser.email
          });
        } else {
          setUser({
            name: 'Fitness Warrior',
            email: null
          });
        }
      }
    }
    fetchUsername();
  }, [currentUser]);

  // Define TABS for the tabbed trackers
  const TABS = [
    {
      key: 'realtime',
      title: 'Real Time',
      icon: 'time-outline',
      component: RealTimeTracker
    },
    {
      key: 'sleep',
      title: 'Sleep',
      icon: 'moon-outline',
      component: SleepTracker
    },
    {
      key: 'steps',
      title: 'Steps',
      icon: 'walk-outline',
      component: StepCounter
    },
    {
      key: 'calendar',
      title: 'Calendar',
      icon: 'calendar-outline',
      component: () => <DailyCalendar userEmail={user?.email} />
    }
  ];

  // Define trackers for progress section
  const trackers = [
    {
      key: 'steps',
      label: 'Steps',
      value: healthMetrics.steps.toLocaleString(),
      progress: Math.min(healthMetrics.steps / 10000, 1),
      color: '#4CAF50',
      icon: 'walk'
    },
    {
      key: 'calories',
      label: 'Calories',
      value: healthMetrics.calories.toLocaleString(),
      progress: Math.min(healthMetrics.calories / 2000, 1),
      color: '#FF9800',
      icon: 'flame'
    },
    {
      key: 'distance',
      label: 'Distance',
      value: `${healthMetrics.distance.toFixed(1)} km`,
      progress: Math.min(healthMetrics.distance / 10, 1),
      color: '#2196F3',
      icon: 'map'
    },
    {
      key: 'sleep',
      label: 'Sleep',
      value: `${healthMetrics.sleep}h`,
      progress: Math.min(healthMetrics.sleep / 8, 1),
      color: '#9C27B0',
      icon: 'moon'
    }
  ];

  // Define slider images with actual workout images
  const sliderImages = [
    {
      title: 'Morning Workout',
      subtitle: 'Start your day with energy',
      image: getReliableImageSource('morning'),
      onPress: () => {
        try {
          console.log('Navigating to morning-workouts...');
          setTimeout(() => {
            router.push('/morning-workouts');
          }, 100);
        } catch (error) {
          console.error('Navigation error to morning-workouts:', error);
          Alert.alert('Error', 'Unable to open Morning Workouts. Please try again.');
        }
      }
    },
    {
      title: 'Strength Training',
      subtitle: 'Build muscle and power',
      image: getReliableImageSource('strength'),
      onPress: () => {
        try {
          console.log('Navigating to strength-training...');
          setTimeout(() => {
            router.push('/strength-training');
          }, 100);
        } catch (error) {
          console.error('Navigation error to strength-training:', error);
          Alert.alert('Error', 'Unable to open Strength Training. Please try again.');
        }
      }
    },
    {
      title: 'Cardio Blast',
      subtitle: 'Burn calories and boost endurance',
      image: getReliableImageSource('cardio'),
      onPress: () => {
        try {
          console.log('Navigating to cardio-blast...');
          setTimeout(() => {
            router.push('/cardio-blast');
          }, 100);
        } catch (error) {
          console.error('Navigation error to cardio-blast:', error);
          Alert.alert('Error', 'Unable to open Cardio Blast. Please try again.');
        }
      }
    },
    {
      title: 'Flexibility & Yoga',
      subtitle: 'Improve mobility and recovery',
      image: getReliableImageSource('flexibility'),
      onPress: () => {
        try {
          console.log('Navigating to flexibility-yoga...');
          setTimeout(() => {
            router.push('/flexibility-yoga');
          }, 100);
        } catch (error) {
          console.error('Navigation error to flexibility-yoga:', error);
          Alert.alert('Error', 'Unable to open Flexibility & Yoga. Please try again.');
        }
      }
    },
    {
      title: 'Nutrition Guide',
      subtitle: 'Fuel your fitness journey',
      image: getReliableImageSource('nutrition'),
      onPress: () => {
        try {
          console.log('Navigating to nutrition-guide...');
          setTimeout(() => {
            router.push('/nutrition-guide');
          }, 100);
        } catch (error) {
          console.error('Navigation error to nutrition-guide:', error);
          Alert.alert('Error', 'Unable to open Nutrition Guide. Please try again.');
        }
      }
    }
  ];

  // Menu items
  const menuItems = [
    { label: 'Profile', icon: 'person-circle-outline', onPress: () => router.push('/profile/user-profile') },
    { label: 'Favorites', icon: 'heart-outline', onPress: () => router.push('/favorites') },
    { label: 'Settings', icon: 'settings-outline', onPress: () => router.push('/settings') },
    { label: 'Notifications', icon: 'notifications-outline', onPress: () => router.push('/notifications') },
    { label: 'Achievements', icon: 'trophy-outline', onPress: () => router.push('/nft-achievements') },
    { label: 'Online Classes', icon: 'videocam-outline', onPress: () => router.push('/virtual-classes') },
    { label: 'Metaverse', icon: 'globe-outline', onPress: () => router.push('/social-metaverse') },
    { label: 'Logout', icon: 'log-out-outline', onPress: () => {
      Alert.alert('Logout', 'Are you sure you want to logout?', [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: () => router.push('/user/login') }
      ]);
    }}
  ];

  const handleMenuPress = (item) => {
    try {
      setMenuVisible(false);
      if (item.onPress) {
        item.onPress();
      }
    } catch (error) {
      console.error('Error handling menu press:', error);
      Alert.alert('Error', 'Unable to navigate. Please try again.');
    }
  };

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  // Error boundary
  if (error) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient colors={['#0c0c0c', '#1a1a2e']} style={StyleSheet.absoluteFill} />
        <Ionicons name="warning" size={48} color="#FF6B6B" />
        <Text style={styles.loadingText}>Something went wrong</Text>
        <Text style={{ color: 'rgba(255,255,255,0.7)', marginTop: 8, textAlign: 'center' }}>
          {error}
        </Text>
        <TouchableOpacity 
          style={{ marginTop: 20, padding: 12, backgroundColor: '#667eea', borderRadius: 8 }}
          onPress={() => window.location.reload()}
        >
          <Text style={{ color: '#fff', fontFamily: 'Poppins_600SemiBold' }}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} style={StyleSheet.absoluteFill} />
      {[...Array(15)].map((_, i) => (
        <AdvancedParticle key={i} style={[styles.particle, { left: `${Math.random() * 100}%`, width: Math.random() * 4 + 2, height: Math.random() * 4 + 2 }]} />
      ))}
      
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" />
        
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.headerTitle}>
                Hi, {user?.name || 'Fitness Warrior'}! 👋
              </Text>
              <Text style={styles.headerSubtitle}>
                Ready to crush your goals today?
              </Text>
            </View>
            <TouchableOpacity 
              style={styles.menuButton}
              onPress={() => setMenuVisible(true)}
            >
              <View style={styles.menuIconContainer}>
                <View style={styles.menuLine} />
                <View style={styles.menuLine} />
                <View style={styles.menuLine} />
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Menu Modal */}
        <Modal
          visible={menuVisible}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setMenuVisible(false)}
        >
          <View style={styles.menuOverlay}>
            <View style={styles.menuContainer}>
              <View style={styles.menuHeader}>
                <Text style={styles.menuTitle}>Menu</Text>
                <TouchableOpacity onPress={() => setMenuVisible(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              <ScrollView style={styles.menuScrollView} showsVerticalScrollIndicator={false}>
                {menuItems.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.menuItem,
                      item.label === 'Logout' && styles.menuItemLogout
                    ]}
                    onPress={() => handleMenuPress(item)}
                  >
                    <View style={styles.menuItemContent}>
                      <Ionicons name={item.icon} size={24} color={item.label === 'Logout' ? '#ff6b6b' : '#fff'} />
                      <Text style={[
                        styles.menuItemText,
                        item.label === 'Logout' && styles.menuItemTextLogout
                      ]}>
                        {item.label}
                      </Text>
                    </View>
                    <Ionicons name="chevron-forward" size={18} color="rgba(255,255,255,0.5)" />
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        </Modal>

        <ScrollView ref={scrollViewRef} contentContainerStyle={{ flexGrow: 1, paddingBottom: 100 }} showsVerticalScrollIndicator={false}>
          {/* Image Slider */}
          <ImageSlider images={sliderImages} />

          {/* Tabbed Trackers */}
          <View style={styles.tabbedContainer}>
            <View style={styles.tabHeader}>
              {TABS.map((tab, idx) => (
                <TouchableOpacity
                  key={tab.key}
                  style={[
                    styles.tabButton,
                    tabIndex === idx && styles.tabButtonActive
                  ]}
                  onPress={() => setTabIndex(idx)}
                >
                  <Ionicons 
                    name={tab.icon} 
                    size={22} 
                    color={tabIndex === idx ? '#667eea' : '#fff'} 
                  />
                  <Text style={[
                    styles.tabButtonText,
                    tabIndex === idx && styles.tabButtonTextActive
                  ]}>
                    {tab.title}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            {/* Full Screen Tab Content */}
            <View style={styles.tabContent}>
              {(() => {
                try {
                  const Component = TABS[tabIndex].component;
                  return React.createElement(Component);
                } catch (error) {
                  console.error('Error rendering tab component:', error);
                  return (
                    <View style={styles.errorContainer}>
                      <Text style={styles.errorText}>Unable to load component</Text>
                    </View>
                  );
                }
              })()}
            </View>
            {/* Removed extra box below tab view */}
          </View>

          {/* Daily Calendar Section Removed */}

          {/* Progress Trackers Section Removed */}
        </ScrollView>

        {/* Bottom Navigation Bar */}
        <View style={styles.bottomNav}>
          <TouchableOpacity style={styles.navItem} onPress={() => {
            try {
              router.push('/Home');
            } catch (error) {
              console.error('Navigation error:', error);
            }
          }}>
            <Ionicons name="barbell-outline" size={26} color="#fff" />
            <Text style={styles.navText}>Exercises</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => {
            try {
              router.push('/plans');
            } catch (error) {
              console.error('Navigation error:', error);
            }
          }}>
            <Ionicons name="restaurant-outline" size={26} color="#fff" />
            <Text style={styles.navText}>Diet Plans</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => {
            try {
              router.push('/ai-coach');
            } catch (error) {
              console.error('Navigation error:', error);
            }
          }}>
            <Ionicons name="chatbubble-ellipses-outline" size={26} color="#fff" />
            <Text style={styles.navText}>AI Chat</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => {
            try {
              router.push('/analyze');
            } catch (error) {
              console.error('Navigation error:', error);
            }
          }}>
            <Ionicons name="analytics-outline" size={26} color="#fff" />
            <Text style={styles.navText}>Daily Report</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0c0c0c',
  },
  loadingText: {
    color: '#fff',
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
  },
  particle: {
    position: 'absolute',
    backgroundColor: 'rgba(102, 126, 234, 0.6)',
    borderRadius: 2,
    zIndex: 0,
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 40 : 20,
    paddingBottom: 10,
    backgroundColor: 'transparent',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
  },
  headerSubtitle: {
    color: '#aaa',
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    marginTop: 2,
  },
  menuButton: {
    padding: 5,
  },
  menuIconContainer: {
    width: 24,
    height: 18,
    justifyContent: 'space-between',
  },
  menuLine: {
    width: '100%',
    height: 2,
    backgroundColor: '#fff',
    borderRadius: 1,
  },
  menuOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  menuContainer: {
    backgroundColor: '#1a1a2e',
    width: '75%',
    height: 'auto',
    maxHeight: '80%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 15,
    paddingBottom: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 15,
  },
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  menuTitle: {
    color: '#fff',
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
  },
  menuScrollView: {
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.08)',
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
    marginLeft: 15,
    fontWeight: '500',
  },
  menuItemLogout: {
    borderBottomColor: 'rgba(255,107,107,0.2)',
    backgroundColor: 'rgba(255,107,107,0.05)',
  },
  menuItemTextLogout: {
    color: '#ff6b6b',
  },
  tabbedContainer: {
    flex: 1,
    marginHorizontal: 16,
    marginTop: 20,
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  tabHeader: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 10,
  },
  tabButton: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    backgroundColor: 'transparent',
    minHeight: 60,
  },
  tabButtonActive: {
    backgroundColor: 'rgba(102,126,234,0.15)',
  },
  tabButtonText: {
    color: '#fff',
    fontSize: 13,
    fontFamily: 'Poppins_600SemiBold',
    marginTop: 6,
    textAlign: 'center',
  },
  tabButtonTextActive: {
    color: '#667eea',
  },
  tabContent: {
    flex: 1,
    backgroundColor: 'transparent',
    borderRadius: 12,
    paddingTop: 10,
  },
  progressSection: {
    marginHorizontal: 16,
    marginBottom: 20,
  },
  sectionTitle: {
    color: '#fff',
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    marginBottom: 12,
  },
  progressGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  progressCard: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    width: '48%',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  progressCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  progressPercentage: {
    color: '#aaa',
    fontSize: 10,
    fontFamily: 'Poppins_400Regular',
    marginTop: 4,
  },
  progressValue: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    marginBottom: 4,
  },
  progressLabel: {
    color: '#aaa',
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    marginLeft: 6,
    textAlign: 'center',
  },
  progressBar: {
    width: '100%',
    height: 6,
    backgroundColor: 'rgba(255,255,255,0.15)',
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  bottomNav: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: 'rgba(30,30,40,0.98)',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    elevation: 10,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: -2 },
    minHeight: 70,
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  navText: {
    color: '#fff',
    fontSize: 11,
    fontFamily: 'Poppins_600SemiBold',
    marginTop: 4,
    textAlign: 'center',
  },
  // Calendar Styles
  calendarContainer: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.3)',
    marginVertical: 10,
    marginHorizontal: 8,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  calendarTitle: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
  },
  addWorkoutButton: {
    backgroundColor: '#667eea',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  calendarMonthTitle: {
    color: '#fff',
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    textAlign: 'center',
    marginVertical: 10,
  },
  calendarWeekdayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 10,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
    paddingBottom: 8,
  },
  calendarWeekdayText: {
    color: '#aaa',
    fontSize: 11,
    fontFamily: 'Poppins_600SemiBold',
    width: (width - 80) / 7,
    textAlign: 'center',
  },
  calendarScrollView: {
    maxHeight: 300,
    marginBottom: 10,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  calendarDay: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 6,
    borderRadius: 12,
    width: (width - 80) / 7,
    height: 50,
    margin: 2,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.15)',
    backgroundColor: 'rgba(255,255,255,0.05)',
  },
  calendarDayToday: {
    backgroundColor: 'rgba(102,126,234,0.2)',
    borderColor: '#667eea',
  },
  calendarDaySelected: {
    backgroundColor: 'rgba(102,126,234,0.3)',
    borderColor: '#667eea',
  },
  calendarDayWithWorkout: {
    backgroundColor: 'rgba(76,175,80,0.2)',
    borderColor: '#4CAF50',
  },
  calendarDayCompleted: {
    backgroundColor: 'rgba(76,175,80,0.3)',
    borderColor: '#4CAF50',
  },
  calendarDayPast: {
    backgroundColor: 'rgba(255,255,255,0.02)',
    borderColor: 'rgba(255,255,255,0.05)',
  },
  calendarDayDisabled: {
    opacity: 0.5,
  },
  calendarDayNextAvailable: {
    backgroundColor: 'rgba(255,152,0,0.1)',
    borderColor: '#FF9800',
  },
  calendarDayName: {
    color: '#ccc',
    fontSize: 9,
    fontFamily: 'Poppins_400Regular',
    marginBottom: 2,
  },
  calendarDayNameToday: {
    color: '#667eea',
    fontFamily: 'Poppins_600SemiBold',
  },
  calendarDayNameSelected: {
    color: '#667eea',
    fontFamily: 'Poppins_600SemiBold',
  },
  calendarDayNameCompleted: {
    color: '#4CAF50',
    fontFamily: 'Poppins_600SemiBold',
  },
  calendarDayNamePast: {
    color: '#666',
  },
  calendarDayNameDisabled: {
    color: '#555',
  },
  calendarDayNameNextAvailable: {
    color: '#FF9800',
    fontFamily: 'Poppins_600SemiBold',
  },
  calendarDayNumber: {
    color: '#fff',
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
  },
  calendarDayNumberToday: {
    color: '#667eea',
  },
  calendarDayNumberSelected: {
    color: '#667eea',
  },
  calendarDayNumberCompleted: {
    color: '#4CAF50',
  },
  calendarDayNumberPast: {
    color: '#666',
  },
  calendarDayNumberDisabled: {
    color: '#555',
  },
  calendarDayNumberNextAvailable: {
    color: '#FF9800',
  },
  workoutIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
  },
  nextAvailableIndicator: {
    position: 'absolute',
    bottom: 2,
    left: 2,
  },
  selectedDateInfo: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.3)',
    marginTop: 10,
  },
  selectedDateText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    marginBottom: 12,
  },
  workoutList: {
    marginTop: 8,
  },
  workoutItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  workoutText: {
    color: '#fff',
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    marginLeft: 8,
  },
  noWorkoutsText: {
    color: '#aaa',
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    fontStyle: 'italic',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#1a1a2e',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxWidth: 400,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  input: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
  },
  notesInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  addWorkoutModalButton: {
    backgroundColor: '#667eea',
    borderRadius: 12,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  addWorkoutModalButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
  },
  // Removed extraBox styles
  calendarSection: {
    marginHorizontal: 16,
    marginBottom: 20,
  },
  calendarAddButton: {
    padding: 8,
  },
  calendarContainer: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(102,126,234,0.2)',
    marginHorizontal: 16,
    marginBottom: 20,
  },
  calendarMonthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  calendarNavButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.1)',
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 4,
  },
  calendarWeekdayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 10,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
    paddingBottom: 8,
  },
  calendarWeekdayText: {
    color: '#aaa',
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
    width: 40,
    textAlign: 'center',
  },
  calendarMonthTitle: {
    color: '#fff',
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0c0c0c',
    padding: 20,
  },
  errorText: {
    color: '#fff',
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    textAlign: 'center',
  },
  floatingShapeGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 16,
  },
});
