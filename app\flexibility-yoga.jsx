import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Image,
  SafeAreaView,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import SafeImage from '../components/SafeImage';
import { getImageByCategory } from '../constants/remoteImages';

const { width, height } = Dimensions.get('window');

const ImageSlider = ({ images }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef(null);

  const handleScroll = (event) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / width);
    setCurrentIndex(index);
  };

  return (
    <View style={styles.sliderContainer}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        {images.map((image, index) => (
          <View key={index} style={styles.slide}>
            <Image source={image} style={styles.slideImage} resizeMode="cover" />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.7)']}
              style={styles.slideOverlay}
            >
              <Text style={styles.slideTitle}>{image.title}</Text>
            </LinearGradient>
          </View>
        ))}
      </ScrollView>
      <View style={styles.pagination}>
        {images.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              { backgroundColor: index === currentIndex ? '#fff' : 'rgba(255,255,255,0.5)' }
            ]}
          />
        ))}
      </View>
    </View>
  );
};

export default function FlexibilityYoga() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  const [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  const yogaImages = [
    { uri: getImageByCategory('yoga'), title: 'Yoga Flow' },
    { uri: getImageByCategory('yoga'), title: 'Flexibility Training' },
    { uri: getImageByCategory('exercise'), title: 'Mindful Movement' },
    { uri: getImageByCategory('exercise'), title: 'Stretching Routine' },
  ];

  const yogaTips = [
    {
      title: 'Breathe Deeply',
      description: 'Focus on your breath to enhance flexibility and relaxation.',
      icon: 'leaf',
      color: '#4ECDC4'
    },
    {
      title: 'Listen to Your Body',
      description: 'Never force a stretch - go only as far as comfortable.',
      icon: 'heart',
      color: '#FF6B6B'
    },
    {
      title: 'Be Patient',
      description: 'Flexibility takes time to develop. Stay consistent.',
      icon: 'time',
      color: '#667eea'
    },
    {
      title: 'Warm Up First',
      description: 'Always warm up your muscles before stretching.',
      icon: 'thermometer',
      color: '#FFD166'
    }
  ];

  const yogaSessions = [
    {
      title: 'Morning Yoga Flow',
      duration: '30 min',
      difficulty: 'All Levels',
      focus: 'Flexibility',
      poses: ['Sun Salutation', 'Downward Dog', 'Warrior Poses', 'Child\'s Pose'],
      image: { uri: getImageByCategory('yoga') }
    },
    {
      title: 'Deep Stretching',
      duration: '45 min',
      difficulty: 'Intermediate',
      focus: 'Recovery',
      poses: ['Pigeon Pose', 'Forward Folds', 'Hip Openers', 'Twists'],
      image: { uri: getImageByCategory('strength') }
    },
    {
      title: 'Mindful Movement',
      duration: '20 min',
      difficulty: 'Beginner',
      focus: 'Relaxation',
      poses: ['Cat-Cow', 'Gentle Twists', 'Legs Up Wall', 'Savasana'],
      image: { uri: getImageByCategory('exercise') }
    }
  ];

  if (!fontsLoaded) return null;

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#96CEB4', '#FFEAA7']}
        style={StyleSheet.absoluteFill}
      />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Flexibility & Yoga</Text>
        <TouchableOpacity style={styles.shareButton}>
          <Ionicons name="share-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Image Slider */}
      <ImageSlider images={yogaImages} />

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {['overview', 'sessions', 'tips', 'videos'].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[styles.tab, activeTab === tab && styles.activeTab]}
            onPress={() => setActiveTab(tab)}
          >
            <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'overview' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Find Your Inner Peace</Text>
            <Text style={styles.description}>
              Yoga and flexibility training improve mobility, reduce stress, and enhance mind-body connection. 
              Regular practice increases range of motion and promotes overall well-being.
            </Text>
            
            <View style={styles.benefitsContainer}>
              <View style={styles.benefitCard}>
                <Ionicons name="leaf" size={24} color="#4ECDC4" />
                <Text style={styles.benefitTitle}>Flexibility</Text>
                <Text style={styles.benefitText}>Improve range of motion</Text>
              </View>
              <View style={styles.benefitCard}>
                <Ionicons name="moon" size={24} color="#667eea" />
                <Text style={styles.benefitTitle}>Stress Relief</Text>
                <Text style={styles.benefitText}>Reduce anxiety and tension</Text>
              </View>
              <View style={styles.benefitCard}>
                <Ionicons name="body" size={24} color="#FF6B6B" />
                <Text style={styles.benefitTitle}>Posture</Text>
                <Text style={styles.benefitText}>Improve alignment and balance</Text>
              </View>
            </View>
          </View>
        )}

        {activeTab === 'sessions' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Yoga Sessions</Text>
            {yogaSessions.map((session, index) => (
              <View key={index} style={styles.sessionCard}>
                <Image source={session.image} style={styles.sessionImage} />
                <View style={styles.sessionInfo}>
                  <Text style={styles.sessionTitle}>{session.title}</Text>
                  <View style={styles.sessionMeta}>
                    <View style={styles.metaItem}>
                      <Ionicons name="time-outline" size={16} color="#4ECDC4" />
                      <Text style={styles.metaText}>{session.duration}</Text>
                    </View>
                    <View style={styles.metaItem}>
                      <Ionicons name="fitness-outline" size={16} color="#FF6B6B" />
                      <Text style={styles.metaText}>{session.difficulty}</Text>
                    </View>
                    <View style={styles.metaItem}>
                      <Ionicons name="flag-outline" size={16} color="#FFD166" />
                      <Text style={styles.metaText}>{session.focus}</Text>
                    </View>
                  </View>
                  <View style={styles.posesList}>
                    {session.poses.map((pose, idx) => (
                      <View key={idx} style={styles.poseItem}>
                        <Ionicons name="checkmark-circle" size={16} color="#4ECDC4" />
                        <Text style={styles.poseText}>{pose}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              </View>
            ))}
          </View>
        )}

        {activeTab === 'tips' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Yoga & Flexibility Tips</Text>
            {yogaTips.map((tip, index) => (
              <View key={index} style={styles.tipCard}>
                <View style={[styles.tipIcon, { backgroundColor: tip.color }]}>
                  <Ionicons name={tip.icon} size={24} color="#fff" />
                </View>
                <View style={styles.tipContent}>
                  <Text style={styles.tipTitle}>{tip.title}</Text>
                  <Text style={styles.tipDescription}>{tip.description}</Text>
                </View>
              </View>
            ))}
          </View>
        )}

        {activeTab === 'videos' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Yoga & Flexibility Videos</Text>
            <View style={styles.videoContainer}>
              <View style={styles.videoCard}>
                <View style={styles.videoThumbnail}>
                  <Ionicons name="play-circle" size={48} color="#fff" />
                </View>
                <Text style={styles.videoTitle}>Morning Yoga Flow</Text>
                <Text style={styles.videoDuration}>25 min</Text>
              </View>
              <View style={styles.videoCard}>
                <View style={styles.videoThumbnail}>
                  <Ionicons name="play-circle" size={48} color="#fff" />
                </View>
                <Text style={styles.videoTitle}>Deep Stretching</Text>
                <Text style={styles.videoDuration}>30 min</Text>
              </View>
              <View style={styles.videoCard}>
                <View style={styles.videoThumbnail}>
                  <Ionicons name="play-circle" size={48} color="#fff" />
                </View>
                <Text style={styles.videoTitle}>Flexibility Training</Text>
                <Text style={styles.videoDuration}>20 min</Text>
              </View>
              <View style={styles.videoCard}>
                <View style={styles.videoThumbnail}>
                  <Ionicons name="play-circle" size={48} color="#fff" />
                </View>
                <Text style={styles.videoTitle}>Restorative Yoga</Text>
                <Text style={styles.videoDuration}>40 min</Text>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  shareButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  sliderContainer: {
    height: 250,
    position: 'relative',
  },
  slide: {
    width,
    height: 250,
    position: 'relative',
  },
  slideImage: {
    width: '100%',
    height: '100%',
  },
  slideOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
    justifyContent: 'flex-end',
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  slideTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  pagination: {
    position: 'absolute',
    bottom: 10,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,0.1)',
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255,255,255,0.7)',
  },
  activeTabText: {
    color: '#fff',
  },
  content: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: 20,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  description: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    lineHeight: 24,
    marginBottom: 20,
  },
  benefitsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  benefitCard: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    marginHorizontal: 5,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  benefitTitle: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginTop: 8,
    textAlign: 'center',
  },
  benefitText: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: 4,
  },
  sessionCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    marginBottom: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sessionImage: {
    width: '100%',
    height: 150,
  },
  sessionInfo: {
    padding: 20,
  },
  sessionTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_700Bold',
    color: '#2c3e50',
    marginBottom: 10,
  },
  sessionMeta: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
  },
  metaText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    marginLeft: 5,
  },
  posesList: {
    marginTop: 10,
  },
  poseItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  poseText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#2c3e50',
    marginLeft: 8,
  },
  tipCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    marginBottom: 5,
  },
  tipDescription: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
    lineHeight: 20,
  },
  videoContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  videoCard: {
    width: (width - 60) / 2,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  videoThumbnail: {
    width: 80,
    height: 80,
    backgroundColor: '#96CEB4',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  videoTitle: {
    fontSize: 14,
    fontFamily: 'Poppins_600SemiBold',
    color: '#2c3e50',
    textAlign: 'center',
    marginBottom: 5,
  },
  videoDuration: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#7f8c8d',
  },
}); 