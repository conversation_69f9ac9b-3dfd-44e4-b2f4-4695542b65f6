import React, { useRef, useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet, 
  Image, 
  Platform, 
  SafeAreaView, 
  Alert,
  ActivityIndicator,
  Dimensions,
  ScrollView
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring, 
  withTiming,
  withSequence,
  withDelay
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useUser } from '../../context/UserContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StatusBar } from 'expo-status-bar';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';
import {
  useFonts,
  Poppins_700Bold,
  Poppins_400Regular,
  Poppins_600SemiBold,
} from '@expo-google-fonts/poppins';

const { width, height } = Dimensions.get('window');

export default function Summary() {
  const { 
    fitnessLevel, 
    gender, 
    targetAreas, 
    activityLevel, 
    setUserHistory, 
    currentUser,
    updateCurrentUser,
    height,
    weight,
    targetWeight,
    name,
    email,
    age,
    targetHeight
  } = useUser();
  const router = useRouter();
  const userEmail = currentUser?.email;
  const [profile, setProfile] = useState({});
  const [loading, setLoading] = useState(true);
  const [displayFitnessLevel, setDisplayFitnessLevel] = useState('');

  // Animation values
  const fadeAnim = useSharedValue(0);
  const slideAnim = useSharedValue(50);
  const scaleAnim = useSharedValue(0.8);
  const buttonScale = useSharedValue(1);

  let [fontsLoaded] = useFonts({
    Poppins_700Bold,
    Poppins_400Regular,
    Poppins_600SemiBold,
  });

  useEffect(() => {
    // Start animations
    fadeAnim.value = withTiming(1, { duration: 1000 });
    slideAnim.value = withTiming(0, { duration: 800 });
    scaleAnim.value = withSpring(1, { friction: 8, tension: 40 });
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
      const data = await AsyncStorage.getItem(`@user_data:${currentUser?.email}`);
        console.log('Summary: Loading user data for:', currentUser?.email);
        console.log('Summary: Raw data from AsyncStorage:', data);
        
        if (data) {
          const parsedData = JSON.parse(data);
          setProfile(parsedData);
          console.log('Summary: Parsed user data:', parsedData);
          
          // Set fitness level from multiple sources with priority
          const fitnessLevelFromData = parsedData.fitnessLevel;
          const fitnessLevelFromContext = fitnessLevel;
          const finalFitnessLevel = fitnessLevelFromData || fitnessLevelFromContext || 'Not set';
          setDisplayFitnessLevel(finalFitnessLevel);
          console.log('Summary: Fitness level sources - Data:', fitnessLevelFromData, 'Context:', fitnessLevelFromContext, 'Final:', finalFitnessLevel);
        } else {
          console.log('Summary: No user data found in AsyncStorage');
        }
        
        // Also check profile data
        if (currentUser?.email) {
          const profileData = await AsyncStorage.getItem(`@user_profile:${currentUser.email}`);
          console.log('Summary: Profile data:', profileData);
          if (profileData) {
            const profile = JSON.parse(profileData);
            console.log('Summary: Parsed profile data:', profile);
            if (profile.fitnessLevel && !displayFitnessLevel) {
              setDisplayFitnessLevel(profile.fitnessLevel);
              console.log('Summary: Set fitness level from profile:', profile.fitnessLevel);
            }
          }
        }
        
        // Also check current user storage
        const currentUserData = await AsyncStorage.getItem('@FitnessApp:currentUser');
        console.log('Summary: Current user data:', currentUserData);
        if (currentUserData) {
          const currentUser = JSON.parse(currentUserData);
          console.log('Summary: Parsed current user data:', currentUser);
          if (currentUser.fitnessLevel && !displayFitnessLevel) {
            setDisplayFitnessLevel(currentUser.fitnessLevel);
            console.log('Summary: Set fitness level from current user:', currentUser.fitnessLevel);
          }
        }
        
      } catch (error) {
        console.error('Error loading profile data:', error);
      } finally {
      setLoading(false);
      }
    };
    fetchData();
  }, [currentUser, fitnessLevel]);

  const animatedStyles = useAnimatedStyle(() => ({
    opacity: fadeAnim.value,
    transform: [{ translateY: slideAnim.value }]
  }));

  const scaleStyles = useAnimatedStyle(() => ({
    transform: [{ scale: scaleAnim.value }]
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScale.value }]
  }));

  const handleComplete = async () => {
    try {
      // Animate button press
      buttonScale.value = withSequence(
        withTiming(0.95, { duration: 100 }),
        withTiming(1, { duration: 100 })
      );

      // Get current date and time
      const now = new Date();
      const loginDate = now.toLocaleDateString();
      const loginTime = now.toLocaleTimeString();
      
      // Create complete user profile data
      const completeProfileData = {
        name,
        email: currentUser?.email || email,
        fitnessLevel: displayFitnessLevel || fitnessLevel,
        gender,
        age,
        targetAreas,
        activityLevel,
        height,
        weight,
        targetWeight,
        targetHeight,
        isProfileComplete: true,
        completedAt: now.toISOString(),
        loginDate,
        loginTime,
        registrationDate: loginDate,
        registrationTime: loginTime
      };

      // Save to user-specific history
      const newHistoryEntry = {
        action: 'Profile Completed',
        userEmail: currentUser?.email || email,
        userName: name,
        planTitle: 'Profile Setup',
        timestamp: new Date().toISOString(),
        details: `Completed profile setup with ${displayFitnessLevel || fitnessLevel} fitness level, ${gender} gender, age ${age}, targeting ${targetAreas.join(', ')} areas`
      };
      
      // Save to user-specific AsyncStorage
      if (currentUser?.email || email) {
        const userEmail = currentUser?.email || email;
        const userHistoryKey = `@userHistory:${userEmail}`;
        const existingHistory = await AsyncStorage.getItem(userHistoryKey);
        const history = existingHistory ? JSON.parse(existingHistory) : [];
        await AsyncStorage.setItem(userHistoryKey, JSON.stringify([...history, newHistoryEntry]));
      }

      // Save complete profile data to user-specific storage
      const userEmail = currentUser?.email || email;
      // Save to user-specific profile
      const profileKey = `@user_profile:${userEmail}`;
      const profileData = await AsyncStorage.getItem(profileKey);
      const profile = profileData ? JSON.parse(profileData) : {};
      const updatedProfile = {
        ...profile,
        fitnessLevel: displayFitnessLevel || fitnessLevel,
        gender,
        targetAreas,
        activityLevel,
        measurements: {
          height,
          weight,
          targetWeight,
          targetHeight,
          age
        },
        isProfileComplete: true,
        completedAt: new Date().toISOString()
      };
      await AsyncStorage.setItem(profileKey, JSON.stringify(updatedProfile));
      console.log('Summary: Saved profile to @user_profile:', updatedProfile);
      // Save to user data
      const userDataKey = `@user_data:${userEmail}`;
      const userDataString = await AsyncStorage.getItem(userDataKey);
      const userData = userDataString ? JSON.parse(userDataString) : {};
      const updatedUserData = {
        ...userData,
        ...completeProfileData
      };
      await AsyncStorage.setItem(userDataKey, JSON.stringify(updatedUserData));
      console.log('Summary: Saved profile to @user_data:', updatedUserData);
      // Also update the current user data in shared storage
      await AsyncStorage.setItem('@FitnessApp:currentUser', JSON.stringify(completeProfileData));
      // Update user context - remove isNewUser flag and set profile as complete
      setUserHistory(prev => [...(prev || []), newHistoryEntry]);
      await updateCurrentUser({ 
        ...completeProfileData,
        isNewUser: false, // Remove the new user flag
        isProfileComplete: true
      });
      
      // Show success message and navigate
      Alert.alert(
        'Profile Complete! 🎉',
        'Your fitness profile has been successfully created. Welcome to My Fitness!',
        [
          {
            text: 'Get Started',
            onPress: () => {
              try {
                router.push('/homepage');
              } catch (navigationError) {
                console.error('Navigation error:', navigationError);
                // Fallback navigation
                router.push('/');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Summary: Error saving profile:', error);
      Alert.alert('Error', 'Failed to save profile. Please try again.');
    }
  };

  const handleBack = () => {
    router.push('/profile/activity-level');
  };

  // Add a derived value for age that checks context, then profile, then fallback
  const displayAge = age || (profile && profile.age) || 'Not set';

  function getMotivationalQuote() {
    const quotes = [
      "Every step counts. Keep moving forward!",
      "You are stronger than you think.",
      "Progress, not perfection.",
      "Small changes make a big difference.",
      "Stay consistent and results will follow.",
      "Your only limit is you.",
      "Believe in yourself and all that you are.",
      "Push yourself, because no one else is going to do it for you.",
      "Success starts with self-discipline.",
      "Dream big. Work hard. Stay focused.",
    ];
    const today = new Date().getDate();
    return quotes[today % quotes.length];
  }

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
          style={StyleSheet.absoluteFill}
        />
        <ActivityIndicator size="large" color="#667eea" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      <LinearGradient 
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']} 
        style={styles.background}
      >
        {/* Background Elements */}
        <View style={styles.circle1} />
        <View style={styles.circle2} />
        <View style={styles.circle3} />
        
        {/* Glow circles */}
        <View style={[styles.glowCircle, { top: '10%', right: '10%' }]} />
        <View style={[styles.glowCircle, { bottom: '20%', left: '5%' }]} />

        {/* Dynamic Greeting and Motivation */}
        <View style={{ alignItems: 'center', marginTop: 30, marginBottom: 10 }}>
          <Text style={{ fontFamily: 'Poppins_700Bold', fontSize: 22, color: '#fff', textAlign: 'center' }}>
            Hello, {profile.name || name || 'User'}
          </Text>
          <Text style={{ fontFamily: 'Poppins_400Regular', fontSize: 15, color: 'rgba(255,255,255,0.7)', textAlign: 'center', marginTop: 4 }}>
            {getMotivationalQuote()}
          </Text>
          <TouchableOpacity
            style={{ marginTop: 12, backgroundColor: 'rgba(102,126,234,0.15)', borderRadius: 18, paddingHorizontal: 18, paddingVertical: 8 }}
            onPress={() => router.push('/profile/updateprofile')}
            activeOpacity={0.85}
          >
            <Text style={{ color: '#667eea', fontFamily: 'Poppins_600SemiBold', fontSize: 15 }}>Update Profile</Text>
          </TouchableOpacity>
        </View>

        {/* Back Button */}
        <TouchableOpacity 
          onPress={handleBack} 
          style={styles.backButton}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
            style={styles.backButtonGradient}
          >
            <Ionicons name="arrow-back" size={24} color="#ffffff" />
          </LinearGradient>
        </TouchableOpacity>

        {/* Main Content */}
        <Animated.ScrollView 
          style={[styles.scrollView, animatedStyles]}
          contentContainerStyle={styles.content}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <Animated.View style={[styles.header, scaleStyles]}>
            <View style={styles.imageWrapper}>
              <LinearGradient
                colors={['rgba(102, 126, 234, 0.3)', 'rgba(118, 75, 162, 0.2)']}
                style={styles.imageGradient}
              >
                <Ionicons name="checkmark-circle" size={60} color="#ffffff" />
              </LinearGradient>
            </View>
            <Text style={styles.title}>Profile Summary</Text>
            <Text style={styles.subtitle}>Your fitness profile is ready!</Text>
          </Animated.View>

          {/* Progress Indicator */}
          <Animated.View style={[styles.progressContainer, animatedStyles]}>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: '100%' }]} />
            </View>
            <Text style={styles.progressText}>Complete!</Text>
          </Animated.View>

          {/* Summary Section */}
          <Animated.View style={[styles.summarySection, animatedStyles]}>
            <Text style={styles.summaryTitle}>Your Profile Details</Text>
            
            <View style={styles.summaryItem}>
              <View style={styles.iconContainer}>
                <Ionicons name="star-outline" size={20} color="#667eea" />
              </View>
              <View style={styles.summaryContent}>
                <Text style={styles.summaryLabel}>Fitness Level</Text>
                <Text style={styles.summaryValue}>
                  {displayFitnessLevel || fitnessLevel || 'Not set'}
                  {!displayFitnessLevel && !fitnessLevel && (
                    <Text style={{ fontSize: 12, color: 'rgba(255,255,255,0.6)', fontStyle: 'italic' }}>
                      {' '}(Please go back and select your fitness level)
                    </Text>
                  )}
                </Text>
              </View>
            </View>

            <View style={styles.divider} />

            <View style={styles.summaryItem}>
              <View style={styles.iconContainer}>
                <Ionicons name="mail-outline" size={20} color="#667eea" />
              </View>
              <View style={styles.summaryContent}>
                <Text style={styles.summaryLabel}>Email</Text>
                <Text style={styles.summaryValue}>{userEmail || 'Not set'}</Text>
              </View>
            </View>

            <View style={styles.divider} />

            <View style={styles.summaryItem}>
              <View style={styles.iconContainer}>
                <Ionicons name="person-outline" size={20} color="#667eea" />
              </View>
              <View style={styles.summaryContent}>
                <Text style={styles.summaryLabel}>Name</Text>
                <Text style={styles.summaryValue}>{name || 'Not set'}</Text>
              </View>
            </View>

            <View style={styles.divider} />

            <View style={styles.summaryItem}>
              <View style={styles.iconContainer}>
                <Ionicons name="person-outline" size={20} color="#667eea" />
              </View>
              <View style={styles.summaryContent}>
                <Text style={styles.summaryLabel}>Gender</Text>
                <Text style={styles.summaryValue}>{gender || 'Not set'}</Text>
              </View>
            </View>

            <View style={styles.divider} />
            
            <View style={styles.summaryItem}>
              <View style={styles.iconContainer}>
                <Ionicons name="calendar-outline" size={20} color="#667eea" />
              </View>
              <View style={styles.summaryContent}>
                <Text style={styles.summaryLabel}>Age</Text>
                <Text style={styles.summaryValue}>{displayAge}</Text>
              </View>
            </View>

            <View style={styles.divider} />
            
            <View style={styles.summaryItem}>
              <View style={styles.iconContainer}>
                <Ionicons name="time-outline" size={20} color="#667eea" />
              </View>
              <View style={styles.summaryContent}>
                <Text style={styles.summaryLabel}>Registration Date</Text>
                <Text style={styles.summaryValue}>{profile.registrationDate || 'Not set'}</Text>
              </View>
            </View>

            <View style={styles.divider} />
            
            <View style={styles.summaryItem}>
              <View style={styles.iconContainer}>
                <Ionicons name="time-outline" size={20} color="#667eea" />
              </View>
              <View style={styles.summaryContent}>
                <Text style={styles.summaryLabel}>Registration Time</Text>
                <Text style={styles.summaryValue}>{profile.registrationTime || 'Not set'}</Text>
              </View>
            </View>

            <View style={styles.divider} />

            <View style={styles.summaryItem}>
              <View style={styles.iconContainer}>
                <Ionicons name="fitness-outline" size={20} color="#667eea" />
              </View>
              <View style={styles.summaryContent}>
                <Text style={styles.summaryLabel}>Target Areas</Text>
                <Text style={styles.summaryValue}>
                  {targetAreas && targetAreas.length > 0 
                    ? targetAreas.map(area => area.charAt(0).toUpperCase() + area.slice(1)).join(', ') 
                    : 'Not set'}
                </Text>
              </View>
            </View>

            <View style={styles.divider} />

            <View style={styles.summaryItem}>
              <View style={styles.iconContainer}>
                <Ionicons name="flash-outline" size={20} color="#667eea" />
              </View>
              <View style={styles.summaryContent}>
                <Text style={styles.summaryLabel}>Activity Level</Text>
                <Text style={styles.summaryValue}>{activityLevel || 'Not set'}</Text>
              </View>
            </View>

            {height && weight && (
              <>
                <View style={styles.divider} />
                <View style={styles.summaryItem}>
                  <View style={styles.iconContainer}>
                    <Ionicons name="body-outline" size={20} color="#667eea" />
                  </View>
                  <View style={styles.summaryContent}>
                    <Text style={styles.summaryLabel}>Measurements</Text>
                    <Text style={styles.summaryValue}>
                      {`Height: ${height} cm, Weight: ${weight} kg`}
                    </Text>
                    <Text style={styles.summaryValue}>
                      {`Target Height: ${targetHeight || 'Not set'} cm, Target Weight: ${targetWeight || 'Not set'} kg`}
                    </Text>
                  </View>
                </View>
              </>
            )}
          </Animated.View>

          {/* Welcome Message */}
          <Animated.View style={[styles.welcomeSection, animatedStyles]}>
            <Text style={styles.welcomeTitle}>Welcome to My Fitness!</Text>
            <Text style={styles.welcomeText}>
              Your personalized fitness journey is about to begin. We'll create custom workouts and track your progress to help you achieve your goals.
            </Text>
          </Animated.View>

          {/* Complete Button */}
          <Animated.View style={[styles.buttonContainer, buttonAnimatedStyle]}>
            <TouchableOpacity 
              onPress={handleComplete} 
              activeOpacity={0.8}
              style={styles.completeButton}
            >
              <LinearGradient 
                colors={['#667eea', '#764ba2']} 
                style={styles.buttonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={styles.buttonText}>
                  Complete Profile
                </Text>
                <Ionicons 
                  name="checkmark-circle" 
                  size={24} 
                  color="#ffffff" 
                  style={styles.buttonIcon}
                />
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>
        </Animated.ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0c0c0c',
  },
  background: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  circle1: {
    position: 'absolute',
    top: -100,
    right: -100,
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  circle2: {
    position: 'absolute',
    bottom: -50,
    left: -50,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  circle3: {
    position: 'absolute',
    top: '40%',
    right: -30,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  glowCircle: {
    position: 'absolute',
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    zIndex: 0,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 100 : 80,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  imageWrapper: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    overflow: 'hidden',
  },
  imageGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontFamily: 'Poppins_700Bold',
    fontSize: 28,
    color: '#ffffff',
    marginBottom: 5,
    textAlign: 'center',
  },
  subtitle: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  progressContainer: {
    marginBottom: 30,
    alignItems: 'center',
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
    width: '100%',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#667eea',
    borderRadius: 4,
  },
  progressText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  summarySection: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
  },
  summaryTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 18,
    color: '#ffffff',
    marginBottom: 20,
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  summaryContent: {
    flex: 1,
  },
  summaryLabel: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
    marginBottom: 2,
  },
  summaryValue: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#ffffff',
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginVertical: 12,
  },
  welcomeSection: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 30,
  },
  welcomeTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 18,
    color: '#ffffff',
    marginBottom: 10,
  },
  welcomeText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    lineHeight: 22,
  },
  buttonContainer: {
    marginBottom: 20,
  },
  completeButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  buttonText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#ffffff',
    marginRight: 8,
  },
  buttonIcon: {
    marginLeft: 4,
  },
  backButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 30,
    left: 20,
    zIndex: 10,
  },
  backButtonGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  particle: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderRadius: 10,
    zIndex: 1,
  },
  particleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  },
});