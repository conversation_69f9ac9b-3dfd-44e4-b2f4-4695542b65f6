import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  Switch,
  Dimensions,
  Animated,
  SafeAreaView,
  Platform,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Poppins_400Regular, Poppins_600SemiBold, Poppins_700Bold, useFonts } from '@expo-google-fonts/poppins';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from '../../utils/responsive';
import { StatusBar } from 'expo-status-bar';
import * as Notifications from 'expo-notifications';
// Temporarily disable Firebase imports
// import { getAllPushTokens } from '../../firebaseConfig';

// Mock Firebase function
const getAllPushTokens = () => Promise.resolve([]);

const { width, height } = Dimensions.get('window');

// Advanced Particle component
const AdvancedParticle = ({ style, delay = 0, speed = 1 }) => {
  const particleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.parallel([
          Animated.timing(particleAnim, {
            toValue: 1,
            duration: (4000 + Math.random() * 3000) * speed,
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, { 
            toValue: 1, 
            duration: 1500, 
            useNativeDriver: true 
          }),
          Animated.spring(scaleAnim, { 
            toValue: 1, 
            friction: 8, 
            tension: 40, 
            useNativeDriver: true 
          }),
        ]),
        Animated.parallel([
          Animated.timing(opacityAnim, { 
            toValue: 0, 
            duration: 1000, 
            useNativeDriver: true 
          }),
          Animated.timing(scaleAnim, { 
            toValue: 0, 
            duration: 1000, 
            useNativeDriver: true 
          }),
        ]),
      ])
    );
    
    Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 8000 + Math.random() * 4000,
        useNativeDriver: true,
      })
    ).start();
    
    setTimeout(() => animation.start(), delay);
  }, []);

  return (
    <Animated.View
      style={[
        style,
        {
          opacity: opacityAnim || 1,
          transform: [
            {
              translateY: (particleAnim && particleAnim.interpolate) ? 
                particleAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -height * 0.5],
                }) : 0,
            },
            {
              scale: scaleAnim || 1,
            },
            {
              rotate: (rotationAnim && rotationAnim.interpolate) ? 
                rotationAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg'],
                }) : '0deg',
            },
          ],
        },
      ]}
    >
      <LinearGradient
        colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
        style={styles.particleGradient}
      />
    </Animated.View>
  );
};

export default function RecentUsers() {
  const router = useRouter();
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editedUser, setEditedUser] = useState({});
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [newUser, setNewUser] = useState({ name: '', email: '', fitnessLevel: '', isActive: true });
  const [notifyModalVisible, setNotifyModalVisible] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [userList, setUserList] = useState([]);
  const [userSearch, setUserSearch] = useState('');
  const [groupedUsers, setGroupedUsers] = useState({ active: [], inactive: [] });

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const searchAnim = useRef(new Animated.Value(0)).current;
  const listItemAnims = useRef([]).current;

  let [fontsLoaded] = useFonts({
    Poppins_400Regular,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  useEffect(() => {
    fetchUsers();

    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.timing(searchAnim, {
        toValue: 1,
        duration: 1000,
        delay: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  useEffect(() => {
    filterUsers();
  }, [searchQuery, users]);

  useEffect(() => {
    if (notifyModalVisible) {
      (async () => {
        const allTokens = await getAllPushTokens();
        // Fetch user details from AsyncStorage for names/status
        const usersData = await AsyncStorage.getItem('@FitnessApp:users');
        const usersArr = usersData ? JSON.parse(usersData) : [];
        const usersWithStatus = Object.keys(allTokens).map(email => {
          const user = usersArr.find(u => u.email === email) || {};
          return { email, name: user.name || '', isActive: user.isActive !== false };
        });
        setUserList(usersWithStatus);
        setUserSearch('');
        setSelectedUsers([]);
        setGroupedUsers({
          active: usersWithStatus.filter(u => u.isActive),
          inactive: usersWithStatus.filter(u => !u.isActive)
        });
      })();
    }
  }, [notifyModalVisible]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      
      // Fetch users from the main users storage
      const usersData = await AsyncStorage.getItem('@FitnessApp:users');
      let parsedUsers = usersData ? JSON.parse(usersData) : [];
      
      // Also check for individual user profiles
      const allUserEmails = new Set();
      
      // Get all user emails from the main users list
      parsedUsers.forEach(user => {
        if (user.email) {
          allUserEmails.add(user.email);
        }
      });
      
      // Check for additional user profiles that might not be in the main list
      const allKeys = await AsyncStorage.getAllKeys();
      const profileKeys = allKeys.filter(key => key.startsWith('@user_profile:'));
      
      profileKeys.forEach(key => {
        const email = key.replace('@user_profile:', '');
        allUserEmails.add(email);
      });
      
      // Fetch complete user data for each email
      const usersWithProfiles = await Promise.all(
        Array.from(allUserEmails).map(async (email) => {
          try {
            // Get user profile data
            const profileKey = `@user_profile:${email}`;
            const profileData = await AsyncStorage.getItem(profileKey);
            const profile = profileData ? JSON.parse(profileData) : {};
            
            // Get user from main users list
            const mainUser = parsedUsers.find(u => u.email === email) || {};
            
            // Merge data with profile taking precedence
            const userData = {
              ...mainUser,
              ...profile,
              email: email,
              name: profile.name || mainUser.name || 'Unknown User',
              isActive: profile.isActive !== false, // Default to true if not specified
              signupTime: profile.signupTime || profile.registrationDate || mainUser.signupTime || new Date().toISOString(),
              fitnessLevel: profile.fitnessLevel || mainUser.fitnessLevel || 'Not specified',
              targetArea: profile.targetAreas ? profile.targetAreas.join(', ') : profile.targetArea || mainUser.targetArea || 'Not specified',
              activityLevel: profile.activityLevel || mainUser.activityLevel || 'Not specified',
              height: profile.height || profile.targetHeight || mainUser.height || 'Not specified',
              weight: profile.weight || profile.targetWeight || mainUser.weight || 'Not specified',
              age: profile.age || mainUser.age || 'Not specified',
              gender: profile.gender || mainUser.gender || 'Not specified',
              lastLogin: profile.lastLogin || mainUser.lastLogin || new Date().toISOString(),
            };
            
            return userData;
          } catch (error) {
            console.error(`Error loading profile for ${email}:`, error);
            return {
              email: email,
              name: 'Unknown User',
              isActive: true,
              signupTime: new Date().toISOString(),
            };
          }
        })
      );
      
      // Filter out any invalid users and sort by signup time (newest first)
      const validUsers = usersWithProfiles
        .filter(user => user.email)
        .sort((a, b) => new Date(b.signupTime) - new Date(a.signupTime));
      
      setUsers(validUsers);
      
      // Initialize animation values for list items
      listItemAnims.length = 0;
      validUsers.forEach(() => {
        listItemAnims.push(new Animated.Value(0));
      });
      
      console.log('Fetched users:', validUsers.length);
    } catch (error) {
      console.error('Error fetching users:', error);
      Alert.alert('Error', 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const filterUsers = () => {
    if (!searchQuery.trim()) {
      setFilteredUsers(users);
      return;
    }

    const filtered = users.filter(
      user =>
        user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredUsers(filtered);
  };

  const handleUserAction = (user, action) => {
    setSelectedUser(user);
    switch (action) {
      case 'view':
        setModalVisible(true);
        break;
      case 'edit':
        setEditedUser({ ...user });
        setEditModalVisible(true);
        break;
      case 'delete':
        handleDeleteUser(user);
        break;
    }
  };

  const handleDeleteUser = (user) => {
    Alert.alert(
      'Delete User',
      `Are you sure you want to delete ${user.name || user.email}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Use email as the unique identifier since it's guaranteed to be unique
              // If email is not available, fall back to ID
              const userIdentifier = user.email || user.id;
              
              if (!userIdentifier) {
                Alert.alert('Error', 'Cannot identify user to delete');
                return;
              }
              
              // Filter based on email first, then ID if needed
              const updatedUsers = users.filter(u => {
                if (user.email) {
                  return u.email !== user.email;
                } else {
                  return u.id !== user.id;
                }
              });
              
              // Save the updated users list
              await AsyncStorage.setItem('@FitnessApp:users', JSON.stringify(updatedUsers));
              
              // Update state
              setUsers(updatedUsers);
              
              // Also update filtered users to reflect the change immediately
              setFilteredUsers(prevFiltered => 
                prevFiltered.filter(u => {
                  if (user.email) {
                    return u.email !== user.email;
                  } else {
                    return u.id !== user.id;
                  }
                })
              );
              
              // Clean up user profile data
              if (user.email) {
                try {
                  await AsyncStorage.removeItem(`@user_profile:${user.email}`);
                } catch (profileError) {
                  console.error('Error removing user profile:', profileError);
                  // Continue with deletion even if profile removal fails
                }
              }
              
              Alert.alert('Success', 'User deleted successfully');
            } catch (error) {
              console.error('Error deleting user:', error);
              Alert.alert('Error', 'Failed to delete user');
            }
          }
        }
      ]
    );
  };

  const handleSaveUser = async () => {
    try {
      if (!editedUser.email) {
        Alert.alert('Error', 'Email is required');
        return;
      }

      // Check if email is being changed and if it would conflict with an existing user
      const originalUser = users.find(u => u.id === editedUser.id);
      if (originalUser && originalUser.email !== editedUser.email) {
        // Email is being changed, check for conflicts
        const emailExists = users.some(u => 
          u.id !== editedUser.id && u.email === editedUser.email
        );
        
        if (emailExists) {
          Alert.alert('Error', 'A user with this email already exists');
          return;
        }
      }

      // Update the user in the users array
      const updatedUsers = users.map(u =>
        u.id === editedUser.id ? { ...editedUser } : u
      );

      // Save the updated users list
      await AsyncStorage.setItem('@FitnessApp:users', JSON.stringify(updatedUsers));
      
      // If email was changed, update the user profile storage key
      if (originalUser && originalUser.email !== editedUser.email) {
        try {
          // Get the old profile
          const oldProfileData = await AsyncStorage.getItem(`@user_profile:${originalUser.email}`);
          if (oldProfileData) {
            // Save it under the new email
            await AsyncStorage.setItem(`@user_profile:${editedUser.email}`, oldProfileData);
            // Remove the old profile
            await AsyncStorage.removeItem(`@user_profile:${originalUser.email}`);
          }
        } catch (profileError) {
          console.error('Error updating user profile storage key:', profileError);
          // Continue with the update even if profile key update fails
        }
      }
      
      // Update the user profile data
      try {
        const profileKey = `@user_profile:${editedUser.email}`;
        const profileData = await AsyncStorage.getItem(profileKey);
        const profile = profileData ? JSON.parse(profileData) : {};
        
        // Merge relevant fields from editedUser into the profile
        const updatedProfile = {
          ...profile,
          name: editedUser.name,
          fitnessLevel: editedUser.fitnessLevel,
          isActive: editedUser.isActive,
          // Add other fields you want to update in the profile
        };
        
        await AsyncStorage.setItem(profileKey, JSON.stringify(updatedProfile));
      } catch (profileError) {
        console.error('Error updating user profile data:', profileError);
        // Continue with the update even if profile data update fails
      }
      
      // Update state
      setUsers(updatedUsers);
      setFilteredUsers(prevFiltered => 
        prevFiltered.map(u => u.id === editedUser.id ? { ...editedUser } : u)
      );
      
      setEditModalVisible(false);
      Alert.alert('Success', 'User updated successfully');
    } catch (error) {
      console.error('Error saving user:', error);
      Alert.alert('Error', 'Failed to update user');
    }
  };

  const handleAddUser = async () => {
    try {
      if (!newUser.email) {
        Alert.alert('Error', 'Email is required');
        return;
      }
      
      // Check if a user with this email already exists
      const emailExists = users.some(u => u.email === newUser.email);
      if (emailExists) {
        Alert.alert('Error', 'A user with this email already exists');
        return;
      }
      
      // Generate a unique ID and add signup time
      const id = Date.now();
      const signupTime = Date.now();
      const userToAdd = { 
        ...newUser, 
        id, 
        signupTime,
        lastLogin: signupTime // Set initial login time
      };
      
      // Add to users array
      const updatedUsers = [...users, userToAdd];
      
      // Save to AsyncStorage
      await AsyncStorage.setItem('@FitnessApp:users', JSON.stringify(updatedUsers));
      
      // Create a basic profile for the new user
      try {
        const profileKey = `@user_profile:${newUser.email}`;
        const newProfile = {
          name: newUser.name,
          email: newUser.email,
          fitnessLevel: newUser.fitnessLevel,
          isActive: newUser.isActive,
          createdAt: signupTime,
          measurements: {},
          preferences: {},
          // Add other default profile fields as needed
        };
        
        await AsyncStorage.setItem(profileKey, JSON.stringify(newProfile));
      } catch (profileError) {
        console.error('Error creating user profile:', profileError);
        // Continue with user creation even if profile creation fails
      }
      
      // Update state
      setUsers(updatedUsers);
      setFilteredUsers(prevFiltered => [...prevFiltered, userToAdd]);
      
      // Reset form and close modal
      setAddModalVisible(false);
      setNewUser({ name: '', email: '', fitnessLevel: '', isActive: true });
      
      Alert.alert('Success', 'User added successfully');
    } catch (error) {
      console.error('Error adding user:', error);
      Alert.alert('Error', 'Failed to add user');
    }
  };

  const handleClearAllUsers = () => {
    Alert.alert(
      'Clear All Users', 
      'Are you sure you want to delete all users? This action cannot be undone.', 
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All', 
          style: 'destructive', 
          onPress: async () => {
            try {
              // First, get all users to clean up their profiles
              const currentUsers = [...users];
              
              // Clear the main users list
              await AsyncStorage.setItem('@FitnessApp:users', JSON.stringify([]));
              
              // Clean up individual user profiles
              for (const user of currentUsers) {
                if (user.email) {
                  try {
                    await AsyncStorage.removeItem(`@user_profile:${user.email}`);
                  } catch (profileError) {
                    console.error(`Error removing profile for ${user.email}:`, profileError);
                    // Continue with deletion even if individual profile removal fails
                  }
                }
              }
              
              // Update state
              setUsers([]);
              setFilteredUsers([]);
              
              Alert.alert('Success', 'All users have been deleted');
            } catch (error) {
              console.error('Error clearing all users:', error);
              Alert.alert('Error', 'Failed to delete all users');
            }
          }
        }
      ]
    );
  };

  const handleSendNotification = async () => {
    if (!notificationMessage.trim()) {
      Alert.alert('Error', 'Notification message cannot be empty');
      return;
    }
    try {
      // Get all push tokens from Firebase
      const allTokens = await getAllPushTokens(); // { userEmail: { tokenId: token, ... }, ... }
      let notifiedCount = 0;
      for (const userEmail in allTokens) {
        const tokensObj = allTokens[userEmail];
        if (!tokensObj) continue;
        for (const tokenId in tokensObj) {
          const token = tokensObj[tokenId];
          if (token) {
            await fetch('https://exp.host/--/api/v2/push/send', {
              method: 'POST',
              headers: { 'Accept': 'application/json', 'Content-Type': 'application/json' },
              body: JSON.stringify({
                to: token,
                sound: 'default',
                title: 'Admin Notification',
                body: notificationMessage,
                data: { type: 'admin_broadcast' }
              })
            });
            notifiedCount++;
          }
        }
      }
      setNotifyModalVisible(false);
      setNotificationMessage('');
      Alert.alert('Notification Sent', `Notification sent to ${notifiedCount} devices.`);
    } catch (e) {
      Alert.alert('Error', 'Failed to send push notifications.');
      console.log('Push notification error:', e);
    }
  };

  const filteredGroupedUsers = {
    active: groupedUsers.active.filter(u =>
      u.email.toLowerCase().includes(userSearch.toLowerCase()) ||
      (u.name && u.name.toLowerCase().includes(userSearch.toLowerCase()))
    ),
    inactive: groupedUsers.inactive.filter(u =>
      u.email.toLowerCase().includes(userSearch.toLowerCase()) ||
      (u.name && u.name.toLowerCase().includes(userSearch.toLowerCase()))
    )
  };

  const renderUserItem = ({ item, index }) => {
    // Start animation for this list item if it hasn't been animated yet
    if (listItemAnims[index]?._value === 0) {
      Animated.timing(listItemAnims[index], {
        toValue: 1,
        duration: 500,
        delay: index * 100,
        useNativeDriver: true,
      }).start();
    }

    return (
      <Animated.View
        style={[
          styles.userItemContainer,
          {
            opacity: listItemAnims[index] || fadeAnim || 1,
            transform: [
              { 
                translateY: ((listItemAnims[index] || fadeAnim) && (listItemAnims[index] || fadeAnim).interpolate) ? 
                  (listItemAnims[index] || fadeAnim).interpolate({
                    inputRange: [0, 1],
                    outputRange: [50, 0]
                  }) : 0
              }
            ]
          }
        ]}
      >
        <TouchableOpacity
          style={styles.userItem}
          onPress={() => handleUserAction(item, 'view')}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']}
            style={styles.userItemGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.userAvatar}>
              <LinearGradient
                colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
                style={styles.avatarGradient}
              >
                <Text style={styles.avatarText}>
                  {item.name ? item.name.charAt(0).toUpperCase() : item.email.charAt(0).toUpperCase()}
                </Text>
              </LinearGradient>
            </View>
            
            <View style={styles.userInfo}>
              <Text style={styles.userName}>{item.name || 'No Name'}</Text>
              <Text style={styles.userEmail}>{item.email}</Text>
              <View style={styles.userMeta}>
                <Text style={styles.userDetails}>
                  {item.age && item.age !== 'Not specified' ? `${item.age} years` : ''}
                  {item.gender && item.gender !== 'Not specified' ? ` • ${item.gender}` : ''}
                  {item.fitnessLevel && item.fitnessLevel !== 'Not specified' ? ` • ${item.fitnessLevel}` : ''}
                </Text>
                <Text style={styles.userJoined}>
                  Joined: {new Date(item.signupTime || Date.now()).toLocaleDateString()}
                </Text>
                <View style={[
                  styles.userStatus, 
                  { backgroundColor: item.isActive ? '#4CAF50' : '#F44336' }
                ]} />
              </View>
            </View>
            
            <View style={styles.userActions}>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: 'rgba(102, 126, 234, 0.2)' }]}
                onPress={() => handleUserAction(item, 'edit')}
              >
                <Ionicons name="create-outline" size={16} color="#667eea" />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: 'rgba(244, 67, 54, 0.2)' }]}
                onPress={() => handleUserAction(item, 'delete')}
              >
                <Ionicons name="trash-outline" size={16} color="#F44336" />
              </TouchableOpacity>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderUserModal = () => (
    <Modal
      visible={modalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <LinearGradient
            colors={['#1a1a2e', '#16213e']}
            style={styles.modalContent}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>User Details</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            
            {selectedUser && (
              <View style={styles.userDetails}>
                <View style={styles.userAvatarLarge}>
                  <LinearGradient
                    colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
                    style={styles.avatarGradientLarge}
                  >
                    <Text style={styles.avatarTextLarge}>
                      {selectedUser.name 
                        ? selectedUser.name.charAt(0).toUpperCase() 
                        : selectedUser.email.charAt(0).toUpperCase()}
                    </Text>
                  </LinearGradient>
                </View>
                
                <Text style={styles.userNameLarge}>{selectedUser.name || 'No Name'}</Text>
                <Text style={styles.userEmailLarge}>{selectedUser.email}</Text>
                
                <View style={styles.detailsContainer}>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Status:</Text>
                    <Text style={[
                      styles.detailValue, 
                      { color: selectedUser.isActive ? '#4CAF50' : '#F44336' }
                    ]}>
                      {selectedUser.isActive ? 'Active' : 'Inactive'}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Joined:</Text>
                    <Text style={styles.detailValue}>
                      {new Date(selectedUser.signupTime || Date.now()).toLocaleDateString()}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Last Login:</Text>
                    <Text style={styles.detailValue}>
                      {selectedUser.lastLogin ? new Date(selectedUser.lastLogin).toLocaleDateString() : 'Never'}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Age:</Text>
                    <Text style={styles.detailValue}>
                      {selectedUser.age || 'Not specified'}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Gender:</Text>
                    <Text style={styles.detailValue}>
                      {selectedUser.gender || 'Not specified'}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Fitness Level:</Text>
                    <Text style={styles.detailValue}>
                      {selectedUser.fitnessLevel || 'Not specified'}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Target Area:</Text>
                    <Text style={styles.detailValue}>
                      {selectedUser.targetArea || 'Not specified'}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Activity Level:</Text>
                    <Text style={styles.detailValue}>
                      {selectedUser.activityLevel || 'Not specified'}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Height:</Text>
                    <Text style={styles.detailValue}>
                      {selectedUser.height ? `${selectedUser.height} cm` : 'Not specified'}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Weight:</Text>
                    <Text style={styles.detailValue}>
                      {selectedUser.weight ? `${selectedUser.weight} kg` : 'Not specified'}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={[styles.modalButton, styles.editButton]}
                    onPress={() => {
                      setModalVisible(false);
                      setEditedUser({ ...selectedUser });
                      setEditModalVisible(true);
                    }}
                  >
                    <LinearGradient
                      colors={['#667eea', '#764ba2']}
                      style={styles.buttonGradient}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                    >
                      <Text style={styles.buttonText}>Edit User</Text>
                    </LinearGradient>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.modalButton, styles.deleteButton]}
                    onPress={() => {
                      setModalVisible(false);
                      handleDeleteUser(selectedUser);
                    }}
                  >
                    <LinearGradient
                      colors={['#F44336', '#D32F2F']}
                      style={styles.buttonGradient}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                    >
                      <Text style={styles.buttonText}>Delete User</Text>
                    </LinearGradient>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </LinearGradient>
        </View>
      </View>
    </Modal>
  );

  const renderEditModal = () => (
    <Modal
      visible={editModalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setEditModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <LinearGradient
            colors={['#1a1a2e', '#16213e']}
            style={styles.modalContent}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Edit User</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setEditModalVisible(false)}
              >
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.editForm}>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Name</Text>
                <TextInput
                  style={styles.input}
                  value={editedUser.name || ''}
                  onChangeText={(text) => setEditedUser({ ...editedUser, name: text })}
                  placeholder="Enter name"
                  placeholderTextColor="rgba(255, 255, 255, 0.5)"
                />
              </View>
              
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Email</Text>
                <TextInput
                  style={styles.input}
                  value={editedUser.email || ''}
                  onChangeText={(text) => setEditedUser({ ...editedUser, email: text })}
                  placeholder="Enter email"
                  placeholderTextColor="rgba(255, 255, 255, 0.5)"
                  keyboardType="email-address"
                />
              </View>
              
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Fitness Level</Text>
                <TextInput
                  style={styles.input}
                  value={editedUser.fitnessLevel || ''}
                  onChangeText={(text) => setEditedUser({ ...editedUser, fitnessLevel: text })}
                  placeholder="Enter fitness level"
                  placeholderTextColor="rgba(255, 255, 255, 0.5)"
                />
              </View>
              
              <View style={styles.switchContainer}>
                <Text style={styles.switchLabel}>Active Status</Text>
                <Switch
                  value={editedUser.isActive || false}
                  onValueChange={(value) => setEditedUser({ ...editedUser, isActive: value })}
                  trackColor={{ false: '#767577', true: 'rgba(102, 126, 234, 0.4)' }}
                  thumbColor={editedUser.isActive ? '#667eea' : '#f4f3f4'}
                  ios_backgroundColor="#3e3e3e"
                />
              </View>
              
              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={[styles.modalButton, styles.saveButton]}
                  onPress={handleSaveUser}
                >
                  <LinearGradient
                    colors={['#667eea', '#764ba2']}
                    style={styles.buttonGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <Text style={styles.buttonText}>Save Changes</Text>
                  </LinearGradient>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => setEditModalVisible(false)}
                >
                  <LinearGradient
                    colors={['#3f3f3f', '#2a2a2a']}
                    style={styles.buttonGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <Text style={styles.buttonText}>Cancel</Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
        </View>
      </View>
    </Modal>
  );

  const renderAddModal = () => (
    <Modal
      visible={addModalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setAddModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <LinearGradient colors={['#1a1a2e', '#16213e']} style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add User</Text>
              <TouchableOpacity style={styles.closeButton} onPress={() => setAddModalVisible(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            <View style={styles.editForm}>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Name</Text>
                <TextInput style={styles.input} value={newUser.name} onChangeText={text => setNewUser({ ...newUser, name: text })} placeholder="Enter name" placeholderTextColor="rgba(255,255,255,0.5)" />
              </View>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Email</Text>
                <TextInput style={styles.input} value={newUser.email} onChangeText={text => setNewUser({ ...newUser, email: text })} placeholder="Enter email" placeholderTextColor="rgba(255,255,255,0.5)" keyboardType="email-address" />
              </View>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Fitness Level</Text>
                <TextInput style={styles.input} value={newUser.fitnessLevel} onChangeText={text => setNewUser({ ...newUser, fitnessLevel: text })} placeholder="Enter fitness level" placeholderTextColor="rgba(255,255,255,0.5)" />
              </View>
              <View style={styles.switchContainer}>
                <Text style={styles.switchLabel}>Active Status</Text>
                <Switch value={newUser.isActive} onValueChange={value => setNewUser({ ...newUser, isActive: value })} trackColor={{ false: '#767577', true: 'rgba(102, 126, 234, 0.4)' }} thumbColor={newUser.isActive ? '#667eea' : '#f4f3f4'} ios_backgroundColor="#3e3e3e" />
              </View>
              <View style={styles.modalActions}>
                <TouchableOpacity style={[styles.modalButton, styles.saveButton]} onPress={handleAddUser}>
                  <LinearGradient colors={['#667eea', '#764ba2']} style={styles.buttonGradient}>
                    <Text style={styles.buttonText}>Add User</Text>
                  </LinearGradient>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.modalButton, styles.cancelButton]} onPress={() => setAddModalVisible(false)}>
                  <LinearGradient colors={['#3f3f3f', '#2a2a2a']} style={styles.buttonGradient}>
                    <Text style={styles.buttonText}>Cancel</Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
        </View>
      </View>
    </Modal>
  );

  const renderNotifyModal = () => (
    <Modal
      visible={notifyModalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setNotifyModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <LinearGradient colors={['#1a1a2e', '#16213e']} style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Send Notification</Text>
              <TouchableOpacity style={styles.closeButton} onPress={() => setNotifyModalVisible(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            <View style={styles.editForm}>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Message</Text>
                <TextInput style={styles.input} value={notificationMessage} onChangeText={setNotificationMessage} placeholder="Enter notification message" placeholderTextColor="rgba(255,255,255,0.5)" multiline />
              </View>
              <View style={{marginVertical:10}}>
                <Text style={{color:'#fff', fontFamily:'Poppins_600SemiBold', fontSize:16, marginBottom:8}}>Select Recipients</Text>
                <TextInput
                  style={{backgroundColor:'rgba(255,255,255,0.08)', color:'#fff', borderRadius:8, padding:8, fontFamily:'Poppins_400Regular', marginBottom:8}}
                  placeholder="Search users..."
                  placeholderTextColor="rgba(255,255,255,0.5)"
                  value={userSearch}
                  onChangeText={setUserSearch}
                />
                <TouchableOpacity
                  style={{backgroundColor:selectedUsers.length===userList.length?'#667eea':'#444', borderRadius:8, padding:10, marginBottom:8}}
                  onPress={()=>setSelectedUsers(selectedUsers.length===userList.length?[]:userList.map(u=>u.email))}
                >
                  <Text style={{color:'#fff', fontFamily:'Poppins_400Regular'}}>
                    {selectedUsers.length===userList.length?'Deselect All':'Select All'}
                  </Text>
                </TouchableOpacity>
                <ScrollView style={{maxHeight:150}}>
                  {['active','inactive'].map(status=>(
                    filteredGroupedUsers[status].length>0 && (
                      <View key={status}>
                        <Text style={{color:'#FFD93D', fontFamily:'Poppins_600SemiBold', fontSize:15, marginTop:8}}>
                          {status==='active'?'Active Users':'Inactive Users'}
                        </Text>
                        {filteredGroupedUsers[status].map(({email,name})=>(
                          <TouchableOpacity
                            key={email}
                            style={{flexDirection:'row',alignItems:'center',marginBottom:6}}
                            onPress={()=>{
                              setSelectedUsers(selectedUsers.includes(email)
                                ? selectedUsers.filter(e=>e!==email)
                                : [...selectedUsers,email]);
                            }}
                          >
                            <Ionicons name={selectedUsers.includes(email)?'checkbox':'square-outline'} size={22} color="#667eea" style={{marginRight:8}} />
                            <Text style={{color:'#fff', fontFamily:'Poppins_400Regular'}}>{name?`${name} (${email})`:email}</Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    )
                  ))}
                </ScrollView>
              </View>
              <View style={styles.modalActions}>
                <TouchableOpacity style={[styles.modalButton, styles.saveButton]} onPress={handleSendNotification}>
                  <LinearGradient colors={['#FFD93D', '#FF6B6B']} style={styles.buttonGradient}>
                    <Text style={styles.buttonText}>Send</Text>
                  </LinearGradient>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.modalButton, styles.cancelButton]} onPress={() => setNotifyModalVisible(false)}>
                  <LinearGradient colors={['#3f3f3f', '#2a2a2a']} style={styles.buttonGradient}>
                    <Text style={styles.buttonText}>Cancel</Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
        </View>
      </View>
    </Modal>
  );

  if (!fontsLoaded) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
          style={StyleSheet.absoluteFill}
        />
        <ActivityIndicator size="large" color="#667eea" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      <LinearGradient
        colors={['#0c0c0c', '#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      >
        {/* Animated Background Elements */}
        <AdvancedParticle
          style={{ position: 'absolute', width: wp(5), height: wp(5), left: wp(10), top: hp(20) }}
          delay={0}
          speed={1.2}
        />
        <AdvancedParticle
          style={{ position: 'absolute', width: wp(5), height: wp(5), left: wp(80), top: hp(30) }}
          delay={500}
          speed={0.8}
        />
        <AdvancedParticle
          style={{ position: 'absolute', width: wp(5), height: wp(5), left: wp(20), top: hp(70) }}
          delay={1000}
          speed={1.5}
        />
        <View style={styles.circle1} />
        <View style={styles.circle2} />
        <View style={styles.circle3} />

        {/* Header */}
        <Animated.View 
          style={[
            styles.header,
            {
              opacity: fadeAnim || 1,
              transform: [{ translateY: slideAnim || 0 }]
            }
          ]}
        >
          {/* Top Row: Back Button and Icons */}
          <View style={styles.headerTopRow}>
            {/* Back Button */}
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
                style={styles.backButtonGradient}
              >
                <Ionicons name="arrow-back" size={20} color="#ffffff" />
              </LinearGradient>
            </TouchableOpacity>

            {/* Right Side Icons */}
            <View style={styles.headerRight}>
              {/* Refresh Icon */}
              <TouchableOpacity
                style={styles.headerIcon}
                onPress={fetchUsers}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={['rgba(102, 126, 234, 0.8)', 'rgba(118, 75, 162, 0.6)']}
                  style={styles.iconGradient}
                >
                  <Ionicons name="refresh" size={18} color="#ffffff" />
                </LinearGradient>
              </TouchableOpacity>

              {/* Add Profile Icon */}
              <TouchableOpacity 
                style={styles.headerIcon} 
                onPress={() => setAddModalVisible(true)}
                activeOpacity={0.8}
              >
                <LinearGradient 
                  colors={['#667eea', '#764ba2']} 
                  style={styles.iconGradient}
                >
                  <Ionicons name="person-add" size={18} color="#fff" />
                </LinearGradient>
              </TouchableOpacity>

              {/* Clear All Icon */}
              <TouchableOpacity 
                style={styles.headerIcon} 
                onPress={handleClearAllUsers}
                activeOpacity={0.8}
              >
                <LinearGradient 
                  colors={['#F44336', '#D32F2F']} 
                  style={styles.iconGradient}
                >
                  <Ionicons name="trash" size={18} color="#fff" />
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>

          {/* Bottom Row: User Management Text */}
          <View style={styles.headerBottomRow}>
            <Text style={styles.headerTitle}>User Management</Text>
          </View>
        </Animated.View>

        {/* Search Bar */}
        <Animated.View 
          style={[
            styles.searchContainer,
            {
              opacity: searchAnim || 1,
              transform: [
                { 
                  translateY: (searchAnim && searchAnim.interpolate) ? 
                    searchAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [20, 0]
                    }) : 0
                }
              ]
            }
          ]}
        >
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color="rgba(255, 255, 255, 0.5)" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search users..."
              placeholderTextColor="rgba(255, 255, 255, 0.5)"
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => setSearchQuery('')}
              >
                <Ionicons name="close-circle" size={20} color="rgba(255, 255, 255, 0.5)" />
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>

        {/* User List */}
        {loading ? (
          <View style={styles.loadingView}>
            <ActivityIndicator size="large" color="#667eea" />
          </View>
        ) : (
          <FlatList
            data={filteredUsers}
            renderItem={renderUserItem}
            keyExtractor={(item, index) =>
              (item.id ? item.id.toString() : (item.email ? item.email : index.toString()))
            }
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="people" size={64} color="rgba(255, 255, 255, 0.2)" />
                <Text style={styles.emptyText}>
                  {searchQuery.length > 0
                    ? 'No users match your search'
                    : 'No users found'}
                </Text>
              </View>
            }
          />
        )}

        {renderUserModal()}
        {renderEditModal()}
        {renderAddModal()}
        {renderNotifyModal()}
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0c0c0c',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0c0c0c',
  },
  background: {
    flex: 1,
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  particleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: wp(2.5),
  },
  circle1: {
    position: 'absolute',
    top: -wp(25),
    right: -wp(25),
    width: wp(50),
    height: wp(50),
    borderRadius: wp(25),
    backgroundColor: 'rgba(255, 255, 255, 0.03)',
  },
  circle2: {
    position: 'absolute',
    bottom: -wp(12.5),
    left: -wp(12.5),
    width: wp(37.5),
    height: wp(37.5),
    borderRadius: wp(18.75),
    backgroundColor: 'rgba(255, 255, 255, 0.02)',
  },
  circle3: {
    position: 'absolute',
    top: '40%',
    right: -wp(7.5),
    width: wp(20),
    height: wp(20),
    borderRadius: wp(10),
    backgroundColor: 'rgba(255, 255, 255, 0.015)',
  },
  header: {
    flexDirection: 'column',
    paddingHorizontal: wp(4),
    paddingVertical: hp(1.5),
    paddingTop: Platform.OS === 'ios' ? hp(6) : hp(4),
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: 'rgba(12, 12, 12, 0.95)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.15)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerTopRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: hp(1),
  },
  backButton: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    overflow: 'hidden',
  },
  backButtonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  headerIcon: {
    width: wp(9),
    height: wp(9),
    borderRadius: wp(4.5),
    overflow: 'hidden',
    marginLeft: wp(1.5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerBottomRow: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: hp(0.5),
  },
  headerTitle: {
    fontSize: wp(4.5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    textAlign: 'center',
  },
  searchContainer: {
    paddingHorizontal: wp(4),
    marginTop: Platform.OS === 'ios' ? hp(18) : hp(16),
    marginBottom: hp(2),
    paddingTop: hp(1),
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: wp(3),
    paddingHorizontal: wp(3),
    height: hp(5.5),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  searchIcon: {
    marginRight: wp(2),
  },
  searchInput: {
    flex: 1,
    color: '#fff',
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(3.2),
    height: hp(5.5),
  },
  clearButton: {
    padding: wp(1),
  },
  loadingView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: hp(10),
  },
  listContent: {
    paddingHorizontal: wp(4),
    paddingTop: hp(1),
    paddingBottom: hp(5),
  },
  userItemContainer: {
    marginBottom: hp(1.5),
  },
  userItem: {
    borderRadius: wp(4),
    overflow: 'hidden',
  },
  userItemGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: wp(4),
  },
  userAvatar: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    overflow: 'hidden',
    marginRight: wp(3),
  },
  avatarGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#fff',
    fontSize: wp(4),
    fontFamily: 'Poppins_600SemiBold',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
    marginBottom: hp(0.2),
  },
  userEmail: {
    fontSize: wp(2.8),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: hp(0.3),
  },
  userMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userDetails: {
    fontSize: wp(2.2),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.5)',
    marginRight: wp(2),
  },
  userJoined: {
    fontSize: wp(2.2),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.5)',
    marginRight: wp(2),
  },
  userStatus: {
    width: wp(2),
    height: wp(2),
    borderRadius: wp(1),
  },
  userActions: {
    flexDirection: 'row',
  },
  actionButton: {
    width: wp(7),
    height: wp(7),
    borderRadius: wp(3.5),
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: wp(1.5),
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: hp(10),
  },
  emptyText: {
    fontSize: wp(4),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.5)',
    marginTop: hp(2),
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: wp(4),
    overflow: 'hidden',
  },
  modalContent: {
    padding: wp(5),
    borderRadius: wp(4),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(3),
  },
  modalTitle: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
  },
  closeButton: {
    padding: wp(1),
  },
  userDetails: {
    alignItems: 'center',
  },
  userAvatarLarge: {
    width: wp(20),
    height: wp(20),
    borderRadius: wp(10),
    overflow: 'hidden',
    marginBottom: hp(2),
  },
  avatarGradientLarge: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarTextLarge: {
    color: '#fff',
    fontSize: wp(8),
    fontFamily: 'Poppins_600SemiBold',
  },
  userNameLarge: {
    fontSize: wp(5),
    fontFamily: 'Poppins_700Bold',
    color: '#fff',
    marginBottom: hp(0.5),
  },
  userEmailLarge: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: hp(3),
  },
  detailsContainer: {
    width: '100%',
    marginBottom: hp(3),
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: hp(1),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  detailLabel: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  detailValue: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_400Regular',
    color: '#fff',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    flex: 1,
    borderRadius: wp(4),
    overflow: 'hidden',
    marginHorizontal: wp(1),
  },
  buttonGradient: {
    paddingVertical: hp(1.5),
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: '#fff',
  },
  editButton: {
    marginRight: wp(1),
  },
  deleteButton: {
    marginLeft: wp(1),
  },
  saveButton: {
    marginRight: wp(1),
  },
  cancelButton: {
    marginLeft: wp(1),
  },
  editForm: {
    width: '100%',
  },
  inputContainer: {
    marginBottom: hp(2),
  },
  inputLabel: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: hp(0.5),
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: wp(3),
    paddingHorizontal: wp(4),
    paddingVertical: Platform.OS === 'ios' ? hp(1.5) : hp(1),
    color: '#fff',
    fontFamily: 'Poppins_400Regular',
    fontSize: wp(3.5),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.05)',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: hp(3),
  },
  switchLabel: {
    fontSize: wp(3.5),
    fontFamily: 'Poppins_600SemiBold',
    color: 'rgba(255, 255, 255, 0.7)',
  },
});