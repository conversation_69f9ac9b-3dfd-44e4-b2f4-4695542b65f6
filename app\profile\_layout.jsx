// /app/profile/_layout.jsx
import { Stack } from 'expo-router';

export default function ProfileLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="user-profile" />
      <Stack.Screen name="updateprofile" />
      <Stack.Screen name="summary" />
      <Stack.Screen name="gender" />
      <Stack.Screen name="target-area" />
      <Stack.Screen name="measurements" />
      <Stack.Screen name="fitness-level" />
      <Stack.Screen name="selectdetails" />
      <Stack.Screen name="selectdetails_new" />
      <Stack.Screen name="activity-level" />
    </Stack>
  );
}
